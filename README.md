# HotDub-Preprocess

Robust video preprocessing pipeline for audio-visual synchronization detection.

## Features

- Corruption detection and repair
- Automatic shot generation
- Single face filtering
- FPS/Hz resampling
- Audio denoising with enhanced stability
- Audio-Visual Synchronization Detection (ASD)

## Enhanced Stability Features

### Resource Management
- Automatic CUDA memory cleanup with retry mechanism
- Controlled batch processing for memory-intensive operations
- Progressive garbage collection
- FFmpeg process monitoring and timeout handling

### Process Safety
- File readiness verification between stages
- Automatic process cleanup on termination
- Graceful handling of GPU memory fragmentation
- Batch-based ASD processing

### Error Recovery
- Automatic cleanup of partial outputs
- Failed operation logging and reporting
- Temporary file management
- Process state monitoring

## Setup

1. Install CUDA Dependencies:
```bash
wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64/cuda-keyring_1.1-1_all.deb
sudo dpkg -i cuda-keyring_1.1-1_all.deb
sudo apt update
sudo apt -y install cudnn cudnn-cuda-12
sudo apt -y install libgl1 xorg
```
2. Clone the repo with submodules:
```bash
<NAME_EMAIL>:Hyathi/HotDub-Preprocess.git
cd HotDub-Preprocess
```
```bash
git submodule update --init --recursive
```

3. Install Python Dependencies:
```bash
uv sync
```

## Usage

Basic usage:
```bash
uv run preprocess.py --base-dir /path/to/data
```

Advanced options:
```bash
uv run preprocess.py \
  --base-dir /path/to/data \
  --workers 4 \
  --start-stage corruption \
  --end-stage asd
```

## Monitoring

The pipeline provides detailed progress information:
- Stage-by-stage progress logging
- Resource usage monitoring
- Error reporting and recovery status
- Process completion verification

## Troubleshooting

### Common Issues

1. GPU Memory Errors
   - Reduce batch size (default is 5)
   - Ensure no other GPU processes are running
   - Monitor with `nvidia-smi`

2. Process Freezes
   - Check system logs for CUDA errors
   - Verify FFmpeg installation
   - Monitor process status with `htop`

3. File Processing Errors
   - Check disk space
   - Verify file permissions
   - Review .ready flags in stages
