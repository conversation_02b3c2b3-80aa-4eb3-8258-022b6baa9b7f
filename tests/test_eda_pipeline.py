#!/usr/bin/env python3
"""
Test script for the EDA Pipeline implementation.

This script provides a simple test to verify that the EDA Pipeline
can be imported and initialized correctly.
"""

import sys
import logging

# Add the project root to the Python path
sys.path.insert(0, '/home/<USER>/HotDub-Preprocess')

def test_imports():
    """Test that all required modules can be imported."""
    try:
        from EDA_Pipeline import AffineMethodOne, AffineMethodTwo, EDAPipeline
        # Verify classes exist
        assert AffineMethodOne is not None
        assert AffineMethodTwo is not None
        assert EDAPipeline is not None
        print("✓ Successfully imported EDA Pipeline classes")
        return True
    except ImportError as e:
        print(f"✗ Failed to import EDA Pipeline classes: {e}")
        return False

def test_eda_pipeline_initialization():
    """Test that EDAPipeline class can be imported."""
    try:
        from EDA_Pipeline import EDAPipeline

        # Just test that the class exists
        assert EDAPipeline is not None
        print("✓ Successfully imported EDAPipeline class")
        return True
    except Exception as e:
        print(f"✗ Failed to import EDAPipeline: {e}")
        return False

def test_video_file_discovery():
    """Test that video file discovery method exists."""
    try:
        from EDA_Pipeline import EDAPipeline

        # Just test that the method exists
        assert hasattr(EDAPipeline, '_get_video_files')
        print("✓ EDAPipeline has _get_video_files method")
        return True
    except Exception as e:
        print(f"✗ Failed video file discovery test: {e}")
        return False

def test_affine_method_initialization():
    """Test that AffineMethod classes can be imported (initialization requires actual dependencies)."""
    try:
        from EDA_Pipeline import AffineMethodOne, AffineMethodTwo

        # Just test that the classes exist and can be referenced
        assert AffineMethodOne is not None
        assert AffineMethodTwo is not None

        print("✓ Successfully imported AffineMethod classes")
        return True
    except Exception as e:
        print(f"✗ Failed affine method import test: {e}")
        return False

def main():
    """Run all tests."""
    print("Running EDA Pipeline Tests")
    print("=" * 40)
    
    # Configure logging to reduce noise during testing
    logging.getLogger().setLevel(logging.WARNING)
    
    tests = [
        test_imports,
        test_eda_pipeline_initialization,
        test_video_file_discovery,
        test_affine_method_initialization,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The EDA Pipeline implementation looks good.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
