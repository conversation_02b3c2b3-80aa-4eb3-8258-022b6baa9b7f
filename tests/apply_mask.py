import cv2
import numpy as np
import os
import shutil
import subprocess
import argparse
import glob
import tempfile
import logging
from multiprocessing import Pool, cpu_count

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)

TARGET_RESOLUTION = (256, 256)


def load_and_preprocess_mask(mask_path, invert_alpha_logic=False):
    """
    Loads the mask image, resizes it to TARGET_RESOLUTION, and prepares it for blending.
    By default (invert_alpha_logic=False), black areas of the original mask will be opaque.
    If invert_alpha_logic=True, white areas of the original mask will be opaque.
    """
    mask_image = cv2.imread(mask_path)
    if mask_image is None:
        logging.error(f"Failed to load mask image from: {mask_path}")
        raise FileNotFoundError(f"Mask image not found at {mask_path}")

    if (
        mask_image.shape[0] != TARGET_RESOLUTION[1]
        or mask_image.shape[1] != TARGET_RESOLUTION[0]
    ):
        logging.warning(
            f"Mask image original resolution {mask_image.shape[1]}x{mask_image.shape[0]} "
            f"is not {TARGET_RESOLUTION[0]}x{TARGET_RESOLUTION[1]}. Resizing mask."
        )
        mask_resized_bgr = cv2.resize(
            mask_image, TARGET_RESOLUTION, interpolation=cv2.INTER_AREA
        )
    else:
        mask_resized_bgr = mask_image

    if len(mask_resized_bgr.shape) < 3 or mask_resized_bgr.shape[2] != 3:
        logging.error(
            f"Mask image at {mask_path} does not have 3 channels after loading and resizing. "
            f"Shape: {mask_resized_bgr.shape}"
        )
        # Attempt to convert to BGR if it's grayscale but loaded as such
        if len(mask_resized_bgr.shape) == 2:  # Grayscale
            mask_resized_bgr = cv2.cvtColor(mask_resized_bgr, cv2.COLOR_GRAY2BGR)
            logging.info(f"Converted grayscale mask to BGR.")
        elif mask_resized_bgr.shape[2] == 1:  # Grayscale with 1 channel
            mask_resized_bgr = cv2.cvtColor(mask_resized_bgr, cv2.COLOR_GRAY2BGR)
            logging.info(f"Converted single-channel grayscale mask to BGR.")
        elif mask_resized_bgr.shape[2] == 4:  # BGRA
            mask_resized_bgr = cv2.cvtColor(mask_resized_bgr, cv2.COLOR_BGRA2BGR)
            logging.info(f"Converted BGRA mask to BGR.")
        else:
            raise ValueError(
                "Mask image must be a 3-channel (BGR) image or convertible to it."
            )

    mask_gray = cv2.cvtColor(mask_resized_bgr, cv2.COLOR_BGR2GRAY)

    # Alpha Map Logic:
    if invert_alpha_logic:
        # White areas (255) of original mask become opaque (1.0), black (0) become transparent (0.0)
        alpha_map_base = mask_gray / 255.0
        logging.info("Mask behavior inverted: White areas of mask will be overlaid.")
    else:
        # Default: Black areas (0) of original mask become opaque (1.0), white (255) become transparent (0.0)
        alpha_map_base = (255.0 - mask_gray) / 255.0
        logging.info("Default mask behavior: Black areas of mask will be overlaid.")

    return {"resized_mask_bgr": mask_resized_bgr, "alpha_map_base": alpha_map_base}


def create_video_from_frames_ffmpeg(
    frames_dir, output_video_path, fps, frame_pattern="frame_%07d.png"
):
    """
    Uses ffmpeg to create a video from a directory of image frames.
    """
    ffmpeg_cmd = [
        "ffmpeg",
        "-y",  # Overwrite output files without asking
        "-framerate",
        str(fps),
        "-i",
        os.path.join(frames_dir, frame_pattern),
        "-c:v",
        "libx264",
        "-pix_fmt",
        "yuv420p",
        output_video_path,
    ]
    logging.info(f"Executing ffmpeg command: {' '.join(ffmpeg_cmd)}")
    try:
        process = subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True)
        logging.info(f"FFmpeg stdout: {process.stdout}")
        logging.info(f"FFmpeg stderr: {process.stderr}")
        logging.info(f"Successfully created video: {output_video_path}")
        return True
    except subprocess.CalledProcessError as e:
        logging.error(f"FFmpeg failed for {output_video_path}.")
        logging.error(f"FFmpeg command: {' '.join(e.cmd)}")
        logging.error(f"FFmpeg stdout: {e.stdout}")
        logging.error(f"FFmpeg stderr: {e.stderr}")
        return False
    except FileNotFoundError:
        logging.error(
            "FFmpeg command not found. Please ensure FFmpeg is installed and in your PATH."
        )
        return False


def process_single_video(
    video_path, mask_image_data, transparency, base_output_dir, fps_override=None
):
    """
    Processes a single video file by applying the mask.
    """
    logging.info(f"Processing video: {video_path}")
    video_filename = os.path.basename(video_path)
    video_name_no_ext = os.path.splitext(video_filename)[0]

    # Create a unique temporary directory for this video's frames
    # Suffix with video name for easier identification if cleanup fails
    temp_frames_dir_parent = os.path.join(base_output_dir, "temp_frames")
    os.makedirs(temp_frames_dir_parent, exist_ok=True)
    temp_frames_dir = tempfile.mkdtemp(
        prefix=f"{video_name_no_ext}_", dir=temp_frames_dir_parent
    )
    logging.info(f"Temporary frames directory: {temp_frames_dir}")

    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        logging.error(f"Failed to open video: {video_path}")
        shutil.rmtree(temp_frames_dir)
        return None

    original_fps = cap.get(cv2.CAP_PROP_FPS)
    output_fps = fps_override if fps_override is not None else original_fps
    if output_fps <= 0:
        logging.warning(
            f"Invalid FPS ({output_fps}) for video {video_path}. Defaulting to 25 FPS."
        )
        output_fps = 25.0

    frame_count = 0
    processed_successfully = True

    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            current_frame_resized = frame
            if (
                frame.shape[0] != TARGET_RESOLUTION[1]
                or frame.shape[1] != TARGET_RESOLUTION[0]
            ):
                logging.debug(  # Changed to debug to avoid flooding logs for every frame
                    f"Frame {frame_count} in {video_path} (res: {frame.shape[1]}x{frame.shape[0]}) "
                    f"is not {TARGET_RESOLUTION[0]}x{TARGET_RESOLUTION[1]}. Resizing."
                )
                current_frame_resized = cv2.resize(
                    frame, TARGET_RESOLUTION, interpolation=cv2.INTER_AREA
                )

            # Effective alpha based on transparency parameter
            # alpha_map_base is (256,256), transparency is scalar
            effective_alpha = mask_image_data["alpha_map_base"] * transparency
            # Expand for broadcasting: (256, 256) -> (256, 256, 1)
            alpha_for_blending = np.expand_dims(effective_alpha, axis=2)

            # Blend: Frame * (1 - alpha) + MaskContent * alpha
            # Mask content is mask_image_data["resized_mask_bgr"]
            blended_frame = (
                current_frame_resized * (1 - alpha_for_blending)
                + mask_image_data["resized_mask_bgr"] * alpha_for_blending
            )

            blended_frame = np.clip(blended_frame, 0, 255).astype(np.uint8)

            frame_filename = os.path.join(
                temp_frames_dir, f"frame_{frame_count:07d}.png"
            )
            cv2.imwrite(frame_filename, blended_frame)
            frame_count += 1

        if frame_count == 0:
            logging.warning(f"No frames were processed for video: {video_path}")
            processed_successfully = False

    except Exception as e:
        logging.error(f"Error processing frames for {video_path}: {e}")
        processed_successfully = False
    finally:
        cap.release()

    output_video_path = None
    if processed_successfully and frame_count > 0:
        output_video_filename = f"{video_name_no_ext}_masked.mp4"
        output_video_path = os.path.join(base_output_dir, output_video_filename)

        if create_video_from_frames_ffmpeg(
            temp_frames_dir, output_video_path, output_fps
        ):
            logging.info(f"Successfully processed and saved: {output_video_path}")
        else:
            logging.error(f"Failed to create video from frames for: {video_path}")
            output_video_path = None  # Indicate failure
    else:
        logging.warning(
            f"Skipping video creation for {video_path} due to processing errors or no frames."
        )

    # Clean up temporary directory
    try:
        shutil.rmtree(temp_frames_dir)
        logging.info(f"Cleaned up temporary directory: {temp_frames_dir}")
    except Exception as e:
        logging.error(f"Failed to clean up temporary directory {temp_frames_dir}: {e}")

    return output_video_path


def apply_mask_to_videos_orchestrator(
    source_input,
    mask_path,
    output_dir,
    transparency=0.5,
    num_workers=None,
    fps_override=None,
    invert_mask_behavior=False,
):
    """
    Main orchestrator function to apply mask to multiple videos.
    """
    video_file_paths_to_process = []
    if os.path.isdir(source_input):
        logging.info(f"Processing all videos in directory: {source_input}")
        for ext in (
            "*.mp4",
            "*.avi",
            "*.mov",
            "*.mkv",
        ):  # Add more extensions if needed
            video_file_paths_to_process.extend(
                glob.glob(os.path.join(source_input, ext))
            )
    elif isinstance(source_input, str) and "," in source_input:
        video_file_paths_to_process = [p.strip() for p in source_input.split(",")]
    elif isinstance(source_input, str) and os.path.isfile(source_input):
        video_file_paths_to_process = [source_input]
    elif isinstance(source_input, list):
        video_file_paths_to_process = source_input
    else:
        logging.error(
            f"Invalid source_input: {source_input}. Must be a directory, a single file, a list of files, or a comma-separated string of files."
        )
        return []

    if not video_file_paths_to_process:
        logging.warning(f"No video files found for source: {source_input}")
        return []

    logging.info(f"Found {len(video_file_paths_to_process)} video(s) to process.")

    os.makedirs(output_dir, exist_ok=True)

    try:
        mask_image_data = load_and_preprocess_mask(
            mask_path, invert_alpha_logic=invert_mask_behavior
        )
    except Exception as e:
        logging.error(f"Failed to load or preprocess mask: {e}. Aborting.")
        return []

    if num_workers is None:
        num_workers = cpu_count()
    num_workers = min(
        num_workers, len(video_file_paths_to_process)
    )  # Don't use more workers than videos
    logging.info(f"Using {num_workers} worker(s) for parallel processing.")

    # Prepare arguments for starmap
    tasks = []
    for video_path in video_file_paths_to_process:
        if not os.path.isfile(video_path):
            logging.warning(f"Video file not found: {video_path}. Skipping.")
            continue
        tasks.append(
            (video_path, mask_image_data, transparency, output_dir, fps_override)
        )

    if not tasks:
        logging.warning("No valid video tasks to process after filtering.")
        return []

    processed_video_paths = []
    if num_workers > 1 and len(tasks) > 1:
        with Pool(processes=num_workers) as pool:
            results = pool.starmap(process_single_video, tasks)
            for result in results:
                if result:
                    processed_video_paths.append(result)
    else:  # Process sequentially if only one worker or one task
        logging.info(
            "Processing videos sequentially (num_workers=1 or only one video)."
        )
        for task_args in tasks:
            result = process_single_video(*task_args)
            if result:
                processed_video_paths.append(result)

    logging.info("Orchestration complete.")
    if processed_video_paths:
        logging.info(f"Successfully processed videos: {processed_video_paths}")
    else:
        logging.warning("No videos were successfully processed.")

    return processed_video_paths


def main():
    parser = argparse.ArgumentParser(
        description="Apply a mask to video(s) with adjustable transparency."
    )
    parser.add_argument(
        "--input_source",
        "-i",
        type=str,
        required=True,
        help="Path to a single video file, a directory containing video files, or a comma-separated list of video file paths.",
    )
    parser.add_argument(
        "--output_dir",
        "-o",
        type=str,
        required=True,
        help="Directory where processed videos will be saved.",
    )
    parser.add_argument(
        "--mask_path",
        "-m",
        type=str,
        required=False,
        help="Path to the mask image (e.g., mask.png).",
        default="src/modules/hotdub/utils/mask.png",
    )
    parser.add_argument(
        "--transparency",
        "-t",
        type=float,
        default=0.5,
        help="Mask transparency (0.0 = fully transparent overlay, 1.0 = fully opaque overlay). Default is 0.5.",
    )
    parser.add_argument(
        "--num_workers",
        "-n",
        type=int,
        default=None,
        help="Number of videos to process in parallel. Defaults to the number of CPU cores.",
    )
    parser.add_argument(
        "--fps_override",
        "-f",
        type=float,
        default=None,
        help="If provided, sets the FPS for all output videos, overriding detected FPS from input videos.",
    )
    parser.add_argument(
        "--invert_mask_behavior",
        "-inv",
        action="store_true",
        help="Invert mask behavior: white areas of the mask become opaque, black transparent. Default is black opaque.",
    )
    parser.add_argument(
        "--verbose",
        "-v",
        action="store_true",
        help="Enable verbose (DEBUG level) logging.",
    )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logging.debug("Verbose logging enabled.")

    if not (0.0 <= args.transparency <= 1.0):
        parser.error("Transparency must be between 0.0 and 1.0.")

    apply_mask_to_videos_orchestrator(
        source_input=args.input_source,
        mask_path=args.mask_path,
        output_dir=args.output_dir,
        transparency=args.transparency,
        num_workers=args.num_workers,
        fps_override=args.fps_override,
        invert_mask_behavior=args.invert_mask_behavior,
    )


if __name__ == "__main__":
    main()
