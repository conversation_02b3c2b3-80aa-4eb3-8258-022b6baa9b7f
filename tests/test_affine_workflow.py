import os
import argparse
import logging
import glob
import cv2
import numpy as np
import torch  # Added as per plan, for potential type hints or tensor conversions
from omegaconf import OmegaConf, DictConfig  # Added DictConfig
from typing import List, Tuple, Optional, Dict, Any  # Added for type hinting

# Attempt to import project-specific modules
try:
    from src.modules.affine_transform.affine_processor import AffineProcessor
    from src.modules.affine_transform.infer_utils import InferenceUtils
    from src.utils.video_utils import write_video_ffmpeg
except ImportError as e:
    logging.error(
        f"Error importing project modules: {e}. Ensure PYTHONPATH is set correctly."
    )
    raise

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)


def load_and_preprocess_mask_image(
    mask_path: str, target_resolution_tuple: tuple, invert_alpha_logic: bool
):
    """
    Loads the mask image, resizes it to target_resolution_tuple, and prepares it for blending.
    """
    logging.debug(
        f"Loading mask from {mask_path}, target_res: {target_resolution_tuple}, invert: {invert_alpha_logic}"
    )
    mask_image = cv2.imread(mask_path)
    if mask_image is None:
        logging.error(f"Failed to load mask image from: {mask_path}")
        raise FileNotFoundError(f"Mask image not found at {mask_path}")

    # Resize mask to match the face crop dimensions
    if (
        mask_image.shape[0] != target_resolution_tuple[1]
        or mask_image.shape[1] != target_resolution_tuple[0]
    ):
        logging.debug(
            f"Mask image original resolution {mask_image.shape[1]}x{mask_image.shape[0]} "
            f"is not {target_resolution_tuple[0]}x{target_resolution_tuple[1]}. Resizing mask."
        )
        mask_resized_bgr = cv2.resize(
            mask_image, target_resolution_tuple, interpolation=cv2.INTER_AREA
        )
    else:
        mask_resized_bgr = mask_image

    # Ensure mask is 3-channel BGR
    if len(mask_resized_bgr.shape) < 3 or mask_resized_bgr.shape[2] != 3:
        logging.warning(
            f"Mask image at {mask_path} does not have 3 channels after loading/resizing. Shape: {mask_resized_bgr.shape}. Attempting conversion."
        )
        if len(mask_resized_bgr.shape) == 2:  # Grayscale
            mask_resized_bgr = cv2.cvtColor(mask_resized_bgr, cv2.COLOR_GRAY2BGR)
        elif mask_resized_bgr.shape[2] == 1:  # Grayscale with 1 channel
            mask_resized_bgr = cv2.cvtColor(mask_resized_bgr, cv2.COLOR_GRAY2BGR)
        elif mask_resized_bgr.shape[2] == 4:  # BGRA
            mask_resized_bgr = cv2.cvtColor(mask_resized_bgr, cv2.COLOR_BGRA2BGR)
        else:
            logging.error(
                "Mask image must be a 3-channel (BGR) image or convertible to it."
            )
            raise ValueError(
                "Mask image must be a 3-channel (BGR) image or convertible to it."
            )

    mask_gray = cv2.cvtColor(mask_resized_bgr, cv2.COLOR_BGR2GRAY)

    if invert_alpha_logic:
        alpha_map_base = mask_gray / 255.0
        logging.debug("Mask behavior inverted: White areas of mask will be overlaid.")
    else:
        alpha_map_base = (255.0 - mask_gray) / 255.0
        logging.debug("Default mask behavior: Black areas of mask will be overlaid.")

    return {"resized_mask_bgr": mask_resized_bgr, "alpha_map_base": alpha_map_base}


def apply_overlay_mask(frame_to_mask: np.ndarray, mask_data: dict, transparency: float):
    """
    Applies the mask to the frame_to_mask using the provided transparency.
    """
    logging.debug(
        f"Applying overlay mask. Initial frame_to_mask shape: {frame_to_mask.shape}, transparency: {transparency}"
    )

    # Ensure frame_to_mask is HWC
    # Typical CHW: (3, H, W), Typical HWC: (H, W, 3)
    if (
        frame_to_mask.ndim == 3
        and frame_to_mask.shape[0] == 3
        and frame_to_mask.shape[1] != 3
        and frame_to_mask.shape[2] != 3
    ):
        logging.debug(
            f"Transposing frame_to_mask from CHW {frame_to_mask.shape} to HWC."
        )
        frame_to_mask = np.transpose(frame_to_mask, (1, 2, 0))
        logging.debug(f"New frame_to_mask shape: {frame_to_mask.shape}")

    if frame_to_mask.shape[:2] != mask_data["alpha_map_base"].shape[:2]:
        logging.warning(
            f"Shape mismatch between frame_to_mask {frame_to_mask.shape[:2]} and alpha_map {mask_data['alpha_map_base'].shape[:2]}. Resizing alpha map."
        )
        # This case should ideally not happen if mask is resized to crop dimensions correctly
        # but as a fallback, resize alpha to match frame_to_mask's H, W
        resized_alpha_map_base = cv2.resize(
            mask_data["alpha_map_base"],
            (frame_to_mask.shape[1], frame_to_mask.shape[0]),
            interpolation=cv2.INTER_NEAREST,
        )
        effective_alpha = resized_alpha_map_base * transparency
    else:
        effective_alpha = mask_data["alpha_map_base"] * transparency

    alpha_for_blending = np.expand_dims(effective_alpha, axis=2)  # (H, W, 1)

    logging.info(
        f"apply_overlay_mask - Shapes before blending: frame_to_mask: {frame_to_mask.shape}, mask_data['resized_mask_bgr']: {mask_data['resized_mask_bgr'].shape}, alpha_for_blending: {alpha_for_blending.shape}"
    )
    logging.info(
        f"Before blending op: type(frame_to_mask): {type(frame_to_mask)}, type(mask_data['resized_mask_bgr']): {type(mask_data['resized_mask_bgr'])}, type(alpha_for_blending): {type(alpha_for_blending)}"
    )

    blended_frame = (
        frame_to_mask * (1 - alpha_for_blending)
        + mask_data["resized_mask_bgr"] * alpha_for_blending
    )

    logging.info(f"After blending op: type(blended_frame): {type(blended_frame)}")

    # Ensure blended_frame is a tensor for torch operations
    if not isinstance(blended_frame, torch.Tensor):
        # This case should ideally not be hit if frame_to_mask was a tensor,
        # but as a safeguard if all inputs to blending were numpy.
        logging.info(
            f"blended_frame is numpy, converting to tensor. Type: {type(blended_frame)}"
        )
        blended_frame = torch.from_numpy(
            blended_frame
        )  # device handling might be needed if not on cpu
        logging.info(
            f"After torch.from_numpy: type(blended_frame): {type(blended_frame)}"
        )

    # Perform operations in PyTorch
    blended_frame = torch.clamp(blended_frame, 0, 255)
    logging.info(
        f"After clamp, before .to(torch.uint8): type(blended_frame): {type(blended_frame)}, dtype: {blended_frame.dtype if isinstance(blended_frame, torch.Tensor) else 'N/A'}"
    )
    blended_frame = blended_frame.to(torch.uint8)

    # Convert to NumPy array for cv2.resize later
    blended_frame = blended_frame.cpu().numpy()
    return blended_frame


def process_video(
    video_file_path: str,
    args: argparse.Namespace,
    affine_processor: AffineProcessor,
    inference_utils: InferenceUtils,
):
    """
    Processes a single video file.
    """
    logging.info(f"Processing video: {video_file_path}")

    cap = cv2.VideoCapture(video_file_path)
    if not cap.isOpened():
        logging.error(f"Failed to open video: {video_file_path}")
        return

    original_fps = cap.get(cv2.CAP_PROP_FPS)
    # original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    # original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

    original_frames_list: List[np.ndarray] = []  # Added type hint
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        original_frames_list.append(frame)
    cap.release()

    if not original_frames_list:
        logging.warning(f"No frames read from video: {video_file_path}")
        return

    logging.info(f"Read {len(original_frames_list)} frames from {video_file_path}.")

    # Perform affine transformation on all frames
    # Assuming affine_processor.__call__ takes video_frames (list of np arrays),
    # return_boxes=True, and potentially output_resolution.
    # The output_resolution for the crop is determined by AffineProcessor's config.
    try:
        # The AffineProcessor might return a list of results per frame,
        # or a tuple of lists (all_crops, all_matrices, all_boxes)
        # Based on lipsync_pipeline.py, it's (faces, boxes, affine_matrices)
        # where 'faces' are crops, 'boxes' are bboxes, 'affine_matrices' are M1s.
        # Each element in these lists corresponds to a frame.
        # If a face is not detected, the corresponding elements might be None.
        logging.info(
            f"Applying affine transformation to {len(original_frames_list)} frames..."
        )
        # The `output_resolution` parameter for AffineProcessor is not explicitly in args,
        # as it's assumed to be part of its internal config.
        # The mask will be resized to the actual crop dimensions.

        # Type hint for results based on AffineProcessor's return when return_boxes=True
        # AffineProcessor.__call__ returns -> Tuple[List[Optional[np.ndarray]], List[Optional[np.ndarray]], List[Optional[List[float]]]] | None
        results: Optional[
            Tuple[
                List[Optional[np.ndarray]],
                List[Optional[np.ndarray]],
                List[Optional[List[float]]],
            ]
        ]
        results = affine_processor(
            video_frames=original_frames_list,
            return_boxes=True,  # type: ignore[arg-type]
        )

        if results is None:
            logging.error(
                f"Affine processor returned None for {video_file_path}. Skipping."
            )
            return

        # Unpack results carefully
        # Expected: (list_of_face_crops, list_of_matrices, list_of_bboxes)
        # AffineProcessor in src/modules/affine_transform/affine_processor.py returns (cropped_face_list, M_list, box_list)
        # M_list contains affine matrices (np.ndarray)
        # box_list contains bounding boxes (List[float])
        # cropped_face_list contains np.ndarray images or None
        all_transformed_face_crops: List[Optional[np.ndarray]]
        all_bounding_box_coords: List[Optional[List[float]]]  # Swapped type hint
        all_affine_matrices_M1: List[Optional[np.ndarray]]  # Swapped type hint

        all_transformed_face_crops, all_bounding_box_coords, all_affine_matrices_M1 = (
            results
        )

        if not (
            len(all_transformed_face_crops) == len(original_frames_list)
            and len(all_bounding_box_coords) == len(original_frames_list)
            and len(all_affine_matrices_M1) == len(original_frames_list)
        ):
            logging.error(
                f"Mismatch in lengths of affine processor outputs for {video_file_path}. Skipping."
            )
            # Log the actual lengths for debugging
            logging.debug(
                f"Lengths: crops={len(all_transformed_face_crops)}, boxes={len(all_bounding_box_coords)}, matrices={len(all_affine_matrices_M1)}, original={len(original_frames_list)}"
            )
            return

    except Exception as e:
        logging.exception(  # Use logging.exception to include stack trace
            f"Error during affine transformation for {video_file_path}: {e}"
        )
        return

    final_processed_frames: List[np.ndarray] = []  # Added type hint
    logging.info(
        "Processing frames: applying mask, inverse affine, and pasting back..."
    )

    for idx, original_frame in enumerate(original_frames_list):
        transformed_face_crop = all_transformed_face_crops[idx]
        affine_matrix_M1 = all_affine_matrices_M1[idx]
        bounding_box_coords = all_bounding_box_coords[idx]

        if (
            transformed_face_crop is None
            or affine_matrix_M1 is None
            or bounding_box_coords is None
        ):
            logging.warning(
                f"No face detected or missing data in frame {idx} of {video_file_path}. Using original frame."
            )
            final_processed_frames.append(original_frame.copy())
            continue

        try:
            logging.info(
                f"Frame {idx}: Initial transformed_face_crop shape: {transformed_face_crop.shape}"
            )
            # Ensure transformed_face_crop is HWC
            if transformed_face_crop.ndim == 3 and transformed_face_crop.shape[0] == 3:
                # Heuristic: if first dim is 3 and others are not, assume CHW
                if (
                    transformed_face_crop.shape[1] != 3
                    and transformed_face_crop.shape[2] != 3
                ):
                    logging.info(
                        f"Frame {idx}: Transposing transformed_face_crop from CHW {transformed_face_crop.shape} to HWC."
                    )
                    transformed_face_crop = np.transpose(
                        transformed_face_crop, (1, 2, 0)
                    )
                    logging.info(
                        f"Frame {idx}: New transformed_face_crop shape after transpose: {transformed_face_crop.shape}"
                    )

            # Determine crop dimensions for mask resizing
            crop_h, crop_w = transformed_face_crop.shape[:2]
            logging.info(
                f"Frame {idx}: Derived crop_h: {crop_h}, crop_w: {crop_w} for mask loading."
            )
            if crop_h == 0 or crop_w == 0:
                logging.warning(
                    f"Empty face crop at frame {idx} for {video_file_path} (h={crop_h}, w={crop_w}). Using original frame."
                )
                final_processed_frames.append(original_frame.copy())
                continue

            mask_data = load_and_preprocess_mask_image(
                args.mask_path, (crop_w, crop_h), args.invert_mask_behavior
            )

            masked_transformed_crop = apply_overlay_mask(
                transformed_face_crop, mask_data, args.transparency
            )

            logging.info(
                f"Frame {idx}: bounding_box_coords type: {type(bounding_box_coords)}, value: {bounding_box_coords}"
            )
            x1, y1, x2, y2 = bounding_box_coords
            original_face_width = int(x2 - x1)
            original_face_height = int(y2 - y1)

            if original_face_width <= 0 or original_face_height <= 0:
                logging.warning(
                    f"Invalid bounding box for frame {idx} of {video_file_path}: {bounding_box_coords}. Skipping paste-back."
                )
                final_processed_frames.append(original_frame.copy())
                continue

            resized_masked_crop = cv2.resize(
                masked_transformed_crop,
                (original_face_width, original_face_height),
                interpolation=cv2.INTER_LANCZOS4,
            )

            # Convert resized_masked_crop (NumPy HWC) to PyTorch CHW tensor for restore_img
            face_tensor_for_restore = torch.from_numpy(
                resized_masked_crop.copy()
            ).permute(2, 0, 1)
            face_tensor_for_restore = face_tensor_for_restore.to(
                device=inference_utils.device, dtype=inference_utils.dtype
            )

            current_affine_matrix_arg = affine_matrix_M1
            if torch.is_tensor(current_affine_matrix_arg):
                current_affine_matrix_arg = current_affine_matrix_arg.to(
                    device=inference_utils.device, dtype=inference_utils.dtype
                )
            # If it's a NumPy array, restore_img's internal logic will handle conversion and device placement.

            final_frame = inference_utils.restore_img(
                original_frame.copy(),
                face_tensor_for_restore,
                current_affine_matrix_arg,
                apply_blending=False,
            )
            final_processed_frames.append(final_frame)

        except Exception as e:
            logging.exception(f"Error processing frame {idx} of {video_file_path}: {e}")
            final_processed_frames.append(original_frame.copy())  # Fallback to original

    if final_processed_frames:
        base_name = os.path.splitext(os.path.basename(video_file_path))[0]
        output_video_name = (
            f"{base_name}_affine_test.mp4"  # Changed from _masked to _affine_test
        )
        output_video_path = os.path.join(args.output_dir, output_video_name)

        os.makedirs(args.output_dir, exist_ok=True)

        logging.info(f"Writing output video to: {output_video_path}")
        try:
            write_video_ffmpeg(
                output_video_path,
                np.array(final_processed_frames),
                fps=int(original_fps),  # Cast fps to int
            )
            logging.info(f"Successfully saved: {output_video_path}")
        except Exception as e:
            logging.error(f"Failed to write video {output_video_path}: {e}")
    else:
        logging.warning(f"No frames processed for video: {video_file_path}")

    # Cleanup for AffineProcessor if needed (per video)
    if hasattr(affine_processor, "reset_state"):
        logging.debug("Resetting AffineProcessor state.")
        affine_processor.reset_state()


def main():
    parser = argparse.ArgumentParser(
        description="Test script for affine transformation, masking, and inverse affine workflow."
    )
    parser.add_argument(
        "-i",
        "--input_path",
        type=str,
        required=True,
        help="Path to the input video file or a directory containing video files.",
    )
    parser.add_argument(
        "-o",
        "--output_dir",
        type=str,
        required=True,
        help="Directory where processed videos will be saved.",
    )
    parser.add_argument(
        "-m",
        "--mask_path",
        type=str,
        default="src/modules/hotdub/utils/mask.png",
        help="Path to the mask image.",
    )
    parser.add_argument(
        "-t",
        "--transparency",
        type=float,
        default=0.5,
        help="Mask transparency (0.0 = fully transparent, 1.0 = fully opaque).",
    )
    parser.add_argument(
        "-inv",
        "--invert_mask_behavior",
        action="store_true",
        help="Invert mask behavior: white areas of the mask become opaque.",
    )
    parser.add_argument(
        "-acp",
        "--affine_config_path",
        type=str,
        default="src/modules/affine_transform/config.yaml",
        help="Path to the config.yaml for AffineProcessor.",
    )
    parser.add_argument(
        "-dev",
        "--device",
        type=str,
        default="cpu",
        help='Device for AffineProcessor (e.g., "cpu", "cuda:0").',
    )
    parser.add_argument(
        "-tr",
        "--target_resolution",
        type=int,
        default=256,
        help="The target resolution (height and width) for the affine-transformed face crop, also used for InferenceUtils initialization.",
    )
    # Mask will be resized to the actual crop dimensions dynamically.

    args = parser.parse_args()

    if not (0.0 <= args.transparency <= 1.0):
        parser.error("Transparency must be between 0.0 and 1.0.")

    # Initialize AffineProcessor and InferenceUtils once
    try:
        loaded_affine_config = OmegaConf.load(args.affine_config_path)
        if not isinstance(loaded_affine_config, DictConfig):
            logging.error(
                f"Affine config at {args.affine_config_path} is not a DictConfig, but {type(loaded_affine_config)}."
            )
            raise TypeError("Affine config must be a DictConfig.")
        affine_config: DictConfig = loaded_affine_config

        # Ensure det_config path is resolved correctly if relative
        det_config_from_yaml: Optional[str] = None
        if hasattr(affine_config, "detector") and hasattr(
            affine_config.detector, "det_config"
        ):
            # Ensure the value is a string before proceeding
            if isinstance(affine_config.detector.det_config, str):
                det_config_from_yaml = affine_config.detector.det_config
            elif affine_config.detector.det_config is not None:
                logging.warning(
                    f"det_config in YAML is not a string: {affine_config.detector.det_config}. Will be treated as None for path resolution."
                )

        det_config_path_to_use: Optional[str] = None
        if det_config_from_yaml:
            if os.path.isabs(det_config_from_yaml):
                det_config_path_to_use = det_config_from_yaml
                logging.info(
                    f"Using absolute det_config path from YAML: {det_config_path_to_use}"
                )
            else:
                # Check relative to the main affine_config.yaml file's directory first
                config_file_dir = os.path.dirname(
                    os.path.abspath(args.affine_config_path)
                )
                path_relative_to_config = os.path.normpath(
                    os.path.join(config_file_dir, det_config_from_yaml)
                )

                # Then check relative to the current working directory (as a fallback)
                path_relative_to_cwd = os.path.normpath(
                    os.path.join(os.getcwd(), det_config_from_yaml)
                )

                if os.path.exists(path_relative_to_config):
                    det_config_path_to_use = path_relative_to_config
                    logging.info(
                        f"Resolved det_config path relative to main config: {det_config_path_to_use}"
                    )
                elif os.path.exists(path_relative_to_cwd):
                    det_config_path_to_use = path_relative_to_cwd
                    logging.info(
                        f"Resolved det_config path relative to CWD: {det_config_path_to_use}"
                    )
                else:
                    # If neither resolved path exists, pass the original string from YAML.
                    # AffineProcessor might have its own internal way to find it (e.g., if it's a known key).
                    logging.warning(
                        f"Could not find det_config file at '{path_relative_to_config}' (relative to main config) "
                        f"or at '{path_relative_to_cwd}' (relative to CWD). "
                        f"Passing original value '{det_config_from_yaml}' to AffineProcessor."
                    )
                    det_config_path_to_use = det_config_from_yaml

        use_onnx_flag: bool = (
            affine_config.detector.get("use_onnx", False)
            if hasattr(affine_config, "detector")
            else False
        )

        affine_processor_instance = AffineProcessor(
            config=affine_config,
            det_config=det_config_path_to_use,  # type: ignore[arg-type] # Path string is acceptable
            use_onnx=use_onnx_flag,
            device=args.device,
            debug_mode=False,
        )

        inference_utils_instance = InferenceUtils(
            resolution=args.target_resolution,  # Explicitly pass args
            mask_path=args.mask_path,  # Explicitly pass args
        )
    except Exception as e:
        logging.exception(
            f"Failed to initialize AffineProcessor or InferenceUtils: {e}"
        )
        return

    video_file_paths_to_process = []
    if os.path.isdir(args.input_path):
        logging.info(f"Processing all videos in directory: {args.input_path}")
        for ext in ("*.mp4", "*.avi", "*.mov", "*.mkv"):  # Common video extensions
            video_file_paths_to_process.extend(
                glob.glob(os.path.join(args.input_path, ext))
            )
            video_file_paths_to_process.extend(
                glob.glob(os.path.join(args.input_path, ext.upper()))
            )  # Also check uppercase
    elif os.path.isfile(args.input_path):
        video_file_paths_to_process = [args.input_path]
    else:
        logging.error(
            f"Invalid input_path: {args.input_path}. Must be a directory or a file."
        )
        return

    if not video_file_paths_to_process:
        logging.warning(f"No video files found for source: {args.input_path}")
        return

    logging.info(f"Found {len(video_file_paths_to_process)} video(s) to process.")

    for video_path in video_file_paths_to_process:
        process_video(
            video_path, args, affine_processor_instance, inference_utils_instance
        )

    logging.info("All processing finished.")


if __name__ == "__main__":
    main()
