#!/usr/bin/env python3

import os
import subprocess
import json
import matplotlib.pyplot as plt
import pandas as pd
import streamlit as st
from typing import Dict, List, Optional, Tuple


def get_video_metadata(filepath: str) -> Optional[Dict]:
    """
    Extract metadata from video file using ffprobe.
    Returns None if extraction fails.
    """
    try:
        # Run ffprobe command to get stream information in JSON format
        cmd = [
            "ffprobe",
            "-v",
            "quiet",
            "-print_format",
            "json",
            "-show_streams",
            filepath,
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode != 0:
            st.error(f"Error processing {filepath}: ffprobe command failed")
            return None

        data = json.loads(result.stdout)

        # Initialize metadata dictionary
        metadata = {
            "width": None,
            "height": None,
            "fps": None,
            "audio_sampling_rate": None,
            "duration": None,
        }

        # Extract information from streams
        for stream in data.get("streams", []):
            if stream["codec_type"] == "video":
                metadata["width"] = int(stream.get("width", 0))
                metadata["height"] = int(stream.get("height", 0))
                # Try to get FPS from avg_frame_rate
                fps_str = stream.get("avg_frame_rate", "0/1")
                try:
                    num, den = map(int, fps_str.split("/"))
                    metadata["fps"] = num / den if den != 0 else 0
                except (ValueError, ZeroDivisionError):
                    metadata["fps"] = 0

                # Extract duration from video stream
                try:
                    metadata["duration"] = float(stream.get("duration", 0))
                except (ValueError, TypeError):
                    metadata["duration"] = 0

            elif stream["codec_type"] == "audio":
                metadata["audio_sampling_rate"] = int(stream.get("sample_rate", 0))

        return metadata

    except Exception as e:
        st.error(f"Error processing {filepath}: {str(e)}")
        return None


def create_distribution_plot(
    data: List, title: str, xlabel: str
) -> Tuple[plt.Figure, plt.Axes]:
    """
    Create a histogram plot for the given data.
    Returns the figure and axes objects.
    """
    fig, ax = plt.subplots(figsize=(10, 6))
    ax.hist(data, bins=30, edgecolor="black")
    ax.set_title(title)
    ax.set_xlabel(xlabel)
    ax.set_ylabel("Count")
    ax.grid(True, alpha=0.3)

    # Use plain number format for audio sampling rate plot
    if "Audio Sampling Rate" in title:
        ax.xaxis.set_major_formatter(plt.ScalarFormatter(useOffset=False))
        ax.ticklabel_format(style="plain", axis="x")

    return fig, ax


# Set page title
st.title("Video Metadata Analysis")

# Get video directory from user
video_dir = st.text_input("Enter the path to video directory:", value="./FINAL/")

# Add analyze button
if st.button("Analyze Videos"):
    # Verify directory exists
    if not os.path.isdir(video_dir):
        st.error(f"Error: Directory '{video_dir}' does not exist")
    else:
        # Get list of MP4 files
        video_files = [
            os.path.join(video_dir, f)
            for f in os.listdir(video_dir)
            if f.endswith(".mp4")
        ]

        total_files = len(video_files)
        if total_files == 0:
            st.error(f"No MP4 files found in directory: {video_dir}")
        else:
            st.info(f"Found {total_files} MP4 files")

            # Create progress bar
            progress_bar = st.progress(0)

            # Extract metadata from all videos
            metadata_list = []
            for i, video_file in enumerate(video_files):
                metadata = get_video_metadata(video_file)
                if metadata:
                    metadata_list.append(metadata)
                # Update progress bar
                progress_bar.progress((i + 1) / total_files)

            st.success(
                f"Successfully extracted metadata from {len(metadata_list)} files"
            )

            # Convert to pandas DataFrame for easier analysis
            df = pd.DataFrame(metadata_list)

            # Display basic statistics
            st.subheader("Summary Statistics")
            st.write(df.describe())

            # Create and display distribution plots
            st.subheader("Distribution Plots")

            # Resolution distribution
            resolutions = df["width"] * df["height"]
            resolutions = resolutions[resolutions > 0]  # Remove any zero values
            fig1, _ = create_distribution_plot(
                resolutions, "Video Resolution Distribution", "Resolution (pixels)"
            )
            st.pyplot(fig1)

            # FPS distribution
            fps_data = df["fps"][df["fps"] > 0]  # Remove any zero values
            fig2, _ = create_distribution_plot(
                fps_data, "FPS Distribution", "Frames Per Second"
            )
            st.pyplot(fig2)

            # Audio sampling rate distribution
            audio_rates = df["audio_sampling_rate"][df["audio_sampling_rate"] > 0]
            fig3, _ = create_distribution_plot(
                audio_rates, "Audio Sampling Rate Distribution", "Sampling Rate (Hz)"
            )
            st.pyplot(fig3)

            # Duration distribution
            durations = df["duration"][df["duration"] > 0]
            fig4, _ = create_distribution_plot(
                durations, "Video Duration Distribution", "Duration (seconds)"
            )
            st.pyplot(fig4)

            st.success("Analysis complete!")
