[project]
name = "hotdub-preprocess"
version = "0.1.0"
requires-python = "==3.12.9"
dependencies = [
    "accelerate>=1.7.0",
    "audio-separator>=0.31.0",
    "click>=8.1.8",
    "decord>=0.6.0",
    "diffusers>=0.32.2",
    "dotenv>=0.9.9",
    "einops>=0.8.1",
    "face-alignment>=1.4.1",
    "ffmpeg-python>=0.2.0",
    "gdown>=5.2.0",
    "imageio>=2.37.0",
    "insightface>=0.7.3",
    "ipykernel>=6.29.5",
    "kornia>=0.8.1",
    "llvmlite>=0.44.0",
    "mediapipe>=0.10.14",
    "minio>=7.2.15",
    "omegaconf>=2.3.0",
    "onnxruntime-gpu>=1.21.1",
    "opencv-python>=*********",
    "pandas>=2.2.3",
    "pyannote-audio>=3.3.2",
    "python-speech-features>=0.6",
    "requests>=2.32.3",
    "rich>=14.0.0",
    "scenedetect>=0.6.6",
    "threeddfa",
    "torch>=2.6.0",
    "torchaudio>=2.6.0",
    "torchvision>=0.21.0",
    "tqdm>=4.67.1",
    "transformers>=4.51.3",
    "wandb>=0.19.11",
]

[project.scripts]
s3manager = "src.s3manager.cli:main_cli_group"

[[tool.uv.index]]
name = "pytorch-cu124"
url = "https://download.pytorch.org/whl/cu124"
explicit = true

[tool.uv.sources]
torch = [
  { index = "pytorch-cu124", marker = "sys_platform == 'linux' or sys_platform == 'win32'" },
]
torchvision = [
  { index = "pytorch-cu124", marker = "sys_platform == 'linux' or sys_platform == 'win32'" },
]
threeddfa = { path = "src/models/threeddfa" }
