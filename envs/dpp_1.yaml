name: dpp
channels:
  - defaults
  - conda-forge
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - asttokens=3.0.0=pyhd8ed1ab_1
  - backcall=0.2.0=pyh9f0ad1d_0
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.2.25=h06a4308_0
  - comm=0.2.2=pyhd8ed1ab_1
  - debugpy=1.8.11=py312h6a678d5_0
  - executing=2.1.0=pyhd8ed1ab_1
  - expat=2.6.4=h6a678d5_0
  - ffmpeg=4.3.2=hca11adc_0
  - freetype=2.10.4=h0708190_1
  - gmp=6.2.1=h58526e2_0
  - gnutls=3.6.13=h85f3911_1
  - importlib-metadata=8.6.1=pyha770c72_0
  - ipykernel=6.29.5=pyh3099207_0
  - ipython=8.14.0=pyh41d4057_0
  - jedi=0.19.2=pyhd8ed1ab_1
  - jupyter_client=8.6.3=pyhd8ed1ab_1
  - jupyter_core=5.7.2=pyh31011fe_1
  - lame=3.100=h7f98852_1001
  - ld_impl_linux-64=2.40=h12ee557_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libpng=1.6.37=h21135ba_2
  - libsodium=1.0.18=h36c2ea0_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - matplotlib-inline=0.1.7=pyhd8ed1ab_1
  - ncurses=6.4=h6a678d5_0
  - nest-asyncio=1.6.0=pyhd8ed1ab_1
  - nettle=3.6=he412f7d_0
  - openh264=2.1.1=h780b84a_0
  - openssl=3.0.16=h5eee18b_0
  - parso=0.8.4=pyhd8ed1ab_1
  - pexpect=4.9.0=pyhd8ed1ab_1
  - pickleshare=0.7.5=pyhd8ed1ab_1004
  - pip=25.0=py312h06a4308_0
  - prompt-toolkit=3.0.50=pyha770c72_0
  - prompt_toolkit=3.0.50=hd8ed1ab_0
  - psutil=5.9.0=py312h5eee18b_1
  - ptyprocess=0.7.0=pyhd8ed1ab_1
  - pure_eval=0.2.3=pyhd8ed1ab_1
  - python=3.12.9=h5148396_0
  - pyzmq=26.2.0=py312h6a678d5_0
  - readline=8.2=h5eee18b_0
  - setuptools=75.8.0=py312h06a4308_0
  - sqlite=3.45.3=h5eee18b_0
  - stack_data=0.6.3=pyhd8ed1ab_1
  - tk=8.6.14=h39e8969_0
  - tornado=6.4.2=py312h5eee18b_0
  - traitlets=5.14.3=pyhd8ed1ab_1
  - typing_extensions=4.12.2=pyha770c72_1
  - wcwidth=0.2.13=pyhd8ed1ab_1
  - wheel=0.45.1=py312h06a4308_0
  - x264=1!161.3030=h7f98852_1
  - xz=5.6.4=h5eee18b_1
  - zeromq=4.3.5=h6a678d5_0
  - zipp=3.21.0=pyhd8ed1ab_1
  - zlib=1.2.13=h5eee18b_1
  - pip:
      - absl-py==2.1.0
      - aiohappyeyeballs==2.6.1
      - aiohttp==3.11.13
      - aiosignal==1.3.2
      - alembic==1.15.1
      - antlr4-python3-runtime==4.9.3
      - asteroid-filterbanks==0.4.0
      - attrs==25.3.0
      - audio-separator==0.30.1
      - audioread==3.0.1
      - beartype==0.18.5
      - beautifulsoup4==4.13.3
      - certifi==2025.1.31
      - cffi==1.17.1
      - charset-normalizer==3.4.1
      - click==8.1.8
      - coloredlogs==15.0.1
      - colorlog==6.9.0
      - contourpy==1.3.1
      - cycler==0.12.1
      - cython==3.0.12
      - decorator==5.2.1
      - decord==0.6.0
      - diffq==0.2.4
      - docopt==0.6.2
      - dotenv==0.9.9
      - einops==0.8.1
      - filelock==3.17.0
      - flatbuffers==25.2.10
      - fonttools==4.56.0
      - frozenlist==1.5.0
      - fsspec==2025.3.0
      - gdown==5.2.0
      - greenlet==3.1.1
      - huggingface-hub==0.29.3
      - humanfriendly==10.0
      - hyperpyyaml==1.2.2
      - idna==3.10
      - imageio==2.37.0
      - jax==0.5.2
      - jaxlib==0.5.1
      - jinja2==3.1.6
      - joblib==1.4.2
      - julius==0.2.7
      - kiwisolver==1.4.8
      - lazy-loader==0.4
      - librosa==0.11.0
      - lightning==2.5.0.post0
      - lightning-utilities==0.14.0
      - llvmlite==0.44.0
      - mako==1.3.9
      - markdown-it-py==3.0.0
      - markupsafe==3.0.2
      - matplotlib==3.10.1
      - mdurl==0.1.2
      - mediapipe==0.10.21
      - ml-collections==1.0.0
      - ml-dtypes==0.5.1
      - mpmath==1.3.0
      - msgpack==1.1.0
      - multidict==6.1.0
      - networkx==3.4.2
      - numba==0.61.0
      - numpy==1.26.4
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.4.127
      - nvidia-cuda-nvrtc-cu12==12.4.127
      - nvidia-cuda-runtime-cu12==12.4.127
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==********
      - nvidia-curand-cu12==**********
      - nvidia-cusolver-cu12==********
      - nvidia-cusparse-cu12==**********
      - nvidia-cusparselt-cu12==0.6.2
      - nvidia-nccl-cu12==2.21.5
      - nvidia-nvjitlink-cu12==12.4.127
      - nvidia-nvtx-cu12==12.4.127
      - omegaconf==2.3.0
      - onnx==1.17.0
      - onnx2torch==1.5.15
      - onnxruntime-gpu==1.21.0
      - opencv-contrib-python==*********
      - opencv-python==*********
      - opt-einsum==3.4.0
      - optuna==4.2.1
      - packaging==24.2
      - pandas==2.2.3
      - pillow==11.1.0
      - platformdirs==4.3.6
      - pooch==1.8.2
      - primepy==1.3
      - propcache==0.3.0
      - protobuf==4.25.6
      - pyannote-audio==3.3.2
      - pyannote-core==5.0.0
      - pyannote-database==5.1.3
      - pyannote-metrics==3.2.1
      - pyannote-pipeline==3.0.1
      - pycparser==2.22
      - pydub==0.25.1
      - pygments==2.19.1
      - pyparsing==3.2.1
      - pysocks==1.7.1
      - python-dateutil==2.9.0.post0
      - python-dotenv==1.0.1
      - python-speech-features==0.6
      - pytorch-lightning==2.5.0.post0
      - pytorch-metric-learning==2.8.1
      - pytz==2025.1
      - pyyaml==6.0.2
      - requests==2.32.3
      - resampy==0.4.3
      - rich==13.9.4
      - rotary-embedding-torch==0.6.5
      - ruamel-yaml==0.18.10
      - ruamel-yaml-clib==0.2.12
      - samplerate==0.1.0
      - scenedetect==0.6.6
      - scikit-learn==1.6.1
      - scipy==1.15.2
      - semver==3.0.4
      - sentencepiece==0.2.0
      - shellingham==1.5.4
      - six==1.17.0
      - sortedcontainers==2.4.0
      - sounddevice==0.5.1
      - soundfile==0.13.1
      - soupsieve==2.6
      - soxr==0.5.0.post1
      - speechbrain==1.0.2
      - sqlalchemy==2.0.39
      - sympy==1.13.1
      - tabulate==0.9.0
      - tensorboardx==*******
      - threadpoolctl==3.5.0
      - torch==2.6.0
      - torch-audiomentations==0.12.0
      - torch-pitch-shift==1.2.5
      - torchaudio==2.6.0
      - torchmetrics==1.6.2
      - torchvision==0.21.0
      - tqdm==4.67.1
      - triton==3.2.0
      - typer==0.15.2
      - tzdata==2025.1
      - urllib3==2.3.0
      - yarl==1.18.3
      - youtube-dl==2021.12.17
prefix: /home/<USER>/miniconda3/envs/dpp
