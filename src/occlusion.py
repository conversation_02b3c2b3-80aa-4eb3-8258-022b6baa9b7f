"""
Occlusion detection module for video analysis.

This module provides functionality to detect face occlusion in videos using
zero-shot object detection (Grounding DINO) and segmentation (SAM) models.
It identifies when objects like microphones occlude faces in video frames.
"""

import os
import torch
import numpy as np
import cv2
from glob import glob

from torch.cuda.amp import autocast
from PIL import Image
from dataclasses import dataclass
from typing import List, Optional

from transformers import (
    AutoProcessor,
    AutoModelForZeroShotObjectDetection,
    AutoModelForMaskGeneration,
)
import face_alignment
from tqdm import tqdm

device = "cuda:0" if torch.cuda.is_available() else "cpu"

fa = face_alignment.FaceAlignment(
    face_alignment.LandmarksType.TWO_D,
    device=device,
    flip_input=False,
)

detection_model_id = "IDEA-Research/grounding-dino-tiny"
detection_processor = AutoProcessor.from_pretrained(detection_model_id)
detection_model = (
    AutoModelForZeroShotObjectDetection.from_pretrained(detection_model_id)
    .to(device)
    .eval()
)

sam_model_id = "facebook/sam-vit-base"
sam_processor = AutoProcessor.from_pretrained(sam_model_id)
sam_model = AutoModelForMaskGeneration.from_pretrained(sam_model_id).to(device).eval()


@dataclass
class DetectionResult:
    """
    Data class representing an object detection result.

    Attributes:
        label (str): The detected object label/class name
        score (float): Confidence score of the detection
        box (List[int]): Bounding box coordinates [xmin, ymin, xmax, ymax]
        mask (Optional[np.ndarray]): Segmentation mask for the detected object
    """

    label: str
    score: float
    box: List[int]  # [xmin, ymin, xmax, ymax]
    mask: Optional[np.ndarray] = None


def detect_objects(
    image: Image.Image,
    labels: List[str],
    threshold: float = 0.3,
):
    """
    Zero-shot object detection using Grounding DINO.

    Args:
        image (Image.Image): Input PIL image for object detection
        labels (List[str]): List of object labels to detect
        threshold (float): Detection confidence threshold (default: 0.3)

    Returns:
        List[DetectionResult]: List of detected objects with their bounding boxes

    Note:
        Uses the global detection_processor and detection_model for inference.
        Performs detection under autocast for memory efficiency.
    """

    inputs = detection_processor(images=image, text=[labels], return_tensors="pt").to(
        device
    )

    # 3. Inference under autocast
    with torch.no_grad():
        with autocast():
            outputs = detection_model(**inputs)

    # 4. Post-process
    results = detection_processor.post_process_grounded_object_detection(
        outputs,
        inputs["input_ids"],
        box_threshold=threshold,
        text_threshold=0.2,
        target_sizes=[image.size[::-1]],
    )[0]

    dets = [
        DetectionResult(
            label=str(lab),
            score=float(score),
            box=[int(x) for x in box.tolist()],
        )
        for box, score, lab in zip(
            results["boxes"], results["scores"], results["labels"]
        )
    ]
    return dets


def segment_masks(
    image: Image.Image,
    detections: List[DetectionResult],
):
    """
    Generate segmentation masks for detected objects using SAM (Segment Anything Model).

    Args:
        image (Image.Image): Input PIL image
        detections (List[DetectionResult]): List of detection results with bounding boxes

    Returns:
        List[DetectionResult]: Updated detection results with segmentation masks attached

    Note:
        Uses box prompts from the detection results to generate precise segmentation masks.
        The masks are attached to the DetectionResult objects in-place.
    """

    # 2. Prepare inputs
    boxes = [det.box for det in detections]
    inputs = sam_processor(images=image, input_boxes=[boxes], return_tensors="pt").to(
        device
    )

    # 3. Inference under autocast
    with torch.no_grad():
        with autocast():
            outputs = sam_model(**inputs)

    # 4. Post-process masks
    masks = sam_processor.image_processor.post_process_masks(
        outputs.pred_masks.cpu(),
        inputs["original_sizes"].cpu(),
        inputs["reshaped_input_sizes"].cpu(),
    )[0]

    # 5. Attach & optionally refine
    for det, m in zip(detections, masks):
        arr = m.numpy().astype(np.uint8)
        det.mask = arr

    return detections


def compute_iou(mask1: np.ndarray, mask2: np.ndarray) -> float:
    """
    Compute the Intersection over Union of two boolean (0/1) masks.
    """
    if mask1.shape != mask2.shape:
        raise ValueError("Masks must have the same shape")
    intersection = np.logical_and(mask1, mask2).sum()
    union = np.logical_or(mask1, mask2).sum()
    if union == 0:
        # define IoU=1 if both masks are empty, else 0
        return 1.0 if intersection == 0 else 0.0
    return intersection / union


if __name__ == "__main__":
    sampling_rate = 10

    base_dir = "dataset"
    # Open both files up front (or you can open them on demand)
    with (
        open("non_occluded_videos.txt", "a") as non_occluded_file,
        open("occluded_videos.txt", "a") as occluded_file,
    ):
        videos = glob(os.path.join(base_dir, "*"))
        for video_name in tqdm(videos):
            rgb_frames = []
            frame_idx = 0
            is_occluded = False

            v_stream = cv2.VideoCapture(video_name)
            try:
                while True:
                    is_okay, frame = v_stream.read()
                    if not is_okay:
                        break
                    if frame_idx % sampling_rate == 0:
                        rgb_frames.append(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                    frame_idx += 1
            finally:
                v_stream.release()

            if not rgb_frames:
                print(f"No sampled frames in {video_name}, skipping.")
                continue

            for frame_idx in range(len(rgb_frames)):
                h, w = rgb_frames[frame_idx].shape[:2]
                bboxes = fa.face_detector.detect_from_image(
                    rgb_frames[frame_idx].copy()
                )
                if not bboxes:
                    print(f"No face detected in sampled frame of {video_name}.")
                    continue

                x1, y1, x2, y2 = np.array(bboxes[0])[:4].astype(int)

                face_mask = np.zeros((h, w), dtype=np.uint8)
                face_mask[y1:y2, x1:x2] = 255

                pil_image = Image.fromarray(rgb_frames[frame_idx]).convert("RGB")

                # Detect with DINO
                dets = detect_objects(pil_image, ["a microphone"], 0.7)
                if not dets:
                    continue

                # Segment masks with SAM
                dets = segment_masks(pil_image, dets)
                # 1) Find the “microphone” detection
                mic_det = next(
                    (d for d in dets if "microphone" in d.label.lower()), None
                )
                if mic_det is None:
                    continue

                mask = mic_det.mask

                mask = mask.transpose(1, 2, 0) * 255

                disp_mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)

                segmentation_mask = (disp_mask > 0).astype(np.uint8) * 255
                IoU = compute_iou(face_mask, segmentation_mask)
                if IoU != 0:
                    is_occluded = True
                    break

            if is_occluded:
                occluded_file.write(video_name + "\n")
            else:
                non_occluded_file.write(video_name + "\n")
