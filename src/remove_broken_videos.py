# Copyright (c) 2024 Bytedance Ltd. and/or its affiliates
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Module for detecting and removing broken/corrupted video files.

This module provides functionality to identify videos that cannot be properly
read and optionally remove them from the filesystem using multiprocessing
for efficient batch processing.
"""

import os
from multiprocessing import Pool
import tqdm

from modules.av_reader import AVReader
from .utils.video_utils import gather_video_paths_recursively


def remove_broken_video(video_path):
    """
    Check if a video is broken and remove it if it cannot be read.

    Args:
        video_path (str): Path to the video file to check

    Note:
        This function attempts to read the video using AVReader.
        If any exception occurs during reading, the video file is deleted.
        No return value as this is designed for use with multiprocessing.
    """
    try:
        AVReader(video_path)
    except Exception:
        os.remove(video_path)


def remove_broken_videos_multiprocessing(input_dir, num_workers):
    """
    Remove broken videos from a directory using multiprocessing.

    Args:
        input_dir (str): Directory containing videos to check
        num_workers (int): Number of worker processes to use

    Note:
        This function recursively finds all video files in the input directory
        and checks each one in parallel. Broken videos are automatically removed.
    """
    video_paths = gather_video_paths_recursively(input_dir)

    print("Removing broken videos...")
    with Pool(num_workers) as pool:
        for _ in tqdm.tqdm(
            pool.imap_unordered(remove_broken_video, video_paths),
            total=len(video_paths),
        ):
            pass


if __name__ == "__main__":
    input_dir = "/mnt/bn/maliva-gen-ai-v2/chunyu.li/multilingual/affine_transformed"
    num_workers = 50

    remove_broken_videos_multiprocessing(input_dir, num_workers)
