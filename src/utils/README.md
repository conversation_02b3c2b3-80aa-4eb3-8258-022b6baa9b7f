# HotDub-Preprocess Utilities

This directory contains utility modules used throughout the HotDub-Preprocess application.

## Modules

### logger.py

The `logger.py` module provides a centralized logging configuration for the entire application.

#### Features

- Unified logging configuration across all modules
- File logging with rotation (in `.logs/application.log`)
- Console output for immediate feedback
- Process-safe logging for multiprocessing support
- Proper exception logging with tracebacks

#### Usage

```python
from src.utils.logger import configure_logger

# Create a logger for the current module
logger = configure_logger(__name__)

# Use the logger
logger.debug("Detailed information for debugging")
logger.info("General information about program execution")
logger.warning("Warning about potential issues")
logger.error("Error information", exc_info=True)  # Include exception traceback
```

#### Multiprocessing Support

When using the logger in a multiprocessing context, reconfigure the logger in each process:

```python
def worker_process(args):
    # Reconfigure logger for this process
    from src.utils.logger import configure_logger
    logger = configure_logger(f"{__name__}.process.{os.getpid()}")
    
    # Now use the logger
    logger.info(f"Worker process started with args: {args}")
    # ...
```

### util.py

General utility functions for video processing.

### audio_utils.py

Utility functions for audio processing.

### video_utils.py

Utility functions for video processing.