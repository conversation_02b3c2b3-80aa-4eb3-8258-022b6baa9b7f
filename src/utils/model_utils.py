import hashlib
import os
from pathlib import Path

import requests
import yaml
from tqdm import tqdm

from ..utils.logger import configure_logger  # Assuming logger is in parent utils

try:
    from huggingface_hub import hf_hub_download
    from huggingface_hub.errors import HfHubHTTPError

    HUGGINGFACE_HUB_AVAILABLE = True
except ImportError:
    HUGGINGFACE_HUB_AVAILABLE = False

logger = configure_logger(__name__)

DEFAULT_MODELS_CONFIG_PATH = "src/configs/models.yaml"


def get_project_root() -> Path:
    """Gets the project root directory."""
    return Path(__file__).parent.parent.parent


def calculate_sha256(file_path: Path) -> str:
    """Calculates the SHA256 checksum of a file."""
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    return sha256_hash.hexdigest()


def download_file_direct(
    url: str, destination: Path, description: str = "Downloading model"
):
    """Downloads a file directly with a progress bar."""
    logger.info(f"Downloading {description} directly from {url} to {destination}...")
    try:
        response = requests.get(url, stream=True, timeout=300)
        response.raise_for_status()
        total_size = int(response.headers.get("content-length", 0))

        destination.parent.mkdir(parents=True, exist_ok=True)

        with (
            open(destination, "wb") as f,
            tqdm(
                desc=description,
                total=total_size,
                unit="iB",
                unit_scale=True,
                unit_divisor=1024,
            ) as bar,
        ):
            for chunk in response.iter_content(chunk_size=8192):
                size = f.write(chunk)
                bar.update(size)
        logger.info(f"Successfully downloaded {destination}")
        return True
    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to download {url}: {e}")
        if destination.exists():
            try:
                os.remove(destination)
            except OSError:
                pass
        return False
    except Exception as e:
        logger.error(
            f"An unexpected error occurred during direct download of {url}: {e}"
        )
        if destination.exists():
            try:
                os.remove(destination)
            except OSError:
                pass
        return False


def verify_and_download_model(
    model_key: str, models_config_path: str = DEFAULT_MODELS_CONFIG_PATH
) -> Path:
    """
    Verifies if a model checkpoint exists and is valid, downloads it if not.
    Supports Hugging Face Hub and direct URL downloads.
    """
    project_root = get_project_root()
    config_file_path = project_root / models_config_path

    if not config_file_path.exists():
        raise FileNotFoundError(
            f"Models configuration file not found: {config_file_path}"
        )

    with open(config_file_path, "r") as f:
        config = yaml.safe_load(f)

    if not config or "models" not in config or model_key not in config["models"]:
        raise KeyError(
            f"Model key '{model_key}' not found in {config_file_path} or config is invalid."
        )

    model_info = config["models"][model_key]
    repo_id = model_info.get("repo_id")
    print(f"Repo-ID: {repo_id}")
    filename = model_info.get("filename")
    revision = model_info.get("revision")
    subfolder = model_info.get("subfolder")
    direct_url = model_info.get("url")
    expected_checksum = model_info.get("checksum")
    relative_model_path = model_info.get(
        "local_path"
    )  # Renamed from 'path' for clarity
    model_description = model_info.get("description", model_key)

    if not relative_model_path:
        raise ValueError(f"Missing 'local_path' for model '{model_key}' in config.")

    absolute_model_path = (project_root / relative_model_path).resolve()
    absolute_model_path.parent.mkdir(
        parents=True, exist_ok=True
    )  # Ensure directory exists

    needs_download = True
    if absolute_model_path.exists():
        if expected_checksum:
            logger.info(
                f"Verifying checksum for existing model: {absolute_model_path}..."
            )
            actual_checksum = calculate_sha256(absolute_model_path)
            if actual_checksum == expected_checksum.split(":")[-1]:
                logger.info(
                    f"Checksum verified for {model_description}. Model is up to date."
                )
                needs_download = False
            else:
                logger.warning(
                    f"Checksum mismatch for {absolute_model_path}. Expected {expected_checksum}, got sha256:{actual_checksum}. Re-downloading."
                )
                try:
                    os.remove(absolute_model_path)
                except OSError:
                    pass
        else:
            logger.info(
                f"Model exists at {absolute_model_path}, but no checksum for verification. Assuming correct."
            )
            needs_download = False

    if needs_download:
        if repo_id and filename and HUGGINGFACE_HUB_AVAILABLE:
            logger.info(
                f"Attempting to download {model_description} from Hugging Face Hub: repo_id='{repo_id}', filename='{filename}'"
            )
            try:
                downloaded_path_temp = hf_hub_download(
                    repo_id=repo_id,
                    filename=filename,
                    revision=revision,
                    subfolder=subfolder,
                    local_dir=absolute_model_path.parent,  # Download to the parent directory
                    local_dir_use_symlinks=False,  # Avoid symlinks, copy directly
                    cache_dir=project_root
                    / ".cache"
                    / "huggingface",  # Optional: specify a cache dir
                )
                # hf_hub_download might save with a different temp name or structure, ensure it's moved to target
                # For simplicity, if local_dir is the parent, it should place it correctly if filename matches.
                # If hf_hub_download saves to a specific cache and returns path, we'd copy from there.
                # Assuming it downloads to local_dir/filename if filename is simple.
                # If it downloads to local_dir/repo_id_structure/filename, we need to move.
                # The current hf_hub_download behavior with local_dir should place it correctly if filename is the target.
                # Let's ensure the final file is at absolute_model_path

                # If hf_hub_download saves to a cache and returns the path to the cached file:
                # shutil.move(downloaded_path_temp, absolute_model_path)
                # For now, assuming it places it directly if local_dir is specific enough.
                # The simplest is to download to a temp file then move.
                # Let's refine: download to a temp name, then rename/move to ensure atomicity.

                temp_download_path = Path(downloaded_path_temp)
                if temp_download_path.name != absolute_model_path.name:
                    # If hf_hub_download doesn't place it exactly as absolute_model_path.name in the parent dir
                    os.rename(temp_download_path, absolute_model_path)
                elif temp_download_path != absolute_model_path:  # if it's in cache
                    shutil.copyfile(temp_download_path, absolute_model_path)

                logger.info(
                    f"Successfully downloaded {model_description} from Hugging Face Hub to {absolute_model_path}"
                )

            except HfHubHTTPError as e:
                logger.error(
                    f"Hugging Face Hub download failed for {model_description}: {e}"
                )
                raise RuntimeError(f"HF download failed for {model_description}") from e
            except Exception as e:
                logger.error(
                    f"An unexpected error occurred during Hugging Face Hub download of {model_description}: {e}"
                )
                raise RuntimeError(f"HF download failed for {model_description}") from e

        elif direct_url:
            if not download_file_direct(
                direct_url, absolute_model_path, model_description
            ):
                raise RuntimeError(
                    f"Failed to download model directly: {model_description} from {direct_url}"
                )
        else:
            raise ValueError(
                f"Missing 'repo_id'/'filename' for Hugging Face Hub or 'url' for direct download for model '{model_key}', and download is required."
            )

        if expected_checksum:  # Re-verify after download
            logger.info(
                f"Verifying checksum for downloaded model: {absolute_model_path}..."
            )
            actual_checksum = calculate_sha256(absolute_model_path)
            if actual_checksum != expected_checksum.split(":")[-1]:
                try:
                    os.remove(absolute_model_path)
                except OSError:
                    pass
                raise RuntimeError(
                    f"Checksum verification FAILED after downloading {model_description}. Expected {expected_checksum}, got sha256:{actual_checksum}."
                )
            logger.info(f"Checksum verified for downloaded {model_description}.")

    return absolute_model_path


if __name__ == "__main__":
    import logging
    import shutil

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    if not HUGGINGFACE_HUB_AVAILABLE:
        logger.error(
            "huggingface_hub library is not installed. Please install it to test Hugging Face Hub downloads."
        )

    logger.info("Attempting to verify/download models (example)...")
    # Create dummy config for testing
    dummy_config_content = {
        "models": {
            "test_hf_model": {
                "repo_id": "hf-internal-testing/tiny-random-bert",  # A small, real model for testing
                "filename": "config.json",
                "local_path": "temp_models/tiny-bert-config.json",
                "description": "Tiny BERT config (HF)",
            },
            "test_direct_model": {
                "url": "https://raw.githubusercontent.com/huggingface/huggingface_hub/main/README.md",  # A small text file
                "local_path": "temp_models/README.md",
                "checksum": "sha256:d83b8b02980ac7827396870271e997990c6810651f2c9500012908270d08f0b0",  # Pre-calculated
                "description": "README (Direct)",
            },
            "face_landmarker": {  # From your actual config
                "url": "https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/latest/face_landmarker.task",
                "local_path": "temp_models/face_landmarker.task",
                "description": "MediaPipe Face Landmarker (Direct)",
            },
        }
    }
    root = get_project_root()
    temp_config_path = root / "src/configs/temp_models_test.yaml"
    temp_models_dir = root / "temp_models"
    if temp_models_dir.exists():
        shutil.rmtree(temp_models_dir)  # Clean up previous test runs
    temp_models_dir.mkdir(parents=True, exist_ok=True)

    with open(temp_config_path, "w") as f:
        yaml.dump(dummy_config_content, f)

    try:
        if HUGGINGFACE_HUB_AVAILABLE:
            hf_model_path = verify_and_download_model(
                "test_hf_model",
                models_config_path=str(temp_config_path.relative_to(root)),
            )
            logger.info(f"Test HF model ready at: {hf_model_path}")
            assert hf_model_path.exists()

        direct_model_path = verify_and_download_model(
            "test_direct_model",
            models_config_path=str(temp_config_path.relative_to(root)),
        )
        logger.info(f"Test Direct model ready at: {direct_model_path}")
        assert direct_model_path.exists()

        # face_landmarker_path = verify_and_download_model("face_landmarker", models_config_path=str(temp_config_path.relative_to(root)))
        # logger.info(f"Face Landmarker model ready at: {face_landmarker_path}")
        # assert face_landmarker_path.exists()

    except Exception as e:
        logger.error(f"Error in example usage: {e}", exc_info=True)
    finally:
        # Clean up dummy files
        if temp_config_path.exists():
            os.remove(temp_config_path)
        # if temp_models_dir.exists():
        #     shutil.rmtree(temp_models_dir)
        logger.info(
            "Test cleanup complete (manual cleanup of temp_models dir might be needed if errors occurred)."
        )
