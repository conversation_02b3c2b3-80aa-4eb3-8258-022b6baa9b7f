import logging
import sys
from logging.handlers import RotatingFileHandler
from pathlib import Path


def configure_logger(name=__name__):
    """
    Configure and return a logger with file and console handlers.

    Args:
        name: Logger name, typically __name__ of the calling module

    Returns:
        logging.Logger: Configured logger instance
    """
    # Create logs directory if needed
    log_dir = Path(__file__).parent.parent.parent / ".logs"
    log_dir.mkdir(exist_ok=True, parents=True)

    logger = logging.getLogger(name)
    logger.setLevel(logging.DEBUG)
    # Prevent propagation to root logger
    logger.propagate = False

    # Prevent duplicate handlers by removing existing ones
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # File handler with rotation
    file_handler = RotatingFileHandler(
        filename=log_dir / "application.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding="utf-8",
    )
    file_formatter = logging.Formatter(
        "%(asctime)s | %(process)d | %(levelname)-8s | %(name)s:%(lineno)d - %(message)s"
    )
    file_handler.setFormatter(file_formatter)
    file_handler.setLevel(logging.DEBUG)

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_formatter = logging.Formatter("%(levelname)-8s | %(message)s")
    console_handler.setFormatter(console_formatter)
    console_handler.setLevel(logging.INFO)

    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger
