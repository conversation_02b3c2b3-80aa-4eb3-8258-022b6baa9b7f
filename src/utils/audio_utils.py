import multiprocessing
import os
import subprocess
from typing import List, Optional

import torch
from audio_separator.separator import Separator
from decord import AudioReader
from dotenv import load_dotenv
from einops import rearrange
from pyannote.audio import Pipeline

# Import logger
from src.utils.logger import configure_logger

# Configure logger
logger = configure_logger(__name__)


def _process_audio_pipeline(
    audio_path: str, hf_token: str, device: torch.device
) -> Pipeline:
    """Initialize and run speaker diarization pipeline in an isolated process.

    Args:
        audio_path: Path to the audio file
        hf_token: HuggingFace authentication token
        device: torch device to use for processing

    Returns:
        Diarization results from the pipeline
    """
    # Initialize the pretrained speaker diarization pipeline
    pipeline = Pipeline.from_pretrained(
        "pyannote/speaker-diarization-3.1", use_auth_token=hf_token
    )

    # Move pipeline to specified device
    pipeline = pipeline.to(device)

    # Process audio and return results
    return pipeline(audio_path)


def extract_audio(
    input_video_path: str, output_path: Optional[str] = None, format: str = "wav"
) -> str:
    """
    Extract audio from a video file using FFMPEG.

    Args:
        input_video_path (str): Path to the input video file
        output_path (str, optional): Path for the output audio file.
            If None, a path will be generated based on the input path.
        format (str, optional): Audio format, default is "wav"

    Returns:
        str: Path to the extracted audio file
    """
    # Validate input
    if not os.path.exists(input_video_path):
        error_msg = f"Video file not found: {input_video_path}"
        logger.error(error_msg)
        raise FileNotFoundError(error_msg)

    # Generate output path if not provided
    if output_path is None:
        video_dir = os.path.dirname(input_video_path)
        video_name = os.path.splitext(os.path.basename(input_video_path))[0]
        output_path = os.path.join(video_dir, f"{video_name}.{format}")

    # Ensure output directory exists
    os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

    try:
        # Check if ffmpeg is installed
        check_ffmpeg_installed()

        # TODO: Extract audio with default sample rate and channels

        # Extract audio using ffmpeg
        command = [
            "ffmpeg",
            "-y",  # Overwrite output file if it exists
            "-loglevel",
            "error",
            "-i",
            input_video_path,
            "-vn",  # No video
            "-acodec",
            "pcm_s16le"
            if format == "wav"
            else "copy",  # Use PCM for WAV, otherwise copy
            output_path,
        ]

        subprocess.run(command, check=True)
        logger.info(f"Successfully extracted audio to {output_path}")
        return output_path

    except subprocess.CalledProcessError as e:
        error_msg = f"FFMPEG error extracting audio: {e}"
        logger.error(error_msg)
        raise RuntimeError(error_msg)
    except Exception as e:
        error_msg = f"Error extracting audio: {e}"
        logger.exception(error_msg)
        raise


def read_audio(audio_path: str, audio_sample_rate: int = 16000) -> torch.Tensor:
    """
    Read audio file and return tensor of samples.

    Args:
        audio_path (str): Path to the audio file
        audio_sample_rate (int, optional): Sample rate for the audio. Defaults to 16000.

    Returns:
        torch.Tensor: Audio samples tensor

    Raises:
        ValueError: If audio path is None
        FileNotFoundError: If audio file does not exist
    """
    if audio_path is None:
        raise ValueError("Audio path is required.")

    if not os.path.exists(audio_path):
        raise FileNotFoundError(f"Audio file not found: {audio_path}")

    try:
        # Use decord's AudioReader to read the audio file
        ar = AudioReader(audio_path, sample_rate=audio_sample_rate, mono=True)

        # Convert to tensor
        audio_samples = torch.from_numpy(ar[:].asnumpy())
        audio_samples = audio_samples.squeeze(0)

        return audio_samples

    except Exception as e:
        logger.exception(f"Error reading audio file: {e}")
        raise


def make_audio_window(audio_embeddings: torch.Tensor, window_size: int) -> torch.Tensor:
    """
    Create sliding windows from audio embeddings.

    Args:
        audio_embeddings (torch.Tensor): Audio embeddings tensor
        window_size (int): Size of the window

    Returns:
        torch.Tensor: Windowed audio embeddings
    """
    audio_window = []
    end_idx = audio_embeddings.shape[1] - window_size + 1

    for i in range(end_idx):
        audio_window.append(audio_embeddings[:, i : i + window_size, :])

    audio_window = torch.stack(audio_window)
    audio_window = rearrange(audio_window, "f b w d -> b f w d")

    return audio_window


def speaker_count(audio_file_path: str, device: Optional[str] = None) -> int:
    """
    Count the number of unique speakers in an audio file using pyannote.audio.

    Parameters:
        audio_file_path (str): Path to the audio file
        device (str, optional): Device to run the pipeline on. Options: 'cpu', 'cuda', 'cuda:0', 'cuda:1', etc.
                            If None, will use CUDA if available, otherwise CPU.

    Returns:
        int: Number of unique speakers detected
    """
    if not os.path.exists(audio_file_path):
        raise FileNotFoundError(f"Audio file not found: {audio_file_path}")

    try:
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True

        # Load environment variables from .env file
        load_dotenv()

        # Get the HuggingFace token from environment variables
        hf_token = os.getenv("HUGGINGFACE_TOKEN")
        if not hf_token:
            raise ValueError("HUGGINGFACE_TOKEN not found in .env file")

        # Determine the device to use
        if device is None:
            device = (
                torch.device("cuda")
                if torch.cuda.is_available()
                else torch.device("cpu")
            )

        # Create and run pipeline in a spawned process
        with multiprocessing.get_context("spawn").Pool(1) as pool:
            diarization = pool.apply(
                _process_audio_pipeline, args=(audio_file_path, hf_token, device)
            )

        logger.info(f"Diarization results: {diarization}")

        # Extract unique speaker labels
        speakers = set()
        for turn, _, speaker in diarization.itertracks(yield_label=True):
            speakers.add(speaker)

        # Return the number of unique speakers
        return len(speakers)

    except Exception as e:
        logger.exception(f"Error counting speakers: {e}")
        raise


def denoise_audio(audio_file_path: str, model_name: Optional[str] = "Denoiser") -> str:
    # Use cleareraudio to denoise the audio.
    pass


def enhance(
    audio_file_path: str, model_name: Optional[str] = "UVR-MDX-NET-Inst_HQ_3.onnx"
) -> List[str]:
    """
    Enhance audio quality using the audio-separator library.

    Args:
        audio_file_path (str): Path to the audio file to enhance
        model_name (str, optional): ML model to use for enhancement.
            Defaults to "UVR-MDX-NET_Crowd_HQ_1.onnx".

    Returns:
        List[str]: Paths to the output enhanced audio files
    """
    if not os.path.exists(audio_file_path):
        raise FileNotFoundError(f"Audio file not found: {audio_file_path}")

    try:
        # Initialize the Separator class
        separator = Separator(sample_rate=16000)

        if model_name == "Default":
            model_name = "model_mel_band_roformer_ep_3005_sdr_11.4360.ckpt"

        # Load the specified machine learning model
        separator.load_model(model_name)

        output_names = {
            "vocals": "needed",
            "Instrumental": "trash",
            "Crowd": "trash",
            "No Crowd": "needed",
        }

        # Perform the separation
        output_files = separator.separate(audio_file_path, output_names)

        logger.info(
            f"Audio enhancement complete! Output file(s): {' '.join(output_files)}"
        )

        return output_files

    except Exception as e:
        logger.exception(f"Error enhancing audio: {e}")
        raise


def check_ffmpeg_installed() -> bool:
    """
    Check if FFmpeg is installed and available in the system path.

    Returns:
        bool: True if FFmpeg is installed, False otherwise

    Raises:
        FileNotFoundError: If FFmpeg is not installed
    """
    try:
        # Run the ffmpeg command with the -version argument
        result = subprocess.run(
            "ffmpeg -version",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            shell=True,
        )

        if result.returncode != 0:
            raise FileNotFoundError(
                "ffmpeg not found, please install it by:\n    $ conda install -c conda-forge ffmpeg"
            )

        return True

    except Exception as e:
        logger.error(f"Error checking FFmpeg installation: {e}")
        raise FileNotFoundError(
            "ffmpeg not found, please install it by:\n    $ conda install -c conda-forge ffmpeg"
        )
