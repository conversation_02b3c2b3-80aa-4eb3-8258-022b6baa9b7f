import json
import os
import shutil
import subprocess
import tempfile
from typing import Dict, List, Optional, Tuple, Union
from ..modules.av_reader import AVReader

import cv2
import imageio
import numpy as np
import torch
import torchvision
from decord import VideoReader
from einops import rearrange

# Import logger
from src.utils.logger import configure_logger


def process_video_corruption(path: str) -> Tuple[str, Union[bool, str]]:
    """Process a single video for corruption check."""
    try:
        is_corrupt_result = check_video_corruption(path)
        return path, is_corrupt_result
    except Exception as e:
        return path, str(e)


def process_video_duration(
    path: str, delete: bool = False, threshold: Optional[float] = None
) -> Tuple[str, Union[float, bool, str]]:
    """
    Process a single video for duration check and optional deletion if below threshold.

    Args:
        path (str): Path to the video file
        delete (bool): If True and duration < threshold, delete the file
        threshold (float, optional): Minimum duration threshold. Returns False if duration below this

    Returns:
        Tuple[str, Union[float, bool, str]]:
            - str: Video file path
            - float: Duration if no threshold or duration >= threshold
            - bool: False if duration < threshold
            - str: Error message if processing failed
    """
    try:
        result = get_video_duration(path, delete, threshold)
        return path, result
    except Exception as e:
        return path, str(e)


# Configure logger
logger = configure_logger(__name__)


def check_video_corruption(input_video_path: str) -> bool:
    """
    Check if a video file is corrupted or unreadable using AVReader.

    Args:
        input_video_path (str): Path to the video file to check

    Returns:
        bool: True if the video is corrupt, False otherwise
    """
    if not os.path.exists(input_video_path):
        logger.error(f"Video file not found: {input_video_path}")
        return True

    try:
        # Check if CUDA is available
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.debug(f"Using device {device} for video reading")

        # # Import decord inside function to avoid circular import
        # from decord import gpu, cpu

        # # Determine device context
        # ctx = gpu(0) if torch.cuda.is_available() else cpu(0)
        # logger.debug(f"Using device {ctx} for video reading")
        reader = None

        # Try to initialize AVReader which will check both audio and video streams
        try:
            # Try to read first frame with audio
            reader = AVReader(
                uri=input_video_path,
                sample_rate=-1,
            )
            # If we got here, both audio and video are readable
            return False
        except Exception as e:
            logger.error(f"Error reading video {input_video_path}: {e}")
            return True
        finally:
            # Cleanup resources
            del reader
    except Exception as e:
        logger.error(f"Error reading video {input_video_path}: {e}")
        return True


def check_all_videos_corruption(
    input_dir: str, output_dir: Optional[str] = None, num_workers: Optional[int] = None
) -> Dict[str, Union[bool, str]]:
    """
    Check all videos in a directory for corruption and optionally move valid ones to output directory.

    Args:
        input_dir (str): Directory containing input videos
        output_dir (str, optional): Directory to move valid videos to
        num_workers (int, optional): Number of worker processes

    Returns:
        Dict[str, Union[bool, str]]: Dictionary mapping video paths to either:
            - bool: True if corrupt, False if valid
            - str: error message if check failed
    """
    if not os.path.isdir(input_dir):
        raise FileNotFoundError(f"Directory not found: {input_dir}")

    # Get all video paths
    video_paths = collect_video_paths_recursively(input_dir)
    if not video_paths:
        logger.warning(f"No videos found in directory: {input_dir}")
        return {}

    # Calculate optimal number of workers if not provided
    if num_workers is None:
        # Use half of CPU cores by default to avoid system overload
        num_workers = max(1, os.cpu_count() // 2)
        logger.info(
            f"Automatically set num_workers to {num_workers} (half of CPU cores)"
        )

    logger.info(
        f"Checking {len(video_paths)} videos for corruption with {num_workers} workers..."
    )

    try:
        from concurrent.futures import ProcessPoolExecutor, TimeoutError, as_completed

        results = []

        # Use ProcessPoolExecutor for better control and error handling
        with ProcessPoolExecutor(max_workers=num_workers) as executor:
            # Submit all tasks and get future objects
            future_to_path = {
                executor.submit(process_video_corruption, path): path
                for path in video_paths
            }

            # Process completed tasks as they finish
            for future in as_completed(
                future_to_path, timeout=300
            ):  # 5 minute timeout per video
                path = future_to_path[future]
                try:
                    result = future.result()
                    results.append((path, result))
                except TimeoutError:
                    logger.error(f"Processing timed out for {path}")
                    results.append((path, str("Processing timed out")))
                except Exception as e:
                    logger.error(f"Error processing {path}: {e}")
                    results.append((path, str(e)))

        # Convert results to dictionary
        corruption_status = dict(results)

        print([is_corrupt[1] for path, is_corrupt in corruption_status.items()])

        # Get valid and corrupted videos
        valid_videos = [
            path for path, is_corrupt in corruption_status.items() if not is_corrupt[1]
        ]
        corrupted_count = sum(1 for _, v in corruption_status.items() if v[1])
        valid_count = len(valid_videos)

        # Log summary
        logger.info(f"Results: {valid_count} valid, {corrupted_count} corrupted videos")

        # Move valid videos to output directory if specified
        if output_dir and valid_videos:
            logger.info(f"Moving {len(valid_videos)} valid videos to {output_dir}")
            for video_path in valid_videos:
                try:
                    # Create relative path structure in output directory
                    rel_path = os.path.relpath(video_path, input_dir)
                    dst_path = os.path.join(output_dir, rel_path)

                    base_name = os.path.basename(dst_path)

                    # Check for spaces in the base name and replace with underscores
                    if " " in base_name:
                        new_base = base_name.replace(" ", "_")
                        dst_path = os.path.join(os.path.dirname(dst_path), new_base)

                    os.makedirs(os.path.dirname(dst_path), exist_ok=True)
                    shutil.copy2(video_path, dst_path)  # Use copy2 to preserve metadata
                    logger.info(f"Moved valid video to: {dst_path}")
                except Exception as e:
                    logger.error(f"Failed to move {video_path}: {e}")
                    corruption_status[video_path] = (
                        True  # Mark as invalid if move failed
                    )

        return corruption_status

    except Exception as e:
        logger.exception(f"Error in parallel video corruption check: {e}")
        raise


def check_ffmpeg_installed() -> bool:
    """
    Check if FFmpeg is installed and available in the system path.

    Returns:
        bool: True if FFmpeg is installed, False otherwise

    Raises:
        FileNotFoundError: If FFmpeg is not installed
    """
    try:
        # Run the ffmpeg command with the -version argument
        result = subprocess.run(
            "ffmpeg -version",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            shell=True,
        )

        if result.returncode != 0:
            raise FileNotFoundError(
                "ffmpeg not found, please install it by:\n    $ conda install -c conda-forge ffmpeg"
            )

        return True

    except Exception as e:
        logger.error(f"Error checking FFmpeg installation: {e}")
        raise FileNotFoundError(
            "ffmpeg not found, please install it by:\n    $ conda install -c conda-forge ffmpeg"
        )


def verify_video_fps(video_path: str, target_fps: float = 25.0) -> float:
    """
    Check if the FPS of a video file matches the target FPS.

    Args:
        video_path (str): Path to the video file
        target_fps (float, optional): Target frame rate to verify against. Defaults to 25.0.

    Returns:
        float: The frame rate of the video

    Raises:
        ValueError: If the video FPS does not match target_fps
    """
    # Check if file exists
    if not os.path.exists(video_path):
        raise FileNotFoundError(f"Video file not found: {video_path}")

    # Open the video and get FPS
    cam = cv2.VideoCapture(video_path)
    fps = cam.get(cv2.CAP_PROP_FPS)
    cam.release()

    # Check if FPS matches target
    if abs(fps - target_fps) > 0.01:  # Small tolerance for floating point comparison
        raise ValueError(
            f"Video FPS is {fps}, not the required {target_fps} FPS. Please convert the video to {target_fps} FPS."
        )

    return fps


def collect_video_paths(input_dir: str, paths: List[str]) -> None:
    """
    Recursively gather video paths from input directory.

    Args:
        input_dir (str): Directory containing input videos
        paths (List[str]): List to store discovered video paths
    """
    # Check if directory exists
    if not os.path.isdir(input_dir):
        logger.error(f"Directory not found: {input_dir}")
        return

    # Iterate through files and subdirectories
    for file in sorted(os.listdir(input_dir)):
        file_path = os.path.join(input_dir, file)

        # If file is a video, add to paths
        if file.endswith(".mp4"):
            paths.append(file_path)

        # If file is a directory, recursively search it
        elif os.path.isdir(file_path):
            collect_video_paths(file_path, paths)


def collect_video_paths_recursively(input_dir: str) -> List[str]:
    """
    Recursively gather all video paths from an input directory.

    Args:
        input_dir (str): Directory containing input videos

    Returns:
        List[str]: List of discovered video paths
    """
    logger.info(f"Recursively gathering video paths of {input_dir} ...")
    paths = []
    collect_video_paths(input_dir, paths)
    return paths


def get_video_duration(
    video_path: str, delete: bool = False, threshold: Optional[float] = None
) -> Union[float, bool]:
    """
    Get video duration and optionally delete if duration is below threshold.

    Args:
        video_path (str): Path to the video file
        delete (bool, optional): If True and duration < threshold, delete the video file. Defaults to False.
        threshold (float, optional): Minimum required duration in seconds. If provided and duration < threshold,
            returns False and optionally deletes file if delete=True. Defaults to None.

    Returns:
        Union[float, bool]:
            - float: Duration in seconds if no threshold or duration >= threshold
            - bool: False if threshold provided and duration < threshold

    Raises:
        FileNotFoundError: If the video file is not found
        RuntimeError: If ffprobe fails to get duration
        OSError: If deletion fails when delete=True
        ValueError: If threshold is negative
    """
    if not os.path.exists(video_path):
        raise FileNotFoundError(f"Video file not found: {video_path}")

    if threshold is not None and threshold < 0:
        raise ValueError(f"Threshold must be non-negative, got {threshold}")

    try:
        # Use ffprobe to get duration efficiently
        command = [
            "ffprobe",
            "-v",
            "error",
            "-show_entries",
            "format=duration",
            "-of",
            "default=noprint_wrappers=1:nokey=1",
            video_path,
        ]

        result = subprocess.run(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=False,
        )

        if result.returncode != 0:
            raise RuntimeError(f"Failed to get video duration: {result.stderr}")

        duration = float(result.stdout.strip())

        # Check threshold if provided
        if threshold is not None and duration < threshold:
            if delete:
                try:
                    os.remove(video_path)
                    logger.info(
                        f"Deleted video below threshold ({duration:.2f}s < {threshold}s): {video_path}"
                    )
                except OSError as e:
                    logger.error(f"Failed to delete video file {video_path}: {e}")
                    raise
            return False

        return duration

    except ValueError as e:
        logger.error(f"Error parsing ffprobe output: {e}")
        raise RuntimeError(f"Failed to parse video duration: {e}")
    except Exception as e:
        logger.error(f"Error getting video duration: {e}")
        raise


def get_all_videos_durations(
    input_dir: str,
    delete: bool = False,
    threshold: Optional[float] = None,
    num_workers: Optional[int] = None,
) -> Dict[str, Union[float, bool, str]]:
    """
    Get durations of all videos in a directory and optionally delete those below threshold.

    Args:
        input_dir (str): Directory containing input videos
        delete (bool, optional): If True and duration < threshold, delete those videos. Defaults to False.
        threshold (float, optional): Minimum required duration in seconds. If provided, returns False for
            videos with duration < threshold. Defaults to None.
        num_workers (int, optional): Number of worker processes. Defaults to CPU count.

    Returns:
        Dict[str, Union[float, bool, str]]: Dictionary mapping video paths to either:
            - float: duration in seconds if no threshold or duration >= threshold
            - bool: False if threshold provided and duration < threshold
            - str: error message if failed
    """
    if not os.path.isdir(input_dir):
        raise FileNotFoundError(f"Directory not found: {input_dir}")

    # Get all video paths
    video_paths = collect_video_paths_recursively(input_dir)
    if not video_paths:
        logger.warning(f"No videos found in directory: {input_dir}")
        return {}

    # Calculate optimal number of workers if not provided
    if num_workers is None:
        # Use half of CPU cores by default to avoid system overload
        num_workers = max(1, os.cpu_count() // 2)
        logger.info(
            f"Automatically set num_workers to {num_workers} (half of CPU cores)"
        )

    logger.info(f"Processing {len(video_paths)} videos with {num_workers} workers...")

    try:
        from concurrent.futures import ProcessPoolExecutor, TimeoutError, as_completed

        results = []

        # Use ProcessPoolExecutor for better control and error handling
        with ProcessPoolExecutor(max_workers=num_workers) as executor:
            # Submit all tasks and get future objects
            future_to_path = {
                executor.submit(process_video_duration, path, delete, threshold): path
                for path in video_paths
            }

            # Process completed tasks as they finish
            for future in as_completed(
                future_to_path, timeout=300
            ):  # 5 minute timeout per video
                path = future_to_path[future]
                try:
                    result = future.result()
                    results.append((path, result))
                except TimeoutError:
                    logger.error(f"Processing timed out for {path}")
                    results.append((path, str("Processing timed out")))
                except Exception as e:
                    logger.error(f"Error processing {path}: {e}")
                    results.append((path, str(e)))

        # Convert results to dictionary
        durations = dict(results)

        # Log summary
        success_count = sum(1 for v in durations.values() if isinstance(v[1], float))
        logger.info(f"Successfully processed {len(video_paths)} videos")
        logger.info(
            f"Results: {success_count} valid durations, {len(durations) - success_count} invalid durations"
        )

        return durations

    except Exception as e:
        logger.exception(f"Error in parallel video processing: {e}")
        raise


def load_and_validate_video(
    input_video_path: str,
    min_duration: float = 5.0,
    min_fps: float = 20.0,
    delete=False,
) -> Union[np.ndarray, str]:
    """
    Read video frames and validate duration and FPS.

    Args:
        input_video_path (str): Path to the video file
        min_duration (float, optional): Minimum required duration in seconds. Defaults to 5.0.
        min_fps (float, optional): Minimum required FPS. Defaults to 20.0.

    Returns:
        Union[np.ndarray, str]:
            - numpy array of frames if valid
            - "duration_invalid" if duration < min_duration
            - "fps_invalid" if fps < min_fps
    """
    if not os.path.exists(input_video_path):
        logger.error(f"Video file not found: {input_video_path}")
        raise FileNotFoundError(f"Video file not found: {input_video_path}")

    try:
        # Check if file is corrupt
        if check_video_corruption(input_video_path):
            logger.error(f"Cannot process corrupt video: {input_video_path}")
            raise ValueError(f"Cannot process corrupt video: {input_video_path}")

        # Check duration using ffprobe with threshold
        duration_result = get_video_duration(
            input_video_path, delete=False, threshold=min_duration
        )
        if duration_result is False:
            # Duration is below threshold
            if delete:
                try:
                    os.remove(input_video_path)
                    logger.info(
                        f"Deleted video due to short duration: {input_video_path}"
                    )
                except OSError as e:
                    logger.error(f"Failed to delete video file {input_video_path}: {e}")
            return "duration_invalid"

        # Check FPS
        try:
            fps = verify_video_fps(
                input_video_path, target_fps=None
            )  # Just get FPS without validation
            if fps < min_fps:
                logger.warning(
                    f"Video FPS too low ({fps:.2f} < {min_fps}): {input_video_path}"
                )
                return "fps_invalid"
        except Exception as e:
            logger.error(f"Error checking video FPS: {e}")
            return "fps_invalid"

        # Read frames using existing function
        video_frames = load_video_with_decord(input_video_path)
        return video_frames

    except Exception as e:
        logger.exception(f"Error reading video: {e}")
        raise
        raise


def merge_audio_into_video(
    input_video_path: str, input_audio_path: str, output_path: Optional[str] = None
) -> str:
    """
    Merge audio and video using FFMPEG.

    Args:
        input_video_path (str): Path to the input video file
        input_audio_path (str): Path to the input audio file
        output_path (str, optional): Path for the output video file.
            If None, a path will be generated based on the input paths.

    Returns:
        str: Path to the merged video file
    """
    # Check if files exist
    if not os.path.exists(input_video_path):
        raise FileNotFoundError(f"Video file not found: {input_video_path}")
    if not os.path.exists(input_audio_path):
        raise FileNotFoundError(f"Audio file not found: {input_audio_path}")

    # Generate output path if not provided
    if output_path is None:
        video_dir = os.path.dirname(input_video_path)
        video_name = os.path.splitext(os.path.basename(input_video_path))[0]
        audio_name = os.path.splitext(os.path.basename(input_audio_path))[0]
        output_path = os.path.join(
            video_dir, f"{video_name}_merged_with_{audio_name}.mp4"
        )

    # Ensure output directory exists
    os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

    try:
        # Check if ffmpeg is installed
        check_ffmpeg_installed()

        # Merge audio and video
        command = [
            "ffmpeg",
            "-y",  # Overwrite output file if it exists
            "-loglevel",
            "error",
            "-i",
            input_video_path,  # Video input
            "-i",
            input_audio_path,  # Audio input
            "-c:v",
            "copy",  # Copy video codec
            "-c:a",
            "aac",  # AAC audio codec
            "-map",
            "0:v",  # Use video from first input
            "-map",
            "1:a",  # Use audio from second input
            "-shortest",  # Finish encoding when the shortest input stream ends
            output_path,
        ]

        subprocess.run(command, check=True)
        logger.info(f"Successfully merged video and audio: {output_path}")
        return output_path

    except subprocess.CalledProcessError as e:
        logger.error(f"FFMPEG error merging audio and video: {e}")
        raise RuntimeError(f"Failed to merge audio and video: {e}")
    except Exception as e:
        logger.exception(f"Error merging audio and video: {e}")
        raise


def resample_video_fps(
    input_video_path: str,
    target_fps: int = 25,
    audio_sample_rate: int = 16000,
    output_path: Optional[str] = None,
) -> str:
    """
    Resample video to target FPS and audio to target sample rate if needed.

    Args:
        input_video_path (str): Path to the input video file
        target_fps (int, optional): Target frame rate. Defaults to 25.
        audio_sample_rate (int, optional): Target audio sample rate in Hz. Defaults to 16000.
        output_path (str, optional): Path for the output video file.
            If None, a path will be generated based on the input path.

    Returns:
        str: Path to the resampled video file, or the original path if no resampling was needed

    Raises:
        FileNotFoundError: If input video file is not found
        RuntimeError: If FFmpeg resampling fails
        ValueError: If target_fps or audio_sample_rate is invalid
    """
    # Check if input file exists
    if not os.path.exists(input_video_path):
        raise FileNotFoundError(f"Video file not found: {input_video_path}")

    # Check for valid parameters
    if target_fps <= 0:
        raise ValueError(f"Invalid target FPS: {target_fps}")
    if audio_sample_rate <= 0:
        raise ValueError(f"Invalid audio sample rate: {audio_sample_rate}")

    # Generate output path if not provided
    if output_path is None:
        video_dir = os.path.dirname(input_video_path)
        video_name = os.path.splitext(os.path.basename(input_video_path))[0]
        video_ext = os.path.splitext(input_video_path)[1]
        output_path = os.path.join(
            video_dir, f"{video_name}_fps{target_fps}{video_ext}"
        )

    # Ensure output directory exists (do this only once)
    os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

    # Check if ffmpeg is installed
    check_ffmpeg_installed()

    # Get current FPS
    # cap = cv2.VideoCapture(input_video_path)
    # current_fps = cap.get(cv2.CAP_PROP_FPS)
    # cap.release()

    # Get current FPS and audio sample rate using ffprobe
    # command = [
    #     "ffprobe",
    #     "-v", "error",
    #     "-select_streams", "v:0",
    #     "-show_entries", "stream=r_frame_rate",
    #     "-select_streams", "a:0",
    #     "-show_entries", "stream=sample_rate",
    #     "-of", "json",
    #     input_video_path
    # ]
    command = [
        "ffprobe",
        "-v",
        "error",
        "-show_entries",
        "stream=r_frame_rate,sample_rate",
        "-of",
        "json",
        input_video_path,
    ]

    try:
        output = subprocess.check_output(command, universal_newlines=True)
        data = json.loads(output)

        # Parse video FPS
        fps_str = data.get("streams", [{}])[0].get("r_frame_rate", "")
        if fps_str:
            num, den = map(int, fps_str.split("/"))
            current_fps = num / den if den != 0 else 0
        else:
            raise ValueError("Could not determine video FPS")

        # Parse audio sample rate
        audio_stream = data.get("streams", [])
        sample_rate = (
            int(audio_stream[1].get("sample_rate", 0)) if len(audio_stream) > 1 else 0
        )

        # Determine if resampling is needed based on FPS
        needs_resampling = (
            abs(current_fps - target_fps) > 0.01 or sample_rate != audio_sample_rate
        )

        try:
            if not needs_resampling:
                # If FPS already matches target, just copy the file
                logger.info(
                    f"Video already at target FPS ({target_fps}) and sample rate ({sample_rate}), copying to output path"
                )
                shutil.copy2(input_video_path, output_path)
            else:
                # FPS doesn't match, need to resample
                logger.info(
                    f"Resampling video from {current_fps:.2f} to {target_fps} FPS"
                )
                logger.info(
                    f"Resampling audio from {sample_rate} to {audio_sample_rate} Hz"
                )

                # Base command with resampling parameters
                command = [
                    "ffmpeg",
                    "-y",
                    "-loglevel",
                    "error",
                    "-i",
                    input_video_path,
                    "-vf",
                    f"fps={target_fps}",  # Set output FPS
                    "-c:v",
                    "libx264",  # Video codec
                    "-preset",
                    "medium",  # Encoding preset
                    "-crf",
                    "18",  # Quality (lower = better)
                    "-c:a",
                    "aac",  # Audio codec
                    "-ar",
                    str(audio_sample_rate),  # Audio sample rate
                    "-q:a",
                    "0",  # Best audio quality
                    output_path,
                ]

                # Execute the command
                subprocess.run(command, check=True)
                logger.info(
                    f"Successfully resampled video to {target_fps} FPS: {output_path}"
                )

            return output_path

        except subprocess.CalledProcessError as e:
            logger.error(f"FFMPEG error resampling video: {e}")
            raise RuntimeError(f"Failed to resample video: {e}")
        except Exception as e:
            logger.exception(f"Error resampling video: {e}")
            raise

    except (subprocess.CalledProcessError, json.JSONDecodeError) as e:
        raise RuntimeError(f"Error analyzing video with ffprobe: {str(e)}")
    except ValueError as e:
        raise RuntimeError(f"Error parsing video info: {str(e)}")
    except Exception as e:
        raise RuntimeError(f"Unexpected error during video analysis: {str(e)}")


def save_video_frames(
    video_output_path: str, video_frames: np.ndarray, fps: int
) -> str:
    """
    Write video frames to a file using FFmpeg for highest quality and compatibility.

    Args:
        video_output_path (str): Path where the video should be saved
        video_frames (np.ndarray): Array of frames to write
        fps (int): Frames per second for the output video

    Returns:
        str: Path to the saved video file

    Raises:
        RuntimeError: If FFmpeg encoding fails
        OSError: If temp directory operations fail
    """
    try:
        # Ensure FFmpeg is installed
        check_ffmpeg_installed()

        # Create temp directory for frame storage
        with tempfile.TemporaryDirectory() as temp_dir:
            # Save frames as PNG files
            for i, frame in enumerate(video_frames):
                frame_path = os.path.join(temp_dir, f"frame_{i:04d}.png")
                # Save as PNG (lossless)
                cv2.imwrite(frame_path, cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))

            # Ensure output directory exists
            os.makedirs(
                os.path.dirname(os.path.abspath(video_output_path)), exist_ok=True
            )

            # Construct FFmpeg command with high quality settings
            command = [
                "ffmpeg",
                "-y",  # Overwrite output if exists
                "-framerate",
                str(fps),
                "-i",
                os.path.join(temp_dir, "frame_%04d.png"),
                "-c:v",
                "libx264",  # H.264 codec
                "-preset",
                "slow",  # Slower encoding = better compression
                "-crf",
                "18",  # High quality (0-51, lower = better)
                "-pix_fmt",
                "yuv420p",  # Widely compatible pixel format
                video_output_path,
            ]

            # Run FFmpeg
            result = subprocess.run(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=False,
            )

            if result.returncode != 0:
                raise RuntimeError(f"FFmpeg encoding failed: {result.stderr}")

            logger.info(
                f"Video successfully written to {video_output_path} using FFmpeg"
            )
            return video_output_path

    except Exception as e:
        logger.exception(f"Error writing video: {e}")
        if os.path.exists(video_output_path):
            try:
                os.remove(video_output_path)
                logger.info(f"Cleaned up incomplete video file: {video_output_path}")
            except OSError:
                pass
        raise


def save_video_grid_as_gif(
    videos: torch.Tensor,
    path: str,
    rescale: bool = False,
    n_rows: int = 6,
    fps: int = 8,
) -> str:
    """
    Save a grid of videos as an animated GIF.

    Args:
        videos (torch.Tensor): Videos tensor with shape [batch, channels, frames, height, width]
        path (str): Path to save the output GIF
        rescale (bool, optional): Whether to rescale pixel values from [-1,1] to [0,1]. Defaults to False.
        n_rows (int, optional): Number of rows in the grid. Defaults to 6.
        fps (int, optional): Frames per second for the output GIF. Defaults to 8.

    Returns:
        str: Path to the saved GIF file
    """
    # Rearrange videos to put frames first
    videos = rearrange(videos, "b c f h w -> f b c h w")
    outputs = []

    # Process each frame
    for x in videos:
        # Create a grid for this frame
        x = torchvision.utils.make_grid(x, nrow=n_rows)
        x = x.transpose(0, 1).transpose(1, 2).squeeze(-1)

        # Rescale if requested
        if rescale:
            x = (x + 1.0) / 2.0  # -1,1 -> 0,1

        # Convert to uint8
        x = (x * 255).numpy().astype(np.uint8)
        outputs.append(x)

    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(os.path.abspath(path)), exist_ok=True)

    # Save as GIF
    imageio.mimsave(path, outputs, fps=fps)
    logger.info(f"Saved video grid to {path}")

    return path


def load_video_with_decord(
    video_path: str, return_fps: bool = False
) -> Union[np.ndarray, Tuple[np.ndarray, float]]:
    """
    Read video frames using Decord library.

    Args:
        video_path (str): Path to the video file
        return_fps (bool, optional): Whether to return the FPS. Defaults to False.

    Returns:
        Union[np.ndarray, Tuple[np.ndarray, float]]:
            - numpy array of frames if return_fps is False
            - tuple of (frames, fps) if return_fps is True
    """
    try:
        vr = VideoReader(video_path)
        fps = vr.get_avg_fps()
        video_frames = vr[:].asnumpy()
        vr.seek(0)

        if return_fps:
            return video_frames, fps
        else:
            return video_frames

    except Exception as e:
        logger.exception(f"Error reading video with Decord: {e}")
        # Fall back to OpenCV if Decord fails
        logger.info("Falling back to OpenCV for video reading")
        return load_video_with_opencv(video_path, return_fps)


def load_video_with_opencv(
    video_path: str, return_fps: bool = False
) -> Union[np.ndarray, Tuple[np.ndarray, float]]:
    """
    Read video frames using OpenCV.

    Args:
        video_path (str): Path to the video file
        return_fps (bool, optional): Whether to return the FPS. Defaults to False.

    Returns:
        Union[np.ndarray, Tuple[np.ndarray, float]]:
            - numpy array of frames if return_fps is False
            - tuple of (frames, fps) if return_fps is True
    """
    # Open the video file
    cap = cv2.VideoCapture(video_path)
    fps = cap.get(cv2.CAP_PROP_FPS)

    # Check if the video was opened successfully
    if not cap.isOpened():
        logger.error(f"Error: Could not open video: {video_path}")
        return np.array([])

    frames = []

    while True:
        # Read a frame
        ret, frame = cap.read()

        # If frame is read correctly ret is True
        if not ret:
            break

        # Convert BGR to RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        frames.append(frame_rgb)

    # Release the video capture object
    cap.release()

    if return_fps:
        return np.array(frames), fps
    else:
        return np.array(frames)


def read_video(video_path: str, change_fps=True, use_decord=True):
    if change_fps:
        temp_dir = "temp"
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        os.makedirs(temp_dir, exist_ok=True)
        command = f"ffmpeg -loglevel error -y -nostdin -i {video_path} -r 25 -crf 18 {os.path.join(temp_dir, 'video.mp4')}"
        subprocess.run(command, shell=True)
        target_video_path = os.path.join(temp_dir, "video.mp4")
    else:
        target_video_path = video_path

    if use_decord:
        return read_video_decord(target_video_path)
    else:
        return read_video_cv2(target_video_path)


def read_video_decord(video_path: str):
    vr = VideoReader(video_path)
    video_frames = vr[:].asnumpy()
    vr.seek(0)
    return video_frames


def read_video_cv2(video_path: str):
    # Open the video file
    cap = cv2.VideoCapture(video_path)

    # Check if the video was opened successfully
    if not cap.isOpened():
        print("Error: Could not open video.")
        return np.array([])

    frames = []

    while True:
        # Read a frame
        ret, frame = cap.read()

        # If frame is read correctly ret is True
        if not ret:
            break

        # Convert BGR to RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        frames.append(frame_rgb)

    # Release the video capture object
    cap.release()

    return np.array(frames)


def write_video(video_output_path: str, video_frames: np.ndarray, fps: int):
    height, width = video_frames[0].shape[:2]
    out = cv2.VideoWriter(
        video_output_path, cv2.VideoWriter_fourcc(*"mp4v"), fps, (width, height)
    )
    for frame in video_frames:
        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
        out.write(frame)
    out.release()


def write_video_ffmpeg(video_output_path: str, video_frames: np.ndarray, fps: int):
    # Create a temporary directory to store the frames
    temp_dir = tempfile.mkdtemp()

    # Save each frame as a PNG file
    for i, frame in enumerate(video_frames):
        frame_path = os.path.join(temp_dir, f"frame_{i:04d}.png")
        logger.debug(
            f"Processing frame {i}: type: {type(frame)}, shape: {frame.shape if hasattr(frame, 'shape') else 'N/A'}, dtype: {frame.dtype if hasattr(frame, 'dtype') else 'N/A'}"
        )
        cv2.imwrite(frame_path, cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))

    # Construct the ffmpeg command
    command = [
        "ffmpeg",
        "-y",  # Overwrite output file if it exists
        "-framerate",
        str(fps),
        "-i",
        os.path.join(temp_dir, "frame_%04d.png"),
        "-c:v",
        "libx264",  # Video codec
        "-preset",
        "medium",  # Encoding preset
        "-crf",
        "18",  # Quality (lower = better)
        "-pix_fmt",
        "yuv420p",  # Widely compatible pixel format
        video_output_path,
    ]

    # Run the ffmpeg command
    subprocess.run(command, check=True)

    # Clean up temporary files
    shutil.rmtree(temp_dir)
