from kornia.geometry.transform import get_perspective_transform, warp_perspective
import torch
import numpy as np
import einops
from scipy.signal import savgol_filter
import cv2
from decord import cpu, VideoReader
from types import GeneratorType
import subprocess
import os
import json


def square_pad_bboxes(bboxes: np.n<PERSON><PERSON>) -> np.ndarray:
    """
    Convert rectangular bounding boxes to squares while preserving center coordinates.

    Args:
        bboxes: Input bounding boxes in format (x1, y1, x2, y2).
                 Can be either:
                 - 2D array of shape (N, 4) for multiple boxes
                 - 1D array of shape (4,) for single box

    Returns:
        Squared bounding boxes in same format as input, maintaining original dimensions
    """
    # Ensure 2D array for consistent processing
    if bboxes.ndim == 1:
        bboxes = bboxes.reshape(1, -1)

    x1, y1, x2, y2 = bboxes[:, 0], bboxes[:, 1], bboxes[:, 2], bboxes[:, 3]
    widths = x2 - x1
    heights = y2 - y1

    # Find maximum dimension for each box
    max_sides = np.maximum(widths, heights)

    # Calculate new coordinates while preserving centers
    new_x1 = x1 + (widths - max_sides) / 2
    new_y1 = y1 + (heights - max_sides) / 2
    new_x2 = new_x1 + max_sides
    new_y2 = new_y1 + max_sides

    squared_boxes = np.stack([new_x1, new_y1, new_x2, new_y2], axis=1)

    return squared_boxes


def _get_transforms(bboxes: np.array):
    x1, y1, x2, y2 = bboxes[:, 0], bboxes[:, 1], bboxes[:, 2], bboxes[:, 3]

    # Shape: (N, 4, 2)
    corners = np.stack(
        [
            np.stack([x1, y1], axis=1),  # top-left
            np.stack([x2, y1], axis=1),  # top-right
            np.stack([x2, y2], axis=1),  # bottom-right
            np.stack([x1, y2], axis=1),  # bottom-left
        ],
        axis=1,
    )

    src = torch.tensor(corners, dtype=torch.float32)

    # New 384x384 centered in 512x512
    x1_dest, y1_dest = 64.0, 64.0
    x2_dest, y2_dest = 448.0, 448.0

    dest = torch.tensor(
        [
            [x1_dest, y1_dest],  # top-left
            [x2_dest, y1_dest],  # top-right
            [x2_dest, y2_dest],  # bottom-right
            [x1_dest, y2_dest],  # bottom-left
        ],
        dtype=torch.float32,
    ).repeat(src.shape[0], 1, 1)

    transforms = get_perspective_transform(points_src=src, points_dst=dest)

    return transforms


def _get_frames(video: str, frameindices: GeneratorType, num_threads: int = 0):
    video_reader = VideoReader(uri=video, num_threads=num_threads)
    frames = video_reader.get_batch(frameindices)
    tensor = torch.tensor(frames.asnumpy(), dtype=torch.float32)
    rearranged_frames = einops.rearrange(tensor, "b h w c -> b c h w")
    return rearranged_frames


def _frame_generator(frame_indices: np.array, batch_size: int = 16):
    """Generate batches of frame indices for processing"""
    num_frames = len(frame_indices)
    index = 0

    while index < num_frames:
        end_index = min(index + batch_size, num_frames)
        yield frame_indices[index:end_index].tolist()
        index = end_index


def _bbox_generator(bboxes: np.array, batch_size: int = 16):
    num_bboxes = len(bboxes)
    index = 0

    while index < num_bboxes:
        end_index = min(index + batch_size, num_bboxes)
        yield bboxes[index:end_index]
        index = end_index


class VideoWriter:
    def __init__(self, output_path, width=512, height=512, fps=30, codec="libx264"):
        self.output_path = output_path
        self.width = width
        self.height = height
        self.fps = fps
        self.codec = codec
        self.process = None

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.process:
            try:
                self.process.stdin.close()
                self.process.wait()
            except:
                pass

    def start_ffmpeg_pipe(self):
        """Start FFmpeg process with stdin pipe for raw video data"""
        cmd = [
            "ffmpeg",
            "-y",  # Overwrite output file
            "-f",
            "rawvideo",
            "-vcodec",
            "rawvideo",
            "-s",
            f"{self.width}x{self.height}",
            "-pix_fmt",
            "rgb24",
            "-r",
            str(self.fps),
            "-i",
            "-",  # Read from stdin
            "-c:v",
            self.codec,
            "-pix_fmt",
            "yuv420p",
            "-preset",
            "fast",
            self.output_path,
        ]

        self.process = subprocess.Popen(
            cmd, stdin=subprocess.PIPE, stderr=subprocess.PIPE
        )
        return self.process

    def write_frames_to_pipe(self, frames):
        """Write frames directly to FFmpeg stdin"""
        if not self.process:
            self.start_ffmpeg_pipe()

        for frame in frames:
            if frame.shape[:2] != (self.height, self.width):
                frame = cv2.resize(frame, (self.width, self.height))

            # Ensure frame is RGB and uint8
            if frame.dtype != np.uint8:
                frame = frame.astype(np.uint8)

            try:
                self.process.stdin.write(frame.tobytes())
            except (BrokenPipeError, OSError):
                print("FFmpeg pipe closed unexpectedly")
                break


def get_video_info(video_path):
    """Get video information including fps and frame count"""
    cmd = [
        "ffprobe",
        "-v",
        "quiet",
        "-print_format",
        "json",
        "-show_format",
        "-show_streams",
        video_path,
    ]
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode != 0:
        raise RuntimeError(f"Failed to get video info: {result.stderr}")

    info = json.loads(result.stdout)

    video_stream = None
    audio_stream = None

    for stream in info["streams"]:
        if stream["codec_type"] == "video" and video_stream is None:
            video_stream = stream
        elif stream["codec_type"] == "audio" and audio_stream is None:
            audio_stream = stream

    fps = eval(video_stream["r_frame_rate"]) if video_stream else 30
    has_audio = audio_stream is not None

    return {
        "fps": fps,
        "has_audio": has_audio,
        "duration": float(info["format"]["duration"])
        if "duration" in info["format"]
        else None,
    }


def process_video(
    video: str,
    frame_indices: np.array,
    bboxes: np.array,
    batch_size: int,
    output_path: str,
    padding_mode: str = "zeros",
    fill_value: float = 0.0,
):
    """
    Process video with warped frames for specific frame indices and write to output using FFmpeg

    Args:
        video: Path to input video
        frame_indices: Array of frame indices to process
        bboxes: Bounding boxes for each frame
        batch_size: Batch size for processing
        output_path: Path for output video
        padding_mode: Padding mode for warp_perspective ('zeros', 'border', 'reflection')
        fill_value: Fill value when using 'zeros' padding mode (0.0 for black, 255.0 for white)
    """

    # Get video info for fps and audio
    video_info = get_video_info(video)

    # Use frame_indices instead of processing entire video
    frames_generator = _frame_generator(
        frame_indices=frame_indices, batch_size=batch_size
    )
    bboxes_generator = _bbox_generator(bboxes=bboxes, batch_size=batch_size)

    # Create temporary video without audio
    temp_video = output_path.replace(".mp4", "_temp_no_audio.mp4")

    with VideoWriter(temp_video, fps=video_info["fps"]) as writer:
        writer.start_ffmpeg_pipe()

        batch_count = 0
        for frames, batch_bboxes in zip(frames_generator, bboxes_generator):
            # Process frames - now using specific frame indices
            processed_frames = _get_frames(video, frames)
            processed_transforms = _get_transforms(batch_bboxes)

            # Use the specified padding mode and fill value
            warp = warp_perspective(
                processed_frames,
                processed_transforms,
                dsize=(512, 512),
                padding_mode="fill",
                fill_value=torch.tensor([127, 127, 127]),
            )
            warp = einops.rearrange(warp, "b c h w -> b h w c")
            warp = warp.numpy().astype(np.uint8)

            # Write frames directly to FFmpeg
            writer.write_frames_to_pipe(warp)

            batch_count += 1
            print(f"Processed batch {batch_count}")

    # Add audio back if original video had audio
    if video_info["has_audio"]:
        print("Adding audio from original video...")

        # Calculate time range for this segment
        start_time = frame_indices[0] / video_info["fps"]
        duration = len(frame_indices) / video_info["fps"]

        cmd = [
            "ffmpeg",
            "-y",
            "-i",
            temp_video,  # Video input (no audio)
            "-ss",
            str(start_time),  # Start time for audio
            "-t",
            str(duration),  # Duration for audio
            "-i",
            video,  # Original video (for audio)
            "-c:v",
            "copy",  # Copy video stream
            "-c:a",
            "aac",  # Encode audio
            "-map",
            "0:v:0",  # Map video from first input
            "-map",
            "1:a:0",  # Map audio from second input
            "-shortest",  # End when shortest stream ends
            output_path,
        ]

        result = subprocess.run(cmd, capture_output=True)
        if result.returncode != 0:
            print(f"Warning: Failed to add audio: {result.stderr.decode()}")
            print("Using video without audio...")
            os.rename(temp_video, output_path)
        else:
            # Clean up temporary file
            os.remove(temp_video)
            print("Audio added successfully!")
    else:
        # No audio in original, just rename temp file
        os.rename(temp_video, output_path)
        print("No audio in original video.")

    print(f"Video processing complete. Output saved to: {output_path}")


def smoothen_and_pad(bboxes: np.array):
    bboxes = savgol_filter(bboxes, window_length=25, polyorder=3, axis=0, mode="mirror")
    bboxes = square_pad_bboxes(bboxes)
    return bboxes
