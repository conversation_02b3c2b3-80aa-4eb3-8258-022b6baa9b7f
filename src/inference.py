"""
Inference module for lip-sync video generation.

This module provides the main inference functionality for generating lip-synced
videos using a diffusion-based pipeline with audio conditioning.
"""

import argparse
import os

import torch
from accelerate.utils import set_seed
from diffusers import AutoencoderKL, DDIMScheduler
from omegaconf import OmegaConf

from src.modules.hotdub.models.unet import UNet3DConditionModel
from src.modules.hotdub.pipelines.lipsync_pipeline import LipsyncPipeline
from src.modules.hotdub.whisper.audio2feature import Audio2Feature


def main(config, args):
    """
    Main inference function for lip-sync video generation.

    Args:
        config: Configuration object containing model and data parameters
        args: Command line arguments containing input/output paths and inference settings

    Raises:
        RuntimeError: If input video or audio files are not found
        NotImplementedError: If cross_attention_dim is not 768 or 384

    Note:
        This function sets up the complete inference pipeline including:
        - Audio encoder (Whisper-based)
        - VAE for video encoding/decoding
        - UNet for denoising
        - DDIM scheduler for inference
        Then runs the lip-sync generation process.
    """
    if not os.path.exists(args.video_path):
        raise RuntimeError(f"Video path '{args.video_path}' not found")
    if not os.path.exists(args.audio_path):
        raise RuntimeError(f"Audio path '{args.audio_path}' not found")
    output_dir = os.path.dirname(args.video_out_path)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)

    # Check if the GPU supports float16
    is_fp16_supported = (
        torch.cuda.is_available() and torch.cuda.get_device_capability()[0] > 7
    )
    dtype = torch.float16 if is_fp16_supported else torch.float32

    print(f"Input video path: {args.video_path}")
    print(f"Input audio path: {args.audio_path}")
    print(f"Loaded checkpoint path: {args.inference_ckpt_path}")

    scheduler = DDIMScheduler.from_pretrained("src/configs")

    if config.model.cross_attention_dim == 768:
        whisper_model_path = config.ckpt.whisper_768
    elif config.model.cross_attention_dim == 384:
        whisper_model_path = config.ckpt.whisper_384
    else:
        raise NotImplementedError("cross_attention_dim must be 768 or 384")

    audio_encoder = Audio2Feature(
        model_path=whisper_model_path,
        device="cuda",
        num_frames=config.data.num_frames,
        audio_feat_length=config.data.audio_feat_length,
    )

    vae = AutoencoderKL.from_pretrained("stabilityai/sd-vae-ft-mse", torch_dtype=dtype)
    vae.config.scaling_factor = 0.18215
    vae.config.shift_factor = 0

    denoising_unet, _ = UNet3DConditionModel.from_pretrained(
        OmegaConf.to_container(config.model),
        args.inference_ckpt_path,
        device="cpu",
    )

    denoising_unet = denoising_unet.to(dtype=dtype)

    pipeline = LipsyncPipeline(
        vae=vae,
        audio_encoder=audio_encoder,
        denoising_unet=denoising_unet,
        scheduler=scheduler,
    ).to("cuda")

    if args.seed != -1:
        set_seed(args.seed)
    else:
        torch.seed()

    print(f"Initial seed: {torch.initial_seed()}")

    pipeline(
        video_path=args.video_path,
        audio_path=args.audio_path,
        video_out_path=args.video_out_path,
        video_mask_path=args.video_out_path.replace(".mp4", "_mask.mp4"),
        num_frames=config.data.num_frames,
        num_inference_steps=args.inference_steps,
        guidance_scale=args.guidance_scale,
        weight_dtype=dtype,
        width=config.data.resolution,
        height=config.data.resolution,
        mask_type=config.run.mask_type,
        mask_path=config.run.mask_path,
        reference_contiguity=config.data.reference_contiguity,
        reference_selection_mode=config.data.reference_selection_mode,
        reference_adulteration_idx=config.data.reference_adulteration_idx,
        smooth_type=config.run.smooth_type,
        face_detector=config.run.face_detector,
        image_processor_config=config.data.image_processor,
        adulteration=config.run.unmasked_adulteration,
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--unet_config_path", type=str, default="configs/unet.yaml")
    parser.add_argument("--inference_ckpt_path", type=str, required=True)
    parser.add_argument("--video_path", type=str, required=True)
    parser.add_argument("--audio_path", type=str, required=True)
    parser.add_argument("--video_out_path", type=str, required=True)
    parser.add_argument("--inference_steps", type=int, default=20)
    parser.add_argument("--guidance_scale", type=float, default=1.0)
    parser.add_argument("--seed", type=int, default=1247)
    args = parser.parse_args()

    config = OmegaConf.load(args.unet_config_path)

    main(config, args)
