# Copyright (c) 2024 Bytedance Ltd. and/or its affiliates
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Audio-video synchronization correction module.

This module provides functionality to detect and correct audio-video synchronization
issues in videos using SyncNet models. It can process videos in parallel across
multiple GPUs to efficiently correct sync offsets.
"""

import os
import tqdm
from src.models.syncnet.syncnet_eval import SyncNetEval
from src.models.syncnet.syncnet_detect import SyncNetDetector
from src.models.syncnet.eval_sync_conf import syncnet_eval
from src.utils.logger import configure_logger
import torch
import subprocess
import shutil
from multiprocessing import Process
import warnings

# 1) Ignore only that exact message (using a regex):
warnings.filterwarnings(
    "ignore",
    category=UserWarning,
    message=r".*output with one or more elements was resized.*",
)

logger = configure_logger(__name__)

paths = []


def gather_paths(input_dir, output_dir):
    """
    Recursively gather video paths from input directory for sync correction.

    Args:
        input_dir (str): Directory containing input videos
        output_dir (str): Directory where corrected videos will be saved

    Note:
        This function modifies the global 'paths' list by appending tuples of
        (input_path, output_path) for videos that don't already exist in output.
    """
    for video in tqdm.tqdm(sorted(os.listdir(input_dir))):
        if video.endswith(".mp4"):
            video_input = os.path.join(input_dir, video)
            video_output = os.path.join(output_dir, video)
            if os.path.isfile(video_output):
                continue
            paths.append((video_input, video_output))
        elif os.path.isdir(os.path.join(input_dir, video)):
            gather_paths(
                os.path.join(input_dir, video), os.path.join(output_dir, video)
            )


def adjust_offset(video_input: str, video_output: str, av_offset: int, fps: int = 25):
    """
    Adjust audio-video synchronization offset using FFmpeg.

    Args:
        video_input (str): Path to input video file
        video_output (str): Path to output video file
        av_offset (int): Audio-video offset in frames
        fps (int): Video frame rate (default: 25)

    Note:
        Uses FFmpeg's itsoffset parameter to shift audio timing relative to video.
        Positive offset delays audio, negative offset advances audio.
    """
    command = f"ffmpeg -loglevel error -y -i {video_input} -itsoffset {av_offset / fps} -i {video_input} -map 0:v -map 1:a -c copy -q:v 0 -q:a 0 {video_output}"
    subprocess.run(command, shell=True)


def func(sync_conf_threshold, paths, device_id, process_temp_dir):
    """
    Worker function for processing videos on a specific GPU device.

    Args:
        sync_conf_threshold (float): Minimum confidence threshold for sync correction
        paths (list): List of (input_path, output_path) tuples to process
        device_id (int): GPU device ID to use
        process_temp_dir (str): Temporary directory for this process

    Note:
        This function evaluates sync confidence and offset for each video.
        Videos with confidence >= threshold and offset <= 6 frames are corrected.
    """
    os.makedirs(process_temp_dir, exist_ok=True)
    device = f"cuda:{device_id}"

    syncnet = SyncNetEval(device=device)
    syncnet.loadParameters("src/models/pretrained/syncnet_v2.model")

    detect_results_dir = os.path.join(process_temp_dir, "detect_results")
    syncnet_eval_results_dir = os.path.join(process_temp_dir, "syncnet_eval_results")

    syncnet_detector = SyncNetDetector(
        device=device, detect_results_dir=detect_results_dir
    )

    for video_input, video_output in paths:
        try:
            av_offset, conf = syncnet_eval(
                syncnet,
                syncnet_detector,
                video_input,
                syncnet_eval_results_dir,
                detect_results_dir,
            )

            logger.info(f"Offset: {av_offset}, Confidence: {conf}")
            logger.info(f"Confidence Threshold: {sync_conf_threshold}")

            if conf >= sync_conf_threshold and abs(av_offset) <= 6:
                os.makedirs(os.path.dirname(video_output), exist_ok=True)
                if av_offset == 0:
                    shutil.copy(video_input, video_output)
                else:
                    adjust_offset(video_input, video_output, av_offset)
            else:
                logger.info("Rejecting video")
        except Exception as e:
            print(e)


def split(a, n):
    """
    Split a list into n roughly equal parts.

    Args:
        a (list): List to split
        n (int): Number of parts to split into

    Returns:
        generator: Generator yielding n sublists
    """
    k, m = divmod(len(a), n)
    return (a[i * k + min(i, m) : (i + 1) * k + min(i + 1, m)] for i in range(n))


def sync_av_multi_gpus(
    input_dir, output_dir, temp_dir, num_workers, sync_conf_threshold
):
    """
    Correct audio-video synchronization using multiple GPUs.

    Args:
        input_dir (str): Directory containing input videos
        output_dir (str): Directory to save sync-corrected videos
        temp_dir (str): Temporary directory for intermediate files
        num_workers (int): Number of worker processes per GPU
        sync_conf_threshold (float): Minimum confidence threshold for sync correction

    Raises:
        RuntimeError: If no GPUs are found

    Note:
        This function coordinates multi-GPU processing by splitting video paths
        among available GPUs and spawning worker processes for each. Only videos
        with sync confidence >= threshold and offset <= 6 frames are corrected.
    """
    gather_paths(input_dir, output_dir)
    num_devices = torch.cuda.device_count()
    if num_devices == 0:
        raise RuntimeError("No GPUs found")
    split_paths = list(split(paths, num_workers * num_devices))
    processes = []

    for i in range(num_devices):
        for j in range(num_workers):
            process_index = i * num_workers + j
            process = Process(
                target=func,
                args=(
                    sync_conf_threshold,
                    split_paths[process_index],
                    i,
                    os.path.join(temp_dir, f"process_{process_index}"),
                ),
            )
            process.start()
            processes.append(process)

    for process in processes:
        process.join()


if __name__ == "__main__":
    input_dir = "Test_Videos/6_VALID"
    output_dir = "Test_videos/7_SYNC_CORRECTED"
    temp_dir = "temp"
    num_workers = 1  # How many processes per device
    sync_conf_threshold = 3

    sync_av_multi_gpus(
        input_dir, output_dir, temp_dir, num_workers, sync_conf_threshold
    )
