# Model Checkpoint Configuration for HotDub-Preprocess (Hugging Face Hub)

models:
  s3fd:
    repo_id: "Hyathi/Preprocess"
    filename: "sfd_face.pth"
    local_path: "src/models/pretrained/faceDetector/s3fd/sfd_face.pth" 
    description: "S3FD Face Detection Model from Hugging Face Hub"

  pretrain_TalkSet:
    repo_id: "Hyathi/Preprocess"
    filename: "pretrain_TalkSet.model"
    local_path: "src/models/pretrained/talknet/pretrain_TalkSet.model"
    description: "TalkNet Model for Active Speaker Detection from Hugging Face Hub"

  face_landmarker:
    repo_id: "Hyathi/Preprocess"
    filename: "face_landmarker.task"
    local_path: "src/models/pretrained/faceDetector/face_landmarker.task"
    description: "face_landmarker Model for face landmark Detection from Hugging Face Hub"

  koniq_pretrained:
    repo_id: "Hyathi/Preprocess"
    filename: "koniq_pretrained.pkl"
    local_path: "src/models/pretrained/koniq_pretrained.pkl"
    description: "Pretrained model for quality assessment from Hugging Face Hub"
  
  syncnet_v2:
    repo_id: "Hyathi/Preprocess"
    filename: "syncnet_v2.model"
    local_path: "src/models/pretrained/syncnet_v2.model"
    description: "Pretrained model for video synchronization from Hugging Face Hub"
