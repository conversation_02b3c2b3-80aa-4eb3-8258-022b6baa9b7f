log:
  use_wandb: true
  
  # Project Identifiers
  project_name: HotDub-Diffusion
  exp_name: Exp-14-BaseExp6-ConstLR-NewAffine
  
  # Wandb watch mode settings
  enable_watch: false
  watch_log_type: gradients
  watch_log_interval: 100
  
  # Wandb log settings
  log_interval: 1
  media_log_interval: 100

data:
  # Image processor settings
  image_processor:
    # Padding configuration for face detection and masking
    padding:
      # Controls the expansion of the face bounding box
      bbox: 0.1
      box_height_ratio: 1

      insightface_pad_factor: 0.5
      
      # MediaPipe-specific padding for stability
      mediapipe: 0.05
      
      # Face Alignment padding for detection robustness
      face_alignment: 0.05
      
      # Controls vertical face coverage in advanced mask mode
      # - Higher values (e.g., 1.0) include more of lower face
      # - Lower values (e.g., 0.8) focus more on central features
      # - Default: 0.95 provides natural face proportions
      height_ratio: 0.8
      
      # Adjusts mask top edge relative to nose position
      # - Higher values (e.g., 0.9) move mask top edge down
      # - Lower values (e.g., 0.7) move mask top edge up
      # - Default: 0.8 aligns well with typical face proportions
      nose_mid_factor: 1.0
    
    # Face template configuration for landmark alignment
    face_templates:
      type: three_point # [four_point, three_point]
      # MediaPipe detector template [left_eye, right_eye, nose]
      mediapipe:
        # Left eye position [x, y]
        # - Affects overall face alignment and symmetry
        # - x: lower = narrower face, higher = wider face
        # - y: lower = eyes higher in frame, higher = eyes lower in frame
        # - [9.0, 30.0]
        # - [25.77, 20.0]
        - [9.8, 29.3]
        
        # Right eye position [x, y]
        # - Controls face width and horizontal alignment
        # - Wider spacing between eyes = broader face detection
        # - Narrower spacing = more focused on central features
        # - [66.0, 30.0]
        # - [74.23, 20.0]
        - [90.2, 29.3]
        
        # Nose position [x, y]
        # - Determines vertical face proportions
        # - Higher y value = lower nose placement
        # - Lower y value = higher nose placement
        # - [37.5,57.5]
        # - [50.0, 50.0]
        - [50.0, 61.32]
      
      # Face Alignment detector template
      # - Generally uses tighter spacing for more precise detection
      face_alignment:
        # Left eye position - optimized for FA detection
        - [25.77, 20.0]
        
        # Right eye position - wider for better landmark coverage
        - [74.23, 20.0]
        
        # Nose position - balanced for natural proportions
        # - Lower y value (40.0 vs MediaPipe's 50.0) for better vertical alignment
        - [50.0, 50.0]

      # Mediapipe model path
      
    mediapipe_model_path: "src/models/pretrained/face_landmarker.task"

  syncnet_config_path: configs/syncnet/syncnet_16_pixel.yaml
  train_output_dir: runs # Path to save checkpoints and logs
  train_fileslist: ""
  train_data_dir: /datasets/hdtf-processed-bucket/HDTF_FINAL
  audio_cache_dir: audio_cache/whisper_new
  test_data_dir: "/datasets/vfhq-dataset/VFHQ-Test-Large"

  # Validation settings
  val_data_dir: /datasets/hdtf-processed-bucket/val/Videos  # Directory containing validation videos
  num_val_videos: 6 # Number of validation videos to process
  val_audio_dir: /datasets/hdtf-processed-bucket/val/Audios  # Optional: separate directory for validation audios
  
  # Legacy single validation video support
  val_video_path: ""
  val_audio_path: ""
  batch_size: 4 # 8
  num_workers: 11 # 11
  num_frames: 16
  resolution: 256
  mask: fix_mask
  audio_sample_rate: 16000
  video_fps: 25

ckpt:
  resume_ckpt_path: checkpoints/latentsync_unet.pt
  save_ckpt_steps: 500

  # Paths
  syncnet_model: "checkpoints/auxiliary/syncnet_v2.model"
  whisper_384: "checkpoints/whisper/tiny.pt"
  whisper_768: "checkpoints/whisper/small.pt"

  to_keep: 3

bucket:
  credentials_path: configs/bucket/credentials.yaml
  slack_webhook_url: "*********************************************************************************" # Add your Slack webhook URL here


run:
  unmasked_adulteration: true # Train with K unmasked frame randomly injected into the batches.
  adulteration_weight: 5 # Number of adulteration frames in each batch.
  adulteration_prob: 0.5 # Probability of adulteration in each batch.
  pixel_space_supervise: true
  use_syncnet: true
  sync_loss_weight: 0 # 1/283
  perceptual_loss_weight: 1.0 # 0.1
  recon_loss_weight: 1.0 # 1
  guidance_scale: 1.0 # 1.5 or 1.0
  trepa_loss_weight: 10 #10
  inference_steps: 20
  seed: 1247
  use_mixed_noise: true
  mixed_noise_alpha: 1 # 1
  mixed_precision_training: true
  vae_dtype: fp16
  enable_gradient_checkpointing: false
  enable_xformers_memory_efficient_attention: true
  max_train_steps: 6000
  max_train_epochs: -1

  # Test set evaluation settings
  test_size: 0.01
  eval_steps: 25

  mask_path: "assets/mask_ori.png"
  mask_type: fix_mask # [fix_mask, half, mouth, face, eye, advanced]
  smooth_type: laplacian # [laplacian, savgol, EMA, kalman, moving_average]
  face_detector: mediapipe # [mediapipe, face_alignment]

optimizer:
  lr: 1e-5
  scale_lr: false
  max_grad_norm: 1.0
  lr_scheduler: constant # Choose between [constant, one_cycle]
  lr_warmup_steps: 0

  # One cycle policy settings
  pct_start: 0.3
  div_factor: 25.0
  final_div_factor: 10000.0
  three_phase: false
  anneal_strategy: cos

model:
  act_fn: silu
  add_audio_layer: true
  custom_audio_layer: false
  audio_condition_method: cross_attn # Choose between [cross_attn, group_norm]
  attention_head_dim: 8
  block_out_channels: [320, 640, 1280, 1280]
  center_input_sample: false
  cross_attention_dim: 384
  down_block_types:
    [
      "CrossAttnDownBlock3D",
      "CrossAttnDownBlock3D",
      "CrossAttnDownBlock3D",
      "DownBlock3D",
    ]
  mid_block_type: UNetMidBlock3DCrossAttn
  up_block_types:
    [
      "UpBlock3D",
      "CrossAttnUpBlock3D",
      "CrossAttnUpBlock3D",
      "CrossAttnUpBlock3D",
    ]
  downsample_padding: 1
  flip_sin_to_cos: true
  freq_shift: 0
  in_channels: 13 # 49
  layers_per_block: 2
  mid_block_scale_factor: 1
  norm_eps: 1e-5
  norm_num_groups: 32
  out_channels: 4 # 16
  sample_size: 64
  resnet_time_scale_shift: default # Choose between [default, scale_shift]
  unet_use_cross_frame_attention: false
  unet_use_temporal_attention: false

  # Actually we don't use the motion module in the final version of LatentSync
  # When we started the project, we used the codebase of AnimateDiff and tried motion module
  # But the results are poor, and we decied to leave the code here for possible future usage
  use_motion_module: false
  motion_module_resolutions: [1, 2, 4, 8]
  motion_module_mid_block: false
  motion_module_decoder_only: false
  motion_module_type: Vanilla
  motion_module_kwargs:
    num_attention_heads: 8
    num_transformer_block: 1
    attention_block_types:
      - Temporal_Self
      - Temporal_Self
    temporal_position_encoding: true
    temporal_position_encoding_max_len: 16
    temporal_attention_dim_div: 1
    zero_initialize: true
