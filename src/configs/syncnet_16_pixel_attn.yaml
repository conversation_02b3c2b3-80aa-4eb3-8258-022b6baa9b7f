log:
  use_wandb: true
  
  # Project Identifiers
  project_name: SyncNet-Train-15
  exp_name: Exp-1-VFHQ
  
  # Wandb watch mode settings
  enable_watch: false
  watch_log_type: gradients
  watch_log_interval: 100
  
  # Wandb log settings
  log_interval: 1
  media_log_interval: 100

bucket:
  credentials_path: configs/bucket/credentials.yaml
  slack_webhook_url: "*********************************************************************************" # Add your Slack webhook URL here

model:
  audio_encoder: # input (1, 80, 52)
    in_channels: 1
    block_out_channels: [32, 64, 128, 256, 512, 1024, 2048]
    downsample_factors: [[2, 1], 2, 2, 1, 2, 2, [2, 3]]
    attn_blocks: [0, 0, 0, 1, 1, 0, 0]
    dropout: 0.0
  visual_encoder: # input (48, 128, 256)
    in_channels: 48
    block_out_channels: [64, 128, 256, 256, 512, 1024, 2048, 2048]
    downsample_factors: [[1, 2], 2, 2, 2, 2, 2, 2, 2]
    attn_blocks: [0, 0, 0, 0, 1, 1, 0, 0]
    dropout: 0.0

ckpt:
  resume_ckpt_path: checkpoints/syncnet/checkpoint-1000.pt
  inference_ckpt_path: checkpoints/syncnet/checkpoint-1000.pt
  save_ckpt_steps: 1000

data:
  train_output_dir: debug/syncnet
  num_val_samples: 2048
  batch_size: 256 # 256
  num_workers: 12 # 12
  latent_space: false
  num_frames: 16
  resolution: 256
  train_fileslist: data/train_list.txt
  train_data_dir: ""
  val_fileslist: data/val_list.txt
  val_data_dir: ""
  audio_mel_cache_dir: audio_cache/mel_new
  lower_half: true
  audio_sample_rate: 16000
  video_fps: 25

  # Image processor settings
  image_processor:
    # Padding configuration for face detection and masking
    padding:
      # Controls the expansion of the face bounding box
      bbox: 0.1
      box_height_ratio: 1

      insightface_pad_factor: 0.5
      
      # MediaPipe-specific padding for stability
      mediapipe: 0.05
      
      # Face Alignment padding for detection robustness
      face_alignment: 0.05
      
      # Controls vertical face coverage in advanced mask mode
      # - Higher values (e.g., 1.0) include more of lower face
      # - Lower values (e.g., 0.8) focus more on central features
      # - Default: 0.95 provides natural face proportions
      height_ratio: 0.8
      
      # Adjusts mask top edge relative to nose position
      # - Higher values (e.g., 0.9) move mask top edge down
      # - Lower values (e.g., 0.7) move mask top edge up
      # - Default: 0.8 aligns well with typical face proportions
      nose_mid_factor: 1.0
    
    # Face template configuration for landmark alignment
    face_templates:
      type: three_point # [four_point, three_point]
      # MediaPipe detector template [left_eye, right_eye, nose]
      mediapipe:
        # Left eye position [x, y]
        # - Affects overall face alignment and symmetry
        # - x: lower = narrower face, higher = wider face
        # - y: lower = eyes higher in frame, higher = eyes lower in frame
        # - [9.0, 30.0]
        # - [25.77, 20.0]
        - [9.8, 29.3]
        
        # Right eye position [x, y]
        # - Controls face width and horizontal alignment
        # - Wider spacing between eyes = broader face detection
        # - Narrower spacing = more focused on central features
        # - [66.0, 30.0]
        # - [74.23, 20.0]
        - [90.2, 29.3]
        
        # Nose position [x, y]
        # - Determines vertical face proportions
        # - Higher y value = lower nose placement
        # - Lower y value = higher nose placement
        # - [37.5,57.5]
        # - [50.0, 50.0]
        - [50.0, 61.32]
      
      # Face Alignment detector template
      # - Generally uses tighter spacing for more precise detection
      face_alignment:
        # Left eye position - optimized for FA detection
        - [25.77, 20.0]
        
        # Right eye position - wider for better landmark coverage
        - [74.23, 20.0]
        
        # Nose position - balanced for natural proportions
        # - Lower y value (40.0 vs MediaPipe's 50.0) for better vertical alignment
        - [50.0, 50.0]

      # Mediapipe model path
      
    mediapipe_model_path: "checkpoints/face_landmarker.task"

optimizer:
  lr: 1e-5 # Max LR in case of one_cycle
  scale_lr: false
  max_grad_norm: 1.0
  lr_scheduler: constant # Choose between [constant, one_cycle]
  lr_warmup_steps: 0

  # One cycle policy settings
  pct_start: 0.3
  div_factor: 25.0
  final_div_factor: 10000.0
  three_phase: false
  anneal_strategy: cos

run:
  max_train_steps: 10000
  validation_steps: 25
  mixed_precision_training: true
  seed: 42

  unmasked_adulteration: true # Train with K unmasked frame randomly injected into the batches.
  adulteration_weight: 5 # Number of adulteration frames in each batch.
  adulteration_prob: 0.5 # Probability of adulteration in each batch.
  mask_path: hotdub/utils/mask.png
  mask_type: fix_mask # [fix_mask, half, mouth, face, eye, advanced]
  smooth_type: laplacian # [laplacian, savgol, EMA, kalman, moving_average]
  face_detector: mediapipe # [mediapipe, face_alignment]
