# Pipeline Configuration for HotDub-Preprocess

# General Pipeline Settings
pipeline:
  process_kill_timeout: 30    # Seconds to wait before force killing a process
  ffmpeg_timeout: 300         # Seconds to wait for FFmpeg operations
  cuda_retry_count: 3         # Number of CUDA cleanup attempts

# Stage-specific Settings
stages:
  filter_single_face:
    confidence_threshold: 0.95 # Confidence threshold for single face detection
  
  delete_short_videos:
    min_duration_seconds: 3     # Minimum video duration in seconds to keep

  sync_correction:
      sync_confidence_threshold: 2

  run_affine:
    output_resolution: 256      # Output resolution for affine transformation

# Add other configurations as needed, e.g., for logging, model paths (if not in a separate models.yaml)