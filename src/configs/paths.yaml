# Path configuration for HotDub-Preprocess

# Base directories
base:
  data_dir: "data"
  models_dir: "models"
  logs_dir: "logs"
  configs_dir: "configs"

# Pretrained models
pretrained:
  base_dir: "models/pretrained"
  talknet_model: "models/pretrained/talknet/pretrain_TalkSet.model"
  talknet_model_id: "1AbN9fCf9IexMxEKXLQY2KYBlb-IhSEea"
  ava_model: "models/pretrained/talknet/pretrain_AVA.model"

# Columbia dataset
columbia:
  labels_id: "1Tto5JBt6NsEOLFRWzyZEeV6kCCddc6wv"
  video_url: "https://www.youtube.com/watch?v=6GzxbrO0DHM&t=2s"

# TalkNet paths
talknet:
  demo_video_folder: "assets/Test-Videos"
  col_save_path: "data/col"
  
# Processing directories
processing:
  pyavi_dir: "pyavi"
  pyframes_dir: "pyframes"
  pywork_dir: "pywork"
  pycrop_dir: "pycrop"
  output_dir: "output-1"