"""
s3manager configuration and credential management.
Handles loading, saving, and resolving bucket configurations
from environment variables and a JSON configuration file.
"""

import os
import json
import stat
from pathlib import Path
from typing import Optional, Dict, Any, Tuple

DEFAULT_CONFIG_DIR = Path.home() / ".config" / "s3manager"
DEFAULT_CONFIG_FILE = DEFAULT_CONFIG_DIR / "credentials.json"


class CredentialsNotFoundError(Exception):
    """Custom exception for when credentials cannot be resolved."""

    pass


def get_default_config_path() -> Path:
    """Returns the default configuration file path."""
    return DEFAULT_CONFIG_FILE


def _ensure_config_dir_exists():
    """Ensures the default configuration directory exists."""
    DEFAULT_CONFIG_DIR.mkdir(parents=True, exist_ok=True)


def _get_config_path(file_path: Optional[str] = None) -> Path:
    """Returns the Path object for the config file, defaulting if None."""
    return Path(file_path) if file_path else get_default_config_path()


def load_all_bucket_configs(
    file_path: Optional[str] = None,
) -> Dict[str, Dict[str, Any]]:
    """
    Loads all bucket configurations from the specified or default JSON file.
    Returns an empty dict if the file doesn't exist or is invalid.
    """
    config_file = _get_config_path(file_path)
    if not config_file.exists():
        return {}
    try:
        with open(config_file, "r") as f:
            return json.load(f)
    except (json.JSONDecodeError, IOError):
        # Consider logging this error
        return {}


def save_bucket_config(
    alias: str,
    bucket_name: str,  # Added bucket_name
    endpoint_url: str,
    access_key: str,
    secret_key: str,
    region: Optional[str] = None,
    file_path: Optional[str] = None,
) -> None:
    """Saves or updates a single bucket configuration to the JSON file."""
    _ensure_config_dir_exists()
    config_file = _get_config_path(file_path)
    configs = load_all_bucket_configs(str(config_file))  # Load from the exact path

    configs[alias] = {
        "bucket_name": bucket_name,  # Store bucket_name
        "endpoint_url": endpoint_url,
        "access_key": access_key,  # Consider encryption for production systems
        "secret_key": secret_key,  # Consider encryption for production systems
        "region": region,
    }
    try:
        with open(config_file, "w") as f:
            json.dump(configs, f, indent=4)
        # Set file permissions to 600 (owner read/write only)
        os.chmod(config_file, stat.S_IRUSR | stat.S_IWUSR)
    except IOError:
        # Consider logging this error
        raise  # Or handle more gracefully


def remove_bucket_config(alias: str, file_path: Optional[str] = None) -> bool:
    """Removes a bucket configuration by alias. Returns True if successful."""
    config_file = _get_config_path(file_path)
    configs = load_all_bucket_configs(str(config_file))
    if alias in configs:
        del configs[alias]
        try:
            with open(config_file, "w") as f:
                json.dump(configs, f, indent=4)
            return True
        except IOError:
            # Consider logging
            return False
    return False


def get_resolved_credentials(
    alias: str, config_file_path: Optional[str] = None
) -> Dict[str, Any]:
    """
    Resolves credentials for a given alias.
    Priority:
    1. Environment Variables (S3MANAGER_ENDPOINT_ALIAS, etc.)
    2. Configuration File (specified or default)
    Raises CredentialsNotFoundError if credentials cannot be found.
    """
    alias_upper = alias.upper()
    env_endpoint = os.getenv(f"S3MANAGER_ENDPOINT_{alias_upper}")
    env_access_key = os.getenv(f"S3MANAGER_ACCESS_KEY_{alias_upper}")
    env_secret_key = os.getenv(f"S3MANAGER_SECRET_KEY_{alias_upper}")
    env_region = os.getenv(f"S3MANAGER_REGION_{alias_upper}")  # Optional
    env_bucket_name = os.getenv(
        f"S3MANAGER_BUCKET_NAME_{alias_upper}"
    )  # Added bucket name env var

    if env_bucket_name and env_endpoint and env_access_key and env_secret_key:
        return {
            "bucket_name": env_bucket_name,
            "endpoint_url": env_endpoint,
            "access_key": env_access_key,
            "secret_key": env_secret_key,
            "region": env_region,
        }

    # Try loading from config file
    configs = load_all_bucket_configs(config_file_path)
    bucket_config_data = configs.get(alias)

    if bucket_config_data:
        # Ensure all required keys are present from the config file
        required_keys = ["bucket_name", "endpoint_url", "access_key", "secret_key"]
        if all(k in bucket_config_data for k in required_keys):
            return {
                "bucket_name": bucket_config_data["bucket_name"],
                "endpoint_url": bucket_config_data["endpoint_url"],
                "access_key": bucket_config_data["access_key"],
                "secret_key": bucket_config_data["secret_key"],
                "region": bucket_config_data.get("region"),  # Region is optional
            }
        else:
            missing_keys = [k for k in required_keys if k not in bucket_config_data]
            raise CredentialsNotFoundError(
                f"Configuration for alias '{alias}' in file is incomplete. Missing: {', '.join(missing_keys)}. "
                "Please re-configure using 's3manager configure'."
            )

    raise CredentialsNotFoundError(
        f"Credentials for alias '{alias}' not found in environment variables or configuration file. "
        f"Use 's3manager configure {alias}' or set S3MANAGER_*_{alias_upper} environment variables (including S3MANAGER_BUCKET_NAME_{alias_upper})."
    )


def export_configurations(
    target_export_path: str, source_config_file_path: Optional[str] = None
) -> None:
    """Exports current configurations to a new file."""
    configs = load_all_bucket_configs(source_config_file_path)
    try:
        with open(target_export_path, "w") as f:
            json.dump(configs, f, indent=4)
        # Optionally set permissions on the exported file too
        os.chmod(target_export_path, stat.S_IRUSR | stat.S_IWUSR)
    except IOError as e:
        # click.echo(f"Error exporting configuration: {e}", err=True)
        raise  # Or handle more gracefully


def import_configurations(
    source_import_path: str,
    destination_config_file_path: Optional[str] = None,
    merge_strategy: str = "merge",  # "merge" or "overwrite"
) -> Tuple[int, int]:
    """
    Imports configurations from a file.
    Returns a tuple of (imported_count, skipped_or_overwritten_count).
    """
    _ensure_config_dir_exists()  # Ensure default dir exists if destination is default

    destination_path = _get_config_path(destination_config_file_path)

    try:
        with open(source_import_path, "r") as f:
            source_configs = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError, IOError) as e:
        # click.echo(f"Error reading import file {source_import_path}: {e}", err=True)
        raise  # Or handle more gracefully

    destination_configs = load_all_bucket_configs(str(destination_path))

    imported_count = 0
    modified_count = 0

    for alias, config_data in source_configs.items():
        if alias in destination_configs:
            if merge_strategy == "overwrite":
                destination_configs[alias] = config_data
                modified_count += 1
            else:  # merge (skip if exists)
                modified_count += 1  # Counted as skipped
                continue
        else:
            destination_configs[alias] = config_data
            imported_count += 1

    try:
        with open(destination_path, "w") as f:
            json.dump(destination_configs, f, indent=4)
        os.chmod(destination_path, stat.S_IRUSR | stat.S_IWUSR)
    except IOError as e:
        # click.echo(f"Error writing imported configurations to {destination_path}: {e}", err=True)
        raise  # Or handle more gracefully

    return imported_count, modified_count
