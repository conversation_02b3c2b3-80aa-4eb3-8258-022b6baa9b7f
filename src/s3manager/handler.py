"""
s3manager S3 interaction handler.
Contains functions for uploading, downloading, and listing objects
in S3-compatible buckets using the MinIO client.
"""

import os
import logging
import random  # Added for random sampling
from minio import Minio
from minio.error import S3Error
from typing import Dict, Any, Optional, Tuple

# Configure logging for this module (or use a shared logger if available)
logger = logging.getLogger(__name__)
# Basic configuration if no root logger is set up
if not logger.hasHandlers():
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s [%(levelname)s] [%(name)s] %(message)s",
        handlers=[logging.StreamHandler()],
    )


def get_s3_client(
    endpoint_url: str,
    access_key: str,
    secret_key: str,
    region: Optional[str] = None,
    secure: bool = True,  # Default to secure connection
) -> Minio:
    """
    Initializes and returns a MinIO client.
    The 'region' parameter is available but might not be used by all S3-compatible services
    in the same way MinIO client expects. It's included for broader compatibility.
    """
    try:
        client = Minio(
            endpoint_url.replace("https://", "").replace(
                "http://", ""
            ),  # Minio client adds scheme
            access_key=access_key,
            secret_key=secret_key,
            secure=secure,
            region=region,  # Pass region if provided
        )
        # You might want to add a quick health check here, e.g., list_buckets()
        # client.list_buckets() # This would require permissions and might be too much
        logger.info(f"MinIO client initialized for endpoint: {endpoint_url}")
        return client
    except Exception as e:
        logger.error(f"Failed to initialize MinIO client for {endpoint_url}: {e}")
        raise  # Re-raise the exception to be handled by the caller


def download_file(
    client: Minio, bucket_name: str, s3_object_key: str, local_file_path: str
) -> bool:
    """Downloads a single object from S3 to a local file path."""
    try:
        local_dir = os.path.dirname(local_file_path)
        if local_dir:
            os.makedirs(local_dir, exist_ok=True)

        logger.info(
            f"Attempting to download s3://{bucket_name}/{s3_object_key} to {local_file_path}"
        )
        client.fget_object(bucket_name, s3_object_key, local_file_path)
        logger.info(
            f"Successfully downloaded: s3://{bucket_name}/{s3_object_key} to {local_file_path}"
        )
        return True
    except S3Error as s3_err:
        logger.error(f"S3 Error downloading {s3_object_key}: {s3_err}")
    except Exception as e:
        logger.error(f"Failed to download {s3_object_key} to {local_file_path}: {e}")
    return False


def download_directory(
    client: Minio,
    bucket_name: str,
    s3_prefix: str,
    local_directory_path: str,
    random_count: Optional[int] = None,  # New parameter for random download
) -> Tuple[int, int]:
    """
    Downloads objects under a given S3 prefix to a local directory.
    If random_count is specified, downloads N random files. Otherwise, downloads all.
    Maintains the S3 folder structure.
    Returns a tuple of (downloaded_count, total_objects_attempted_to_download).
    """
    downloaded_count = 0
    total_objects_for_download_attempt = 0  # Will store total files to be attempted

    # Ensure prefix ends with a slash for proper "folder" listing
    if not s3_prefix.endswith("/"):
        s3_prefix += "/"

    logger.info(f"Listing objects in bucket '{bucket_name}' with prefix '{s3_prefix}'")
    try:
        objects_to_download = [
            obj
            for obj in client.list_objects(
                bucket_name, prefix=s3_prefix, recursive=True
            )
            if not obj.is_dir  # We only want to download actual files
        ]
        total_objects_found_in_prefix = len(objects_to_download)
        logger.info(
            f"Found {total_objects_found_in_prefix} total files under prefix '{s3_prefix}'."
        )

        if not objects_to_download:
            logger.warning(
                f"No downloadable files found under prefix '{s3_prefix}' in bucket '{bucket_name}'."
            )
            return 0, 0  # No files found, so 0 attempted

        selected_objects_for_download = objects_to_download

        if random_count is not None and random_count > 0:
            if random_count >= total_objects_found_in_prefix:
                logger.info(
                    f"--random count ({random_count}) is >= total files ({total_objects_found_in_prefix}). Downloading all files from prefix '{s3_prefix}'."
                )
                # selected_objects_for_download remains all objects_to_download
            else:
                logger.info(
                    f"Randomly selecting {random_count} files out of {total_objects_found_in_prefix} from prefix '{s3_prefix}'."
                )
                selected_objects_for_download = random.sample(
                    objects_to_download, random_count
                )
            total_objects_for_download_attempt = len(selected_objects_for_download)
        else:
            total_objects_for_download_attempt = total_objects_found_in_prefix
            # selected_objects_for_download remains all objects_to_download

        logger.info(
            f"Attempting to download {total_objects_for_download_attempt} files."
        )

        for obj in selected_objects_for_download:
            if obj.object_name is None:
                logger.warning(
                    f"Skipping object with no name in bucket '{bucket_name}' under prefix '{s3_prefix}'."
                )
                continue
            relative_path = obj.object_name[len(s3_prefix) :]
            local_file_path = os.path.join(local_directory_path, relative_path)

            if download_file(client, bucket_name, obj.object_name, local_file_path):
                downloaded_count += 1
            else:
                logger.error(
                    f"Failed to download {obj.object_name} during directory download."
                )
                # Optionally, decide if one failure should stop the whole process

    except S3Error as s3_err:
        logger.error(f"S3 Error listing objects for prefix '{s3_prefix}': {s3_err}")
        return (
            downloaded_count,
            total_objects_for_download_attempt,
        )  # Return partial success based on what was attempted
    except Exception as e:
        logger.error(
            f"Unexpected error listing/downloading objects for prefix '{s3_prefix}': {e}"
        )
        return (
            downloaded_count,
            total_objects_for_download_attempt,
        )  # Return partial success based on what was attempted

    logger.info(
        f"Directory download complete. {downloaded_count}/{total_objects_for_download_attempt} files downloaded from '{s3_prefix}'."
    )
    return downloaded_count, total_objects_for_download_attempt


def upload_file(
    client: Minio, bucket_name: str, local_file_path: str, s3_object_key: str
) -> bool:
    """Uploads a single local file to S3."""
    try:
        if not os.path.exists(local_file_path):
            logger.error(f"Local file not found: {local_file_path}")
            return False
        if os.path.isdir(local_file_path):
            logger.error(
                f"Local path is a directory, not a file: {local_file_path}. Use upload_directory instead."
            )
            return False

        logger.info(
            f"Attempting to upload {local_file_path} to s3://{bucket_name}/{s3_object_key}"
        )
        client.fput_object(bucket_name, s3_object_key, local_file_path)
        logger.info(
            f"Successfully uploaded: {local_file_path} to s3://{bucket_name}/{s3_object_key}"
        )
        return True
    except S3Error as s3_err:
        logger.error(
            f"S3 Error uploading {local_file_path} to {s3_object_key}: {s3_err}"
        )
    except FileNotFoundError:
        logger.error(f"Local file not found during upload: {local_file_path}")
    except Exception as e:
        logger.error(f"Failed to upload {local_file_path} to {s3_object_key}: {e}")
    return False


def upload_directory(
    client: Minio, bucket_name: str, local_directory_path: str, s3_prefix: str
) -> Tuple[int, int]:
    """
    Uploads all files from a local directory to S3 under a given prefix,
    maintaining the local folder structure.
    Returns a tuple of (uploaded_count, total_files_found).
    """
    uploaded_count = 0
    total_files_found = 0

    if not os.path.isdir(local_directory_path):
        logger.error(f"Local path is not a directory: {local_directory_path}")
        return 0, 0

    # Ensure S3 prefix ends with a slash if it's meant to be a "folder"
    if s3_prefix and not s3_prefix.endswith("/"):
        s3_prefix += "/"

    files_to_upload = []
    for root, _, files in os.walk(local_directory_path):
        for filename in files:
            files_to_upload.append(os.path.join(root, filename))

    total_files_found = len(files_to_upload)
    logger.info(
        f"Found {total_files_found} files to upload from directory '{local_directory_path}'."
    )

    if not files_to_upload:
        logger.warning(
            f"No files found to upload in directory '{local_directory_path}'."
        )
        return 0, 0

    for local_file_path in files_to_upload:
        relative_path = os.path.relpath(local_file_path, local_directory_path)
        # Ensure consistent path separators for S3 (Unix-style)
        s3_object_key = s3_prefix + relative_path.replace(os.sep, "/")

        if upload_file(client, bucket_name, local_file_path, s3_object_key):
            uploaded_count += 1
        else:
            logger.error(f"Failed to upload {local_file_path} during directory upload.")
            # Optionally, decide if one failure should stop the whole process

    logger.info(
        f"Directory upload complete. {uploaded_count}/{total_files_found} files uploaded from '{local_directory_path}' to prefix '{s3_prefix}'."
    )
    return uploaded_count, total_files_found


def list_objects_basic(
    client: Minio, bucket_name: str, s3_path: str
) -> Tuple[Optional[str], Optional[bool]]:
    """
    Tries to determine if an S3 path is a file or a "directory" (prefix).
    Returns a tuple: (object_name or prefix, is_dir_flag).
    Returns (None, None) on error or if path doesn't exist clearly as one or the other.
    This is a simplified check. S3 doesn't truly have directories, only prefixes.
    """
    try:
        # Check if it's a specific object (file)
        try:
            stat = client.stat_object(bucket_name, s3_path)
            if stat:  # If stat_object succeeds, it's likely a file-like object
                return (
                    stat.object_name,
                    False,
                )  # False meaning not a directory-like prefix
        except S3Error as e:
            if e.code == "NoSuchKey":  # Common error if it's not a direct file key
                pass  # Continue to check if it's a prefix
            else:
                logger.warning(f"S3 error stating object '{s3_path}': {e}")
                # return None, None # Or re-raise depending on desired strictness

        # Check if it's a prefix by listing objects under it
        # Add a trailing slash if not present to list "contents" of a prefix
        prefix_to_check = s3_path if s3_path.endswith("/") else s3_path + "/"
        objects_under_prefix = list(
            client.list_objects(bucket_name, prefix=prefix_to_check, recursive=False)
        )

        if objects_under_prefix:
            # If listing with a trailing slash returns items, it's acting like a directory
            return prefix_to_check, True

        # If it didn't end with '/', and listing with added '/' found nothing,
        # but also stat_object failed, it might be an empty prefix or non-existent.
        # Let's try listing without the added slash if original didn't have one.
        if not s3_path.endswith("/"):
            objects_at_exact_path = list(
                client.list_objects(bucket_name, prefix=s3_path, recursive=False)
            )
            if (
                len(objects_at_exact_path) == 1
                and objects_at_exact_path[0].object_name == s3_path
                and not objects_at_exact_path[0].is_dir
            ):
                return objects_at_exact_path[0].object_name, False  # It's a file
            elif any(
                obj.object_name is not None
                and obj.object_name.startswith(s3_path + "/")
                for obj in objects_at_exact_path
            ):
                return s3_path + "/", True  # It's a prefix without a trailing slash

        logger.info(
            f"Path '{s3_path}' in bucket '{bucket_name}' does not clearly map to a file or populated prefix."
        )
        return None, None  # Ambiguous or non-existent

    except S3Error as e:
        logger.error(f"S3 error checking path type for '{s3_path}': {e}")
        return None, None
    except Exception as e:
        logger.error(f"Unexpected error checking path type for '{s3_path}': {e}")
        return None, None
