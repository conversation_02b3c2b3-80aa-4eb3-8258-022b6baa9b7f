"""
s3manager CLI entry point.
This module will contain all the click command definitions.
"""

import click
import sys
import json  # Added import for json
import os  # For os.path.isdir/isfile
from . import config as s3_config
from . import handler as s3_handler
from .config import CredentialsNotFoundError


# Main CLI group
@click.group()
@click.option(
    "--config-file",
    type=click.Path(dir_okay=False, writable=True, resolve_path=True),
    help="Path to the configuration file. Defaults to ~/.config/s3manager/credentials.json",
    default=None,
)
@click.version_option(version="0.1.0", prog_name="s3manager")  # Added version option
@click.pass_context
def main_cli_group(ctx, config_file):
    """
    S3Manager CLI: Manage S3-compatible bucket credentials and operations.
    """
    ctx.ensure_object(dict)
    # Store the resolved path or None. config.py functions will handle default if None.
    ctx.obj["CONFIG_FILE_PATH"] = (
        config_file if config_file else s3_config.get_default_config_path()
    )


# --- Configuration Management Commands ---


@main_cli_group.command("configure")
@click.option(
    "--alias",
    prompt="Bucket alias",
    help="A unique nickname for the bucket configuration.",
)
@click.option(
    "--bucket-name", prompt="S3 Bucket Name", help="The actual name of the S3 bucket."
)
@click.option(
    "--endpoint",
    prompt="Endpoint URL (e.g., s3.amazonaws.com or custom.endpoint.com)",
    help="S3 endpoint URL.",
)
@click.option(
    "--access-key", prompt="Access Key ID", help="S3 Access Key ID.", hide_input=True
)
@click.option(
    "--secret-key",
    prompt="Secret Access Key",
    help="S3 Secret Access Key.",
    hide_input=True,
    confirmation_prompt=True,
)
@click.option("--region", help="S3 bucket region (optional).", default=None)
@click.pass_context
def configure_bucket(ctx, alias, bucket_name, endpoint, access_key, secret_key, region):
    """Adds a new bucket configuration or updates an existing one."""
    config_file = ctx.obj["CONFIG_FILE_PATH"]
    try:
        s3_config.save_bucket_config(
            alias,
            bucket_name,
            endpoint,
            access_key,
            secret_key,
            region,
            file_path=str(config_file),
        )
        click.echo(
            f"Configuration for alias '{alias}' (bucket: '{bucket_name}') saved successfully to {config_file}."
        )
    except Exception as e:
        click.echo(f"Error saving configuration: {e}", err=True)
        sys.exit(1)


@main_cli_group.command("list-buckets")
@click.pass_context
def list_buckets(ctx):
    """Lists all configured bucket aliases and their non-sensitive details."""
    config_file = ctx.obj["CONFIG_FILE_PATH"]
    configs = s3_config.load_all_bucket_configs(file_path=str(config_file))
    if not configs:
        click.echo("No bucket configurations found.")
        return

    click.echo(f"Configured buckets (from {config_file}):")
    click.echo(f"{'ALIAS':<20} {'BUCKET NAME':<30} {'ENDPOINT URL':<40} {'REGION'}")
    click.echo("-" * 100)  # Adjusted separator length
    for alias, details in configs.items():
        bucket_name_display = details.get("bucket_name", "N/A")
        region_display = details.get("region", "N/A") or "N/A"
        click.echo(
            f"{alias:<20} {bucket_name_display:<30} {details.get('endpoint_url', 'N/A'):<40} {region_display}"
        )


@main_cli_group.command("remove-bucket")
@click.argument("alias")
@click.confirmation_option(
    prompt="Are you sure you want to remove this bucket configuration?"
)
@click.pass_context
def remove_bucket(ctx, alias):
    """Removes a bucket configuration by its alias."""
    config_file = ctx.obj["CONFIG_FILE_PATH"]
    try:
        if s3_config.remove_bucket_config(alias, file_path=str(config_file)):
            click.echo(
                f"Bucket configuration for alias '{alias}' removed successfully from {config_file}."
            )
        else:
            click.echo(
                f"Bucket configuration for alias '{alias}' not found in {config_file}.",
                err=True,
            )
    except Exception as e:
        click.echo(f"Error removing configuration: {e}", err=True)
        sys.exit(1)


@main_cli_group.command("export-config")
@click.argument(
    "export_file_path",
    type=click.Path(writable=True, dir_okay=False, resolve_path=True),
)
@click.pass_context
def export_config(ctx, export_file_path):
    """Exports the current bucket configurations to a new file."""
    source_config_file = ctx.obj["CONFIG_FILE_PATH"]
    try:
        s3_config.export_configurations(
            export_file_path, source_config_file_path=str(source_config_file)
        )
        click.echo(
            f"Configurations successfully exported from {source_config_file} to {export_file_path}."
        )
    except Exception as e:
        click.echo(f"Error exporting configurations: {e}", err=True)
        sys.exit(1)


@main_cli_group.command("import-config")
@click.argument(
    "import_file_path",
    type=click.Path(exists=True, readable=True, dir_okay=False, resolve_path=True),
)
@click.option(
    "--merge-strategy",
    type=click.Choice(["merge", "overwrite"], case_sensitive=False),
    default="merge",
    show_default=True,
    help="Strategy for handling existing aliases: 'merge' (skip existing) or 'overwrite'.",
)
@click.pass_context
def import_config(ctx, import_file_path, merge_strategy):
    """Imports bucket configurations from a file into the active configuration."""
    destination_config_file = ctx.obj["CONFIG_FILE_PATH"]
    try:
        imported, modified = s3_config.import_configurations(
            import_file_path,
            destination_config_file_path=str(destination_config_file),
            merge_strategy=merge_strategy,
        )
        click.echo(
            f"Configurations imported from {import_file_path} to {destination_config_file}."
        )
        click.echo(f"  New configurations added: {imported}")
        if merge_strategy == "overwrite":
            click.echo(f"  Existing configurations overwritten: {modified}")
        else:
            click.echo(f"  Existing configurations skipped: {modified}")
    except FileNotFoundError:
        click.echo(f"Error: Import file not found at {import_file_path}", err=True)
        sys.exit(1)
    except json.JSONDecodeError:
        click.echo(
            f"Error: Could not decode JSON from import file {import_file_path}. Please ensure it's valid JSON.",
            err=True,
        )
        sys.exit(1)
    except Exception as e:
        click.echo(f"Error importing configurations: {e}", err=True)
        sys.exit(1)


# --- File Operation Commands (Placeholders - to be implemented next) ---


@main_cli_group.command("upload")
@click.argument("alias")
@click.argument(
    "local_path", type=click.Path(exists=True, readable=True, resolve_path=True)
)
@click.argument("remote_path")
@click.pass_context
def upload(ctx, alias, local_path, remote_path):
    """Uploads a local file or directory to the specified S3 bucket."""
    config_file_path = str(ctx.obj["CONFIG_FILE_PATH"])
    click.echo(
        f"Attempting to upload from '{local_path}' to '{alias}:{remote_path}' using config '{config_file_path}'..."
    )

    try:
        creds = s3_config.get_resolved_credentials(
            alias, config_file_path=config_file_path
        )
        client = s3_handler.get_s3_client(
            endpoint_url=creds["endpoint_url"],
            access_key=creds["access_key"],
            secret_key=creds["secret_key"],
            region=creds.get("region"),
        )

        bucket_name = creds["bucket_name"]  # Directly use from resolved creds

        if os.path.isdir(local_path):
            uploaded_count, total_files = s3_handler.upload_directory(
                client, bucket_name, local_path, remote_path
            )
            click.echo(
                f"Directory upload: {uploaded_count}/{total_files} files uploaded successfully to '{alias}:{remote_path}'."
            )
        elif os.path.isfile(local_path):
            if s3_handler.upload_file(client, bucket_name, local_path, remote_path):
                click.echo(
                    f"File '{local_path}' uploaded successfully to '{alias}:{remote_path}'."
                )
            else:
                click.echo(f"Failed to upload file '{local_path}'.", err=True)
                sys.exit(1)
        else:
            click.echo(
                f"Error: Local path '{local_path}' is not a valid file or directory.",
                err=True,
            )
            sys.exit(1)

    except CredentialsNotFoundError as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"An unexpected error occurred during upload: {e}", err=True)
        sys.exit(1)


@main_cli_group.command("download")
@click.argument("alias")
@click.argument("remote_path")
@click.argument(
    "local_path", type=click.Path(writable=True, resolve_path=True)
)  # dir_okay=True implicitly
@click.option(
    "--random",
    "random_count",
    type=int,
    default=None,
    help="Download N random files from the remote_path (if it's a directory/prefix).",
    show_default=False,
)
@click.pass_context
def download(ctx, alias, remote_path, local_path, random_count):
    """Downloads an S3 object or 'directory' (prefix) to the local filesystem."""
    config_file_path = str(ctx.obj["CONFIG_FILE_PATH"])
    click.echo(
        f"Attempting to download from '{alias}:{remote_path}' to '{local_path}' using config '{config_file_path}'..."
    )
    if random_count is not None:
        click.echo(f"Random download requested: {random_count} files.")

    try:
        creds = s3_config.get_resolved_credentials(
            alias, config_file_path=config_file_path
        )
        client = s3_handler.get_s3_client(
            endpoint_url=creds["endpoint_url"],
            access_key=creds["access_key"],
            secret_key=creds["secret_key"],
            region=creds.get("region"),
        )

        bucket_name = creds["bucket_name"]  # Directly use from resolved creds

        # Determine if remote_path is a file or directory (prefix)
        # The list_objects_basic is a helper, but for robust download, we might need more.
        # If remote_path ends with '/', treat as directory. Otherwise, try as file first.

        is_dir_hint = remote_path.endswith("/")

        if is_dir_hint:
            # Ensure local_path is a directory or can be created
            if os.path.exists(local_path) and not os.path.isdir(local_path):
                click.echo(
                    f"Error: Local path '{local_path}' exists and is not a directory.",
                    err=True,
                )
                sys.exit(1)
            os.makedirs(local_path, exist_ok=True)

            if random_count is not None and not remote_path.endswith("/"):
                click.echo(
                    f"Warning: --random option is ignored when remote_path ('{remote_path}') does not end with '/' suggesting a specific file.",
                    err=True,
                )

            downloaded_count, total_files = s3_handler.download_directory(
                client, bucket_name, remote_path, local_path, random_count=random_count
            )
            if (
                random_count is not None
                and random_count > 0
                and total_files < random_count
                and total_files > 0
            ):
                click.echo(
                    f"Directory download: Successfully downloaded {downloaded_count}/{total_files} files (requested {random_count} random files, but only {total_files} were available/selected for download) to '{local_path}'."
                )
            elif random_count is not None and random_count > 0 and total_files == 0:
                click.echo(
                    f"Directory download: No files found to download randomly from '{remote_path}'."
                )
            else:
                click.echo(
                    f"Directory download: {downloaded_count}/{total_files} files downloaded successfully to '{local_path}'."
                )
        else:
            # Try as a single file. If local_path is a dir, append filename.
            effective_local_path = local_path
            if os.path.isdir(local_path):
                effective_local_path = os.path.join(
                    local_path, os.path.basename(remote_path)
                )

            # Ensure parent directory of effective_local_path exists
            local_file_dir = os.path.dirname(effective_local_path)
            if local_file_dir:
                os.makedirs(local_file_dir, exist_ok=True)

            if random_count is not None:
                click.echo(
                    f"Warning: --random option is ignored when downloading a specific file target '{remote_path}'.",
                    err=True,
                )
            if s3_handler.download_file(
                client, bucket_name, remote_path, effective_local_path
            ):
                click.echo(
                    f"File '{alias}:{remote_path}' downloaded successfully to '{effective_local_path}'."
                )
            else:
                # Could be that it was actually a prefix without a trailing slash.
                # Attempt to download as directory.
                click.echo(
                    f"Could not download '{remote_path}' as a file. Attempting as a directory/prefix.",
                    err=True,
                )
                if os.path.exists(local_path) and not os.path.isdir(local_path):
                    click.echo(
                        f"Error: Local path '{local_path}' exists and is not a directory for prefix download.",
                        err=True,
                    )
                    sys.exit(1)
                os.makedirs(local_path, exist_ok=True)

                downloaded_count, total_files = s3_handler.download_directory(
                    client,
                    bucket_name,
                    remote_path,
                    local_path,
                    random_count=random_count,  # remote_path here might need a trailing /
                )
                if downloaded_count > 0:
                    if (
                        random_count is not None
                        and random_count > 0
                        and total_files < random_count
                        and total_files > 0
                    ):
                        click.echo(
                            f"Directory download: Successfully downloaded {downloaded_count}/{total_files} files (requested {random_count} random files, but only {total_files} were available/selected for download) to '{local_path}'."
                        )
                    elif (
                        random_count is not None
                        and random_count > 0
                        and total_files == 0
                    ):
                        click.echo(
                            f"Directory download: No files found to download randomly from '{remote_path}'."
                        )
                    else:
                        click.echo(
                            f"Directory download: {downloaded_count}/{total_files} files downloaded successfully to '{local_path}'."
                        )
                else:
                    click.echo(
                        f"Failed to download '{remote_path}' as a file or directory/prefix.",
                        err=True,
                    )
                    sys.exit(1)

    except CredentialsNotFoundError as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"An unexpected error occurred during download: {e}", err=True)
        sys.exit(1)


if __name__ == "__main__":
    main_cli_group()
