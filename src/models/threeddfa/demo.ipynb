{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## A simple demostration of how to run"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# before import, make sure FaceBoxes and Sim3DR are built successfully, e.g.,\n", "# sh build.sh\n", "\n", "import cv2\n", "import yaml\n", "\n", "from FaceBoxes import FaceBoxes\n", "from TDDFA import TDDFA\n", "from utils.functions import draw_landmarks\n", "from utils.render import render\n", "from utils.depth import depth\n", "\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load configs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# load config\n", "cfg = yaml.load(open(\"configs/mb1_120x120.yml\"), Loader=yaml.SafeLoader)\n", "\n", "# Init FaceBoxes and TDDFA, recommend using onnx flag\n", "onnx_flag = True  # or True to use ONNX to speed up\n", "if onnx_flag:\n", "    import os\n", "\n", "    os.environ[\"KMP_DUPLICATE_LIB_OK\"] = \"True\"\n", "    os.environ[\"OMP_NUM_THREADS\"] = \"4\"\n", "\n", "    from FaceBoxes.FaceBoxes_ONNX import FaceBoxes_ONNX\n", "    from TDDFA_ONNX import TDDFA_ONNX\n", "\n", "    face_boxes = FaceBoxes_ONNX()\n", "    tddfa = TDDFA_ONNX(**cfg)\n", "else:\n", "    tddfa = TDDFA(gpu_mode=False, **cfg)\n", "    face_boxes = FaceBoxes()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# given an image path\n", "img_fp = \"examples/inputs/emma.jpg\"\n", "img = cv2.imread(img_fp)\n", "plt.imshow(img[..., ::-1])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Detect faces using FaceBoxes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# face detection\n", "boxes = face_boxes(img)\n", "print(f\"Detect {len(boxes)} faces\")\n", "print(boxes)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Regressing 3DMM parameters, reconstruction and visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# regress 3DMM params\n", "param_lst, roi_box_lst = tddfa(img, boxes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# reconstruct vertices and visualizing sparse landmarks\n", "dense_flag = False\n", "ver_lst = tddfa.recon_vers(param_lst, roi_box_lst, dense_flag=dense_flag)\n", "draw_landmarks(img, ver_lst, dense_flag=dense_flag)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# reconstruct vertices and visualizing dense landmarks\n", "dense_flag = True\n", "ver_lst = tddfa.recon_vers(param_lst, roi_box_lst, dense_flag=dense_flag)\n", "draw_landmarks(img, ver_lst, dense_flag=dense_flag)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# reconstruct vertices and render\n", "ver_lst = tddfa.recon_vers(param_lst, roi_box_lst, dense_flag=dense_flag)\n", "render(img, ver_lst, tddfa.tri, alpha=0.6, show_flag=True);"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# reconstruct vertices and render depth\n", "ver_lst = tddfa.recon_vers(param_lst, roi_box_lst, dense_flag=dense_flag)\n", "depth(img, ver_lst, tddfa.tri, show_flag=True);"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}