## Statement

The modified BFM2009 face model in `../configs/bfm_noneck_v3.pkl` is only for academic use.
For commercial use, you need to apply for the commercial license, some refs are below:

[1] https://faces.dmi.unibas.ch/bfm/?nav=1-0&id=basel_face_model

[2] https://faces.dmi.unibas.ch/bfm/bfm2019.html

If your work benefits from this repo, please cite

    @PROCEEDINGS{bfm09,
        title={A 3D Face Model for Pose and Illumination Invariant Face Recognition},
        author={<PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>
                and <PERSON><PERSON> and <PERSON><PERSON>},
        journal={Proceedings of the 6th IEEE International Conference on Advanced Video and Signal based Surveillance (AVSS)
             for Security, Safety and Monitoring in Smart Environments},
        organization={IEEE},
        year={2009},
        address     = {Genova, Italy},
    }

 