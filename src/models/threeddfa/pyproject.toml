[build-system]
requires = ["setuptools>=61.0", "wheel", "cython>=3.0.12", "numpy>=1.20.0"]
build-backend = "setuptools.build_meta"
backend-path = "."

[project]
name = "threeddfa"
version = "0.1.0"
requires-python = ">=3.12"
dynamic = ["description", "readme", "authors", "classifiers"]
dependencies = [
    "argparse>=1.4.0",
    "cython>=3.0.12",
    "gradio>=5.29.0",
    "huggingface-hub>=0.30.2",
    "imageio>=2.37.0",
    "imageio-ffmpeg>=0.6.0",
    "matplotlib>=3.10.1",
    "numpy>=2.2.5",
    "onnxruntime>=1.21.1",
    "opencv-python>=*********",
    "plotly>=6.0.1",
    "pyyaml>=6.0.2",
    "scikit-image>=0.25.2",
    "scipy>=1.15.2",
    "streamlit>=1.45.0",
    "torch>=2.6.0",
    "torchvision>=0.21.0",
    "tqdm>=4.67.1",
]

[[tool.uv.index]]
name = "pytorch-cu124"
url = "https://download.pytorch.org/whl/cu124"
explicit = true

[tool.uv.sources]
torch = [
  { index = "pytorch-cu124", marker = "sys_platform == 'linux' or sys_platform == 'win32'" },
]
torchvision = [
  { index = "pytorch-cu124", marker = "sys_platform == 'linux' or sys_platform == 'win32'" },
]
