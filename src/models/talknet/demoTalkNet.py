import glob
import math
import os
import pickle
import subprocess
import sys
import time
import warnings
from shutil import rmtree

import cv2
import numpy
import python_speech_features
import torch
import tqdm
from omegaconf import OmegaConf
from scenedetect.detectors import ContentDetector
from scenedetect.scene_manager import SceneManager
from scenedetect.stats_manager import StatsManager
from scenedetect.video_manager import VideoManager
from scipy import signal
from scipy.interpolate import interp1d
from scipy.io import wavfile
from sklearn.metrics import accuracy_score, f1_score

from src.models.faceDetector.s3fd import S3FD
from src.models.talknet.talkNet import talkNet

warnings.filterwarnings("ignore")


# Load configuration
def load_config():
    # Get project root directory using os.path
    project_root = os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    )

    # Use os.path.join for path concatenation
    paths_config_path = os.path.join(project_root, "configs", "paths.yaml")
    talknet_config_path = os.path.join(project_root, "configs", "talknet.yaml")

    paths_config = OmegaConf.load(paths_config_path)
    talknet_config = OmegaConf.load(talknet_config_path)

    return paths_config, talknet_config, project_root


PATHS_CONFIG, TALKNET_CONFIG, PROJECT_ROOT = load_config()


# Create a configuration class that combines paths and talknet configs
class Config:
    def __init__(self, paths_config, talknet_config, project_root, video_name=None):
        self.project_root = project_root

        # Video parameters
        self.videoName = video_name if video_name else talknet_config.video.video_name
        self.videoFolder = os.path.join(
            str(project_root), paths_config.talknet.demo_video_folder
        )
        self.pretrainModel = os.path.join(
            str(project_root), paths_config.pretrained.talknet_model
        )

        # Processing parameters
        self.nDataLoaderThread = talknet_config.processing.n_data_loader_thread
        self.facedetScale = talknet_config.processing.facedet_scale
        self.minTrack = talknet_config.processing.min_track
        self.numFailedDet = talknet_config.processing.num_failed_det
        self.minFaceSize = talknet_config.processing.min_face_size
        self.cropScale = talknet_config.processing.crop_scale

        # Video timing
        self.start = talknet_config.video.start_time
        self.duration = talknet_config.video.duration

        # Evaluation parameters
        self.evalCol = talknet_config.evaluation.eval_col
        self.colSavePath = os.path.join(
            str(project_root), paths_config.talknet.col_save_path
        )

        # Processing directories
        self.pyavi_dir = paths_config.processing.pyavi_dir
        self.pyframes_dir = paths_config.processing.pyframes_dir
        self.pywork_dir = paths_config.processing.pywork_dir
        self.pycrop_dir = paths_config.processing.pycrop_dir

        # Set video path and save path
        if self.evalCol:
            self.setup_columbia_paths()
        else:
            self.setup_demo_paths()

    def setup_columbia_paths(self):
        self.videoName = "col"
        self.videoFolder = self.colSavePath
        self.savePath = os.path.join(self.videoFolder, self.videoName)

        # Search for video files with any extension
        video_files = glob.glob(os.path.join(self.videoFolder, f"{self.videoName}.*"))

        if video_files:
            self.videoPath = video_files[0]
        else:
            # Create a default video path for download
            self.videoPath = os.path.join(self.videoFolder, f"{self.videoName}.mp4")

        self.duration = 0

    def setup_demo_paths(self):
        if self.videoName:  # Process a specific video
            # Search for video files with any extension
            video_files = glob.glob(
                os.path.join(self.videoFolder, f"{self.videoName}.*")
            )

            if video_files:
                self.videoPath = video_files[0]
                self.savePath = os.path.join(self.videoFolder, self.videoName)
            else:
                raise FileNotFoundError(
                    f"No video file found matching pattern: {os.path.join(self.videoFolder, self.videoName)}.*"
                )
        else:
            # videoName is empty - will process all videos in directory later
            self.videoPath = None
            self.savePath = None


def scene_detect(config):
    # CPU: Scene detection, output is the list of each shot's time duration
    videoManager = VideoManager([config.videoFilePath])
    statsManager = StatsManager()
    sceneManager = SceneManager(statsManager)
    sceneManager.add_detector(ContentDetector())
    baseTimecode = videoManager.get_base_timecode()
    videoManager.set_downscale_factor()
    videoManager.start()
    sceneManager.detect_scenes(frame_source=videoManager)
    sceneList = sceneManager.get_scene_list(baseTimecode)
    savePath = os.path.join(config.pyworkPath, "scene.pckl")
    if sceneList == []:
        sceneList = [
            (videoManager.get_base_timecode(), videoManager.get_current_timecode())
        ]
    with open(savePath, "wb") as fil:
        pickle.dump(sceneList, fil)
        sys.stderr.write(
            "%s - scenes detected %d\n" % (config.videoFilePath, len(sceneList))
        )
    return sceneList


def inference_video(config):
    # GPU: Face detection, output is the list contains the face location and score in this frame
    DET = S3FD(device="cuda")
    flist = glob.glob(os.path.join(config.pyframesPath, "*.jpg"))
    flist.sort()
    dets = []
    for fidx, fname in enumerate(flist):
        image = cv2.imread(fname)
        imageNumpy = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        bboxes = DET.detect_faces(imageNumpy, conf_th=0.9, scales=[config.facedetScale])
        dets.append([])
        for bbox in bboxes:
            dets[-1].append(
                {"frame": fidx, "bbox": (bbox[:-1]).tolist(), "conf": bbox[-1]}
            )  # has the frames info, bbox info, conf info
        sys.stderr.write(
            "%s-%05d; %d dets\r" % (config.videoFilePath, fidx, len(dets[-1]))
        )
    savePath = os.path.join(config.pyworkPath, "faces.pckl")
    with open(savePath, "wb") as fil:
        pickle.dump(dets, fil)
    return dets


def bb_intersection_over_union(boxA, boxB, evalCol=False):
    # CPU: IOU Function to calculate overlap between two image
    xA = max(boxA[0], boxB[0])
    yA = max(boxA[1], boxB[1])
    xB = min(boxA[2], boxB[2])
    yB = min(boxA[3], boxB[3])
    interArea = max(0, xB - xA) * max(0, yB - yA)
    boxAArea = (boxA[2] - boxA[0]) * (boxA[3] - boxA[1])
    boxBArea = (boxB[2] - boxB[0]) * (boxB[3] - boxB[1])
    if evalCol:
        iou = interArea / float(boxAArea)
    else:
        iou = interArea / float(boxAArea + boxBArea - interArea)
    return iou


def track_shot(config, sceneFaces):
    # CPU: Face tracking
    iouThres = 0.5  # Minimum IOU between consecutive face detections
    tracks = []
    while True:
        track = []
        for frameFaces in sceneFaces:
            for face in frameFaces:
                if track == []:
                    track.append(face)
                    frameFaces.remove(face)
                elif face["frame"] - track[-1]["frame"] <= config.numFailedDet:
                    iou = bb_intersection_over_union(face["bbox"], track[-1]["bbox"])
                    if iou > iouThres:
                        track.append(face)
                        frameFaces.remove(face)
                        continue
                else:
                    break
        if track == []:
            break
        elif len(track) > config.minTrack:
            frameNum = numpy.array([f["frame"] for f in track])
            bboxes = numpy.array([numpy.array(f["bbox"]) for f in track])
            frameI = numpy.arange(frameNum[0], frameNum[-1] + 1)
            bboxesI = []
            for ij in range(0, 4):
                interpfn = interp1d(frameNum, bboxes[:, ij])
                bboxesI.append(interpfn(frameI))
            bboxesI = numpy.stack(bboxesI, axis=1)
            if (
                max(
                    numpy.mean(bboxesI[:, 2] - bboxesI[:, 0]),
                    numpy.mean(bboxesI[:, 3] - bboxesI[:, 1]),
                )
                > config.minFaceSize
            ):
                tracks.append({"frame": frameI, "bbox": bboxesI})
    return tracks


def crop_video(config, track, cropFile):
    # CPU: crop the face clips
    flist = glob.glob(os.path.join(config.pyframesPath, "*.jpg"))  # Read the frames
    flist.sort()
    vOut = cv2.VideoWriter(
        cropFile + "t.avi", cv2.VideoWriter_fourcc(*"XVID"), 25, (224, 224)
    )  # Write video
    dets = {"x": [], "y": [], "s": []}
    for det in track["bbox"]:  # Read the tracks
        dets["s"].append(max((det[3] - det[1]), (det[2] - det[0])) / 2)
        dets["y"].append((det[1] + det[3]) / 2)  # crop center x
        dets["x"].append((det[0] + det[2]) / 2)  # crop center y
    dets["s"] = signal.medfilt(dets["s"], kernel_size=13)  # Smooth detections
    dets["x"] = signal.medfilt(dets["x"], kernel_size=13)
    dets["y"] = signal.medfilt(dets["y"], kernel_size=13)
    for fidx, frame in enumerate(track["frame"]):
        cs = config.cropScale
        bs = dets["s"][fidx]  # Detection box size
        bsi = int(bs * (1 + 2 * cs))  # Pad videos by this amount
        image = cv2.imread(flist[frame])
        frame = numpy.pad(
            image,
            ((bsi, bsi), (bsi, bsi), (0, 0)),
            "constant",
            constant_values=(110, 110),
        )
        my = dets["y"][fidx] + bsi  # BBox center Y
        mx = dets["x"][fidx] + bsi  # BBox center X
        face = frame[
            int(my - bs) : int(my + bs * (1 + 2 * cs)),
            int(mx - bs * (1 + cs)) : int(mx + bs * (1 + cs)),
        ]
        vOut.write(cv2.resize(face, (224, 224)))
    audioTmp = cropFile + ".wav"
    audioStart = (track["frame"][0]) / 25
    audioEnd = (track["frame"][-1] + 1) / 25
    vOut.release()
    command = (
        "ffmpeg -y -i %s -async 1 -ac 1 -vn -acodec pcm_s16le -ar 16000 -threads %d -ss %.3f -to %.3f %s -loglevel panic"
        % (
            config.audioFilePath,
            config.nDataLoaderThread,
            audioStart,
            audioEnd,
            audioTmp,
        )
    )
    output = subprocess.call(command, shell=True, stdout=None)  # Crop audio file
    _, audio = wavfile.read(audioTmp)
    command = (
        "ffmpeg -y -i %st.avi -i %s -threads %d -c:v copy -c:a copy %s.avi -loglevel panic"
        % (cropFile, audioTmp, config.nDataLoaderThread, cropFile)
    )  # Combine audio and video file
    output = subprocess.call(command, shell=True, stdout=None)
    os.remove(cropFile + "t.avi")
    return {"track": track, "proc_track": dets}


def extract_MFCC(file, outPath):
    # CPU: extract mfcc
    sr, audio = wavfile.read(file)
    mfcc = python_speech_features.mfcc(audio, sr)  # (N_frames, 13)   [1s = 100 frames]
    featuresPath = os.path.join(outPath, file.split("/")[-1].replace(".wav", ".npy"))
    numpy.save(featuresPath, mfcc)


def evaluate_network(files, config):
    # GPU: active speaker detection by pretrained TalkNet
    s = talkNet()
    s.loadParameters(config.pretrainModel)
    sys.stderr.write("Model %s loaded from previous state! \r\n" % config.pretrainModel)
    s.eval()
    allScores = []
    # durationSet = {1,2,4,6} # To make the result more reliable
    durationSet = {
        1,
        1,
        1,
        2,
        2,
        2,
        3,
        3,
        4,
        5,
        6,
    }  # Use this line can get more reliable result
    for file in tqdm.tqdm(files, total=len(files)):
        fileName = os.path.splitext(file.split("/")[-1])[0]  # Load audio and video
        _, audio = wavfile.read(os.path.join(config.pycropPath, fileName + ".wav"))
        audioFeature = python_speech_features.mfcc(
            audio, 16000, numcep=13, winlen=0.025, winstep=0.010
        )
        video = cv2.VideoCapture(os.path.join(config.pycropPath, fileName + ".avi"))
        videoFeature = []
        while video.isOpened():
            ret, frames = video.read()
            if ret:
                face = cv2.cvtColor(frames, cv2.COLOR_BGR2GRAY)
                face = cv2.resize(face, (224, 224))
                face = face[
                    int(112 - (112 / 2)) : int(112 + (112 / 2)),
                    int(112 - (112 / 2)) : int(112 + (112 / 2)),
                ]
                videoFeature.append(face)
            else:
                break
        video.release()
        videoFeature = numpy.array(videoFeature)
        length = min(
            (audioFeature.shape[0] - audioFeature.shape[0] % 4) / 100,
            videoFeature.shape[0] / 25,
        )
        audioFeature = audioFeature[: int(round(length * 100)), :]
        videoFeature = videoFeature[: int(round(length * 25)), :, :]
        allScore = []  # Evaluation use TalkNet
        for duration in durationSet:
            batchSize = int(math.ceil(length / duration))
            scores = []
            with torch.no_grad():
                for i in range(batchSize):
                    inputA = (
                        torch.FloatTensor(
                            audioFeature[
                                i * duration * 100 : (i + 1) * duration * 100, :
                            ]
                        )
                        .unsqueeze(0)
                        .cuda()
                    )
                    inputV = (
                        torch.FloatTensor(
                            videoFeature[
                                i * duration * 25 : (i + 1) * duration * 25, :, :
                            ]
                        )
                        .unsqueeze(0)
                        .cuda()
                    )
                    embedA = s.model.forward_audio_frontend(inputA)
                    embedV = s.model.forward_visual_frontend(inputV)
                    embedA, embedV = s.model.forward_cross_attention(embedA, embedV)
                    out = s.model.forward_audio_visual_backend(embedA, embedV)
                    score = s.lossAV.forward(out, labels=None)
                    scores.extend(score)
            allScore.append(scores)
        allScore = numpy.round((numpy.mean(numpy.array(allScore), axis=0)), 1).astype(
            float
        )
        allScores.append(allScore)
    return allScores


def visualization(tracks, scores, config):
    # CPU: visulize the result for video format
    flist = glob.glob(os.path.join(config.pyframesPath, "*.jpg"))
    flist.sort()
    faces = [[] for i in range(len(flist))]
    for tidx, track in enumerate(tracks):
        score = scores[tidx]
        for fidx, frame in enumerate(track["track"]["frame"].tolist()):
            s = score[
                max(fidx - 2, 0) : min(fidx + 3, len(score) - 1)
            ]  # average smoothing
            s = numpy.mean(s)
            faces[frame].append(
                {
                    "track": tidx,
                    "score": float(s),
                    "s": track["proc_track"]["s"][fidx],
                    "x": track["proc_track"]["x"][fidx],
                    "y": track["proc_track"]["y"][fidx],
                }
            )
    firstImage = cv2.imread(flist[0])
    fw = firstImage.shape[1]
    fh = firstImage.shape[0]
    vOut = cv2.VideoWriter(
        os.path.join(config.pyaviPath, "video_only.avi"),
        cv2.VideoWriter_fourcc(*"XVID"),
        25,
        (fw, fh),
    )
    colorDict = {0: 0, 1: 255}
    for fidx, fname in tqdm.tqdm(enumerate(flist), total=len(flist)):
        image = cv2.imread(fname)
        for face in faces[fidx]:
            clr = colorDict[int((face["score"] >= 0))]
            txt = round(face["score"], 1)
            cv2.rectangle(
                image,
                (int(face["x"] - face["s"]), int(face["y"] - face["s"])),
                (int(face["x"] + face["s"]), int(face["y"] + face["s"])),
                (0, clr, 255 - clr),
                10,
            )
            cv2.putText(
                image,
                "%s" % (txt),
                (int(face["x"] - face["s"]), int(face["y"] - face["s"])),
                cv2.FONT_HERSHEY_SIMPLEX,
                1.5,
                (0, clr, 255 - clr),
                5,
            )
        vOut.write(image)
    vOut.release()
    command = (
        "ffmpeg -y -i %s -i %s -threads %d -c:v copy -c:a copy %s -loglevel panic"
        % (
            os.path.join(config.pyaviPath, "video_only.avi"),
            os.path.join(config.pyaviPath, "audio.wav"),
            config.nDataLoaderThread,
            os.path.join(config.pyaviPath, "video_out.avi"),
        )
    )
    output = subprocess.call(command, shell=True, stdout=None)


def evaluate_col_ASD(tracks, scores, config):
    txtPath = config.videoFolder + "/col_labels/fusion/*.txt"  # Load labels
    predictionSet = {}
    for name in {"long", "bell", "boll", "lieb", "sick", "abbas"}:
        predictionSet[name] = [[], []]
    dictGT = {}
    txtFiles = glob.glob("%s" % txtPath)
    for file in txtFiles:
        lines = open(file).read().splitlines()
        idName = file.split("/")[-1][:-4]
        for line in lines:
            data = line.split("\t")
            frame = int(int(data[0]) / 29.97 * 25)
            x1 = int(data[1])
            y1 = int(data[2])
            x2 = int(data[1]) + int(data[3])
            y2 = int(data[2]) + int(data[3])
            gt = int(data[4])
            if frame in dictGT:
                dictGT[frame].append([x1, y1, x2, y2, gt, idName])
            else:
                dictGT[frame] = [[x1, y1, x2, y2, gt, idName]]
    flist = glob.glob(os.path.join(config.pyframesPath, "*.jpg"))  # Load files
    flist.sort()
    faces = [[] for i in range(len(flist))]
    for tidx, track in enumerate(tracks):
        score = scores[tidx]
        for fidx, frame in enumerate(track["track"]["frame"].tolist()):
            s = numpy.mean(
                score[max(fidx - 2, 0) : min(fidx + 3, len(score) - 1)]
            )  # average smoothing
            faces[frame].append(
                {
                    "track": tidx,
                    "score": float(s),
                    "s": track["proc_track"]["s"][fidx],
                    "x": track["proc_track"]["x"][fidx],
                    "y": track["proc_track"]["y"][fidx],
                }
            )
    for fidx, fname in tqdm.tqdm(enumerate(flist), total=len(flist)):
        if fidx in dictGT:  # This frame has label
            for gtThisFrame in dictGT[fidx]:  # What this label is ?
                faceGT = gtThisFrame[0:4]
                labelGT = gtThisFrame[4]
                idGT = gtThisFrame[5]
                ious = []
                for face in faces[fidx]:  # Find the right face in my result
                    faceLocation = [
                        int(face["x"] - face["s"]),
                        int(face["y"] - face["s"]),
                        int(face["x"] + face["s"]),
                        int(face["y"] + face["s"]),
                    ]
                    faceLocation_new = [
                        int(face["x"] - face["s"]) // 2,
                        int(face["y"] - face["s"]) // 2,
                        int(face["x"] + face["s"]) // 2,
                        int(face["y"] + face["s"]) // 2,
                    ]
                    iou = bb_intersection_over_union(
                        faceLocation_new, faceGT, evalCol=True
                    )
                    if iou > 0.5:
                        ious.append([iou, round(face["score"], 2)])
                if len(ious) > 0:  # Find my result
                    ious.sort()
                    labelPredict = ious[-1][1]
                else:
                    labelPredict = 0
                x1 = faceGT[0]
                y1 = faceGT[1]
                width = faceGT[2] - faceGT[0]
                predictionSet[idGT][0].append(labelPredict)
                predictionSet[idGT][1].append(labelGT)
    names = ["long", "bell", "boll", "lieb", "sick", "abbas"]  # Evaluate
    names.sort()
    F1s = 0
    for i in names:
        scores = numpy.array(predictionSet[i][0])
        labels = numpy.array(predictionSet[i][1])
        scores = numpy.int64(scores > 0)
        F1 = f1_score(labels, scores)
        ACC = accuracy_score(labels, scores)
        if i != "abbas":
            F1s += F1
            print("%s, ACC:%.2f, F1:%.2f" % (i, 100 * ACC, 100 * F1))
    print("Average F1:%.2f" % (100 * (F1s / 5)))


# Process a single video
def process_video(config):
    # This preprocesstion is modified based on this [repository](https://github.com/joonson/syncnet_python).
    # ```
    # .
    # ├── pyavi
    # │   ├── audio.wav (Audio from input video)
    # │   ├── video.avi (Copy of the input video)
    # │   ├── video_only.avi (Output video without audio)
    # │   └── video_out.avi  (Output video with audio)
    # ├── pycrop (The detected face videos and audios)
    # │   ├── 000000.avi
    # │   ├── 000000.wav
    # │   ├── 000001.avi
    # │   ├── 000001.wav
    # │   └── ...
    # ├── pyframes (All the video frames in this video)
    # │   ├── 000001.jpg
    # │   ├── 000002.jpg
    # │   └── ...
    # └── pywork
    #     ├── faces.pckl (face detection result)
    #     ├── scene.pckl (scene detection result)
    #     ├── scores.pckl (ASD result)
    #     └── tracks.pckl (face tracking result)
    # ```

    # Initialization
    config.pyaviPath = os.path.join(config.savePath, config.pyavi_dir)
    config.pyframesPath = os.path.join(config.savePath, config.pyframes_dir)
    config.pyworkPath = os.path.join(config.savePath, config.pywork_dir)
    config.pycropPath = os.path.join(config.savePath, config.pycrop_dir)

    if os.path.exists(config.savePath):
        rmtree(config.savePath)

    # Create directories
    os.makedirs(
        config.pyaviPath, exist_ok=True
    )  # The path for the input video, input audio, output video
    os.makedirs(config.pyframesPath, exist_ok=True)  # Save all the video frames
    os.makedirs(
        config.pyworkPath, exist_ok=True
    )  # Save the results in this process by the pckl method
    os.makedirs(
        config.pycropPath, exist_ok=True
    )  # Save the detected face clips (audio+video) in this process

    # Extract video
    config.videoFilePath = os.path.join(config.pyaviPath, "video.avi")
    # If duration did not set, extract the whole video, otherwise extract the video from 'config.start' to 'config.start + config.duration'
    if config.duration == 0:
        command = (
            "ffmpeg -y -i %s -qscale:v 2 -threads %d -async 1 -r 25 %s -loglevel panic"
            % (config.videoPath, config.nDataLoaderThread, config.videoFilePath)
        )
    else:
        command = (
            "ffmpeg -y -i %s -qscale:v 2 -threads %d -ss %.3f -to %.3f -async 1 -r 25 %s -loglevel panic"
            % (
                config.videoPath,
                config.nDataLoaderThread,
                config.start,
                config.start + config.duration,
                config.videoFilePath,
            )
        )
    subprocess.call(command, shell=True, stdout=None)
    sys.stderr.write(
        time.strftime("%Y-%m-%d %H:%M:%S")
        + " Extract the video and save in %s \r\n" % (config.videoFilePath)
    )

    # Extract audio
    config.audioFilePath = os.path.join(config.pyaviPath, "audio.wav")
    command = (
        "ffmpeg -y -i %s -qscale:a 0 -ac 1 -vn -threads %d -ar 16000 %s -loglevel panic"
        % (config.videoFilePath, config.nDataLoaderThread, config.audioFilePath)
    )
    subprocess.call(command, shell=True, stdout=None)
    sys.stderr.write(
        time.strftime("%Y-%m-%d %H:%M:%S")
        + " Extract the audio and save in %s \r\n" % (config.audioFilePath)
    )

    # Extract the video frames
    frames_pattern = os.path.join(config.pyframesPath, "%06d.jpg")
    command = "ffmpeg -y -i %s -qscale:v 2 -threads %d -f image2 %s -loglevel panic" % (
        config.videoFilePath,
        config.nDataLoaderThread,
        frames_pattern,
    )
    subprocess.call(command, shell=True, stdout=None)
    sys.stderr.write(
        time.strftime("%Y-%m-%d %H:%M:%S")
        + " Extract the frames and save in %s \r\n" % (config.pyframesPath)
    )

    # Scene detection for the video frames
    scene = scene_detect(config)
    sys.stderr.write(
        time.strftime("%Y-%m-%d %H:%M:%S")
        + " Scene detection and save in %s \r\n" % (config.pyworkPath)
    )

    # Face detection for the video frames
    faces = inference_video(config)
    sys.stderr.write(
        time.strftime("%Y-%m-%d %H:%M:%S")
        + " Face detection and save in %s \r\n" % (config.pyworkPath)
    )

    # Face tracking
    allTracks, vidTracks = [], []
    for shot in scene:
        if (
            shot[1].frame_num - shot[0].frame_num >= config.minTrack
        ):  # Discard the shot frames less than minTrack frames
            allTracks.extend(
                track_shot(config, faces[shot[0].frame_num : shot[1].frame_num])
            )  # 'frames' to present this tracks' timestep, 'bbox' presents the location of the faces
    sys.stderr.write(
        time.strftime("%Y-%m-%d %H:%M:%S")
        + " Face track and detected %d tracks \r\n" % len(allTracks)
    )

    # Face clips cropping
    for ii, track in tqdm.tqdm(enumerate(allTracks), total=len(allTracks)):
        vidTracks.append(
            crop_video(config, track, os.path.join(config.pycropPath, "%05d" % ii))
        )
    savePath = os.path.join(config.pyworkPath, "tracks.pckl")
    with open(savePath, "wb") as fil:
        pickle.dump(vidTracks, fil)
    sys.stderr.write(
        time.strftime("%Y-%m-%d %H:%M:%S")
        + " Face Crop and saved in %s tracks \r\n" % config.pycropPath
    )
    fil = open(savePath, "rb")
    vidTracks = pickle.load(fil)

    # Active Speaker Detection by TalkNet
    files = glob.glob("%s/*.avi" % config.pycropPath)
    files.sort()
    scores = evaluate_network(files, config)
    print(scores)
    savePath = os.path.join(config.pyworkPath, "scores.pckl")
    with open(savePath, "wb") as fil:
        pickle.dump(scores, fil)
    sys.stderr.write(
        time.strftime("%Y-%m-%d %H:%M:%S")
        + " Scores extracted and saved in %s \r\n" % config.pyworkPath
    )

    if config.evalCol:
        evaluate_col_ASD(
            vidTracks, scores, config
        )  # The columnbia video is too big for visualization. You can still add the `visualization` funcition here if you want
        return
    else:
        # Visualization, save the result as the new video
        visualization(vidTracks, scores, config)


# Main function
def main():
    # Create directories if they don't exist
    pretrain_model_dir = os.path.dirname(
        os.path.join(str(PROJECT_ROOT), PATHS_CONFIG.pretrained.talknet_model)
    )
    os.makedirs(pretrain_model_dir, exist_ok=True)

    demo_folder_path = os.path.join(
        str(PROJECT_ROOT), PATHS_CONFIG.talknet.demo_video_folder
    )
    os.makedirs(demo_folder_path, exist_ok=True)

    col_save_path = os.path.join(str(PROJECT_ROOT), PATHS_CONFIG.talknet.col_save_path)
    os.makedirs(col_save_path, exist_ok=True)

    # Check if pretrained model exists, download if not
    pretrain_model_path = os.path.join(
        str(PROJECT_ROOT), PATHS_CONFIG.pretrained.talknet_model
    )
    if not os.path.exists(pretrain_model_path):
        model_id = PATHS_CONFIG.pretrained.talknet_model_id
        cmd = f"gdown --id {model_id} -O {pretrain_model_path}"
        subprocess.call(cmd, shell=True, stdout=None)

    # Handle Columbia dataset evaluation if enabled in config
    if TALKNET_CONFIG.evaluation.eval_col:
        config = Config(PATHS_CONFIG, TALKNET_CONFIG, PROJECT_ROOT)

        # Download video if needed
        if not os.path.exists(config.videoPath):
            video_url = PATHS_CONFIG.columbia.video_url
            cmd = f"youtube-dl -f best -o {config.videoPath} '{video_url}'"
            output = subprocess.call(cmd, shell=True, stdout=None)

        # Download labels if needed
        col_labels_dir = os.path.join(config.videoFolder, "col_labels")
        if not os.path.isdir(col_labels_dir):
            labels_id = PATHS_CONFIG.columbia.labels_id
            labels_archive = os.path.join(config.videoFolder, "col_labels.tar.gz")
            cmd = f"gdown --id {labels_id} -O {labels_archive}"
            subprocess.call(cmd, shell=True, stdout=None)
            cmd = f"tar -xzvf {labels_archive} -C {config.videoFolder}"
            subprocess.call(cmd, shell=True, stdout=None)
            os.remove(labels_archive)  # Remove the archive file

        process_video(config)
    else:
        # If videoName is empty in config, process all video files in the directory
        video_name = TALKNET_CONFIG.video.video_name
        video_folder = os.path.join(
            str(PROJECT_ROOT), PATHS_CONFIG.talknet.demo_video_folder
        )

        if not video_name:
            # Get all video files in the directory
            video_extensions = [".mp4", ".avi", ".mov", ".mkv", ".wmv"]
            all_files = glob.glob(os.path.join(video_folder, "*.*"))
            video_files = [
                f
                for f in all_files
                if os.path.splitext(f)[1].lower() in video_extensions
            ]

            if not video_files:
                print(f"No video files found in directory: {video_folder}")
                return

            print(f"Found {len(video_files)} videos. Processing all videos...")
            for video_file in video_files:
                video_name = os.path.splitext(os.path.basename(video_file))[0]
                print(f"Processing video: {video_name}")
                config = Config(
                    PATHS_CONFIG, TALKNET_CONFIG, PROJECT_ROOT, video_name=video_name
                )
                config.videoPath = video_file
                config.savePath = os.path.join(config.videoFolder, video_name)
                process_video(config)
                print(f"Completed processing video: {video_name}")
        else:
            # Process a single video specified in the config
            config = Config(PATHS_CONFIG, TALKNET_CONFIG, PROJECT_ROOT)
            process_video(config)


if __name__ == "__main__":
    main()
