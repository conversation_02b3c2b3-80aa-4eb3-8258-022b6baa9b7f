"""
TalkNet Wrapper Module

Provides efficient access to TalkNet's active speaker detection scores by reusing cached data
when possible. This wrapper optimizes the processing pipeline by avoiding redundant steps
and leveraging existing preprocessed data.
"""

import os
import sys
import glob
import pickle
import subprocess
from typing import List, Dict, Optional
from concurrent.futures import ProcessPoolExecutor, as_completed
from functools import partial

import torch
from omegaconf import OmegaConf

from src.models.talknet.demoTalkNet import (
    Config,
    scene_detect,
    inference_video,
    track_shot,
    crop_video,
    evaluate_network,
    load_config,
)


def cleanup_temp_files(config):
    """
    Clean up all temporary processing files except scores.pckl.

    Args:
        config: Config object containing paths to clean up
    """
    import shutil

    try:
        # Remove the entire processing directory
        if os.path.exists(config.savePath):
            shutil.rmtree(config.savePath)
    except Exception as e:
        print(f"Warning: Error during cleanup - {str(e)}")


def get_video_scores(
    video_path: str, debug: bool = True, gpu_id: Optional[int] = None
) -> list:
    """
    Get active speaker detection scores for a video, optimized through caching.

    Args:
        video_path (str): Path to the video file to process
        debug (bool, optional): If False, cleanup temporary files after processing. Defaults to True.

    Returns:
        list: List of per-track active speaker detection scores from evaluate_network

    Example:
        >>> scores = get_video_scores("/path/to/video.mp4", debug=False)
        >>> print(f"Found {len(scores)} tracks with scores")
    """
    # Set GPU device if specified
    if gpu_id is not None:
        torch.cuda.set_device(gpu_id)

    # Load configs for paths and processing parameters
    paths_config, talknet_config, project_root = load_config()

    # Check and download pretrained model if needed
    pretrain_model_path = os.path.join(
        project_root, paths_config.pretrained.talknet_model
    )
    if not os.path.exists(pretrain_model_path):
        print(f"Downloading pretrained model to {pretrain_model_path}")
        os.makedirs(os.path.dirname(pretrain_model_path), exist_ok=True)
        model_id = paths_config.pretrained.talknet_model_id
        cmd = f"gdown --id {model_id} -O {pretrain_model_path}"
        subprocess.call(cmd, shell=True, stdout=None)
        if not os.path.exists(pretrain_model_path):
            raise RuntimeError(
                f"Failed to download pretrained model to {pretrain_model_path}"
            )
        print("Pretrained model downloaded successfully")

    # Get video name and directory from path
    video_name = os.path.splitext(os.path.basename(video_path))[0]
    video_dir = os.path.dirname(video_path)

    # Create config with video-specific paths
    config = Config(paths_config, talknet_config, project_root)

    # Override paths to use video location
    config.videoPath = video_path
    config.savePath = os.path.join(video_dir, f"{video_name}_talknet")

    # Set up processing directories
    config.pyaviPath = os.path.join(config.savePath, config.pyavi_dir)
    config.pyframesPath = os.path.join(config.savePath, config.pyframes_dir)
    config.pyworkPath = os.path.join(config.savePath, config.pywork_dir)
    config.pycropPath = os.path.join(config.savePath, config.pycrop_dir)

    # Create directories if they don't exist
    for path in [
        config.pyaviPath,
        config.pyframesPath,
        config.pyworkPath,
        config.pycropPath,
    ]:
        os.makedirs(path, exist_ok=True)

    # Check for cached scores
    scores_path = os.path.join(config.pyworkPath, "scores.pckl")
    if os.path.exists(scores_path):
        with open(scores_path, "rb") as f:
            return pickle.load(f)

    # Check for cached tracks
    tracks_path = os.path.join(config.pyworkPath, "tracks.pckl")
    if os.path.exists(tracks_path):
        with open(tracks_path, "rb") as f:
            vidTracks = pickle.load(f)
    else:
        # Need to run preprocessing

        # Extract video if not already done
        config.videoFilePath = os.path.join(config.pyaviPath, "video.avi")
        if not os.path.exists(config.videoFilePath):
            if config.duration == 0:
                command = (
                    f"ffmpeg -y -i {config.videoPath} -qscale:v 2 -threads {config.nDataLoaderThread} "
                    f"-async 1 -r 25 {config.videoFilePath} -loglevel panic"
                )
            else:
                command = (
                    f"ffmpeg -y -i {config.videoPath} -qscale:v 2 -threads {config.nDataLoaderThread} "
                    f"-ss {config.start} -to {config.start + config.duration} "
                    f"-async 1 -r 25 {config.videoFilePath} -loglevel panic"
                )
            subprocess.call(command, shell=True, stdout=None)

        # Extract audio if not already done
        config.audioFilePath = os.path.join(config.pyaviPath, "audio.wav")
        if not os.path.exists(config.audioFilePath):
            command = (
                f"ffmpeg -y -i {config.videoFilePath} -qscale:a 0 -ac 1 -vn -threads {config.nDataLoaderThread} "
                f"-ar 16000 {config.audioFilePath} -loglevel panic"
            )
            subprocess.call(command, shell=True, stdout=None)

        # Extract frames if not already done
        frames_exist = glob.glob(os.path.join(config.pyframesPath, "*.jpg"))
        if not frames_exist:
            frames_pattern = os.path.join(config.pyframesPath, "%06d.jpg")
            command = (
                f"ffmpeg -y -i {config.videoFilePath} -qscale:v 2 -threads {config.nDataLoaderThread} "
                f"-f image2 {frames_pattern} -loglevel panic"
            )
            subprocess.call(command, shell=True, stdout=None)

        # Run scene detection
        scene = scene_detect(config)

        # Run face detection
        faces = inference_video(config)

        # Face tracking and cropping
        allTracks = []
        vidTracks = []
        for shot in scene:
            if shot[1].frame_num - shot[0].frame_num >= config.minTrack:
                allTracks.extend(
                    track_shot(config, faces[shot[0].frame_num : shot[1].frame_num])
                )

        # Process each track
        for ii, track in enumerate(allTracks):
            vidTracks.append(
                crop_video(config, track, os.path.join(config.pycropPath, "%05d" % ii))
            )

        # Save tracks for future use
        with open(tracks_path, "wb") as f:
            pickle.dump(vidTracks, f)

    # Get cropped files and evaluate
    files = glob.glob(os.path.join(config.pycropPath, "*.avi"))
    files.sort()

    scores = evaluate_network(files, config)

    # Cache scores for future use
    with open(scores_path, "wb") as f:
        pickle.dump(scores, f)

    # Clean up temporary files if debug mode is off
    if not debug:
        cleanup_temp_files(config)

    return scores


def process_single_video(video_path: str, gpu_id: int, debug: bool = True) -> Dict:
    """
    Process a single video on a specific GPU.

    Args:
        video_path (str): Path to the video file
        gpu_id (int): GPU device ID to use
        debug (bool): If True, keep temporary files

    Returns:
        Dict containing video path and its scores
    """
    try:
        scores = get_video_scores(video_path, debug=debug, gpu_id=gpu_id)
        return {
            "video_path": video_path,
            "scores": scores,
            "success": True,
            "error": None,
        }
    except Exception as e:
        return {
            "video_path": video_path,
            "scores": None,
            "success": False,
            "error": str(e),
        }


def get_demo_videos() -> List[str]:
    """
    Get list of video files from the demo directory specified in paths config.

    Returns:
        List[str]: List of video file paths
    """
    paths_config, _, project_root = load_config()
    demo_dir = os.path.join(project_root, paths_config.talknet.demo_video_folder)

    # Create demo directory if it doesn't exist
    os.makedirs(demo_dir, exist_ok=True)

    # Get all video files with common extensions
    video_extensions = [".mp4", ".avi", ".mov", ".mkv", ".wmv"]
    videos = []
    for ext in video_extensions:
        videos.extend(glob.glob(os.path.join(demo_dir, f"*{ext}")))

    return sorted(videos)


def process_videos_parallel(
    videos: Optional[List[str]] = None, debug: bool = True
) -> List[Dict]:
    """
    Process multiple videos in parallel using available GPUs.

    Args:
        videos (Optional[List[str]]): List of video paths. If None, processes all videos in demo folder.
        debug (bool): If True, keep temporary files after processing

    Returns:
        List[Dict]: List of dictionaries containing results for each video

    Example:
        >>> results = process_videos_parallel(debug=False)
        >>> for result in results:
        ...     if result["success"]:
        ...         print(f"Processed {result['video_path']}: {len(result['scores'])} tracks")
        ...     else:
        ...         print(f"Failed to process {result['video_path']}: {result['error']}")
    """
    # Get number of available GPUs
    num_gpus = torch.cuda.device_count()
    if num_gpus == 0:
        raise RuntimeError("No CUDA-capable GPUs found")

    # Get list of videos if not provided
    if videos is None:
        videos = get_demo_videos()

    if not videos:
        raise ValueError("No videos found to process")

    print(f"Found {len(videos)} videos to process using {num_gpus} GPUs")

    # Create ProcessPoolExecutor with number of workers equal to number of GPUs
    with ProcessPoolExecutor(max_workers=num_gpus) as executor:
        # Create partial function with debug parameter
        process_fn = partial(process_single_video, debug=debug)

        # Process videos in parallel, assigning each to a GPU round-robin
        # Submit all tasks
        future_to_video = {
            executor.submit(process_fn, video_path, i % num_gpus): video_path
            for i, video_path in enumerate(videos)
        }

        # Gather results as they complete
        results = []
        for future in as_completed(future_to_video):
            results.append(future.result())

        return results
