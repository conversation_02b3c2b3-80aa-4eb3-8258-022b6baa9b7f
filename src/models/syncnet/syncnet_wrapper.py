import glob
import subprocess
from pathlib import Path
from typing import Optional
import os
import pickle
import torch
from src.models.syncnet.syncnet_detect import SyncNetDetector
from src.models.syncnet.syncnet_eval import SyncNetEval


# import glob
class SyncNetWrapper:
    """
    Wrapper for Sync<PERSON> to preprocess videos and get sync scores.
    """

    def __init__(
        self,
        video_path: str,
        output_path: str,
        device="cuda" if torch.cuda.is_available() else "cpu",
    ):
        self.video_path = video_path
        self.output_path = output_path
        self.video_name = Path(video_path).stem
        self.device = device

        # Initialize the SyncNet detector
        self.detector = SyncNetDetector(
            device=device, detect_results_dir=os.path.join(output_path, self.video_name)
        )

    def preprocess_one_video(self, min_track=50, scale=False):
        """
        Preprocess the video using syncnet pipeline.
        Uses the integrated SyncNetDetector instead of subprocess call.

        Args:
            min_track (int): Minimum track length for face tracking
            scale (bool): Whether to scale video to 224x224
        """
        try:
            # Call the main processing method from SyncNetDetector
            self.detector(self.video_path, min_track=min_track, scale=scale)
            print(f"Successfully preprocessed video: {self.video_name}")

        except Exception as e:
            print(f"Error preprocessing video {self.video_name}: {str(e)}")
            raise

    def get_sync_score(self):
        """
        Get the sync score for a single video.
        """

        class options:
            pass

        opt = options()
        opt.data_dir = self.output_path
        opt.videofile = self.video_path
        opt.reference = self.video_name
        opt.data_dir = f"{self.output_path}/{self.video_name}"
        opt.batch_size = 20
        opt.vshift = 15
        print(opt.data_dir)
        setattr(opt, "avi_dir", os.path.join(opt.data_dir, "video"))
        setattr(opt, "tmp_dir", os.path.join(opt.data_dir, "frames"))
        setattr(opt, "work_dir", os.path.join(opt.data_dir, "video"))
        setattr(opt, "crop_dir", os.path.join(opt.data_dir, "crop"))
        setattr(opt, "frames_dir", os.path.join(opt.data_dir, "frames"))
        s = SyncNetEval()
        s.loadParameters("src/models/pretrained/syncnet_v2.model")
        flist = glob.glob(os.path.join(opt.crop_dir, "0*.avi"))
        flist.sort()
        max_score = float("-inf")
        best_offset = None

        for file in flist:
            print(f"Processing {file}")
            offset, _, current_score = s.evaluate(video_path=file, temp_dir=opt.tmp_dir)
            print("current score is", current_score)

            if current_score > max_score:
                max_score = current_score
                best_offset = offset

        print("max_score is:", max_score)
        print("best_offset is:", best_offset)
        return best_offset, max_score
