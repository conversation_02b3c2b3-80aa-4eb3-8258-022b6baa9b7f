import numpy as np
import matplotlib.pyplot as plt
import os
import argparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
import numpy as np
import datetime
import shutil
from src.models.syncnet.syncnet_wrapper import SyncNetWrapper
import sys
from src.generate_scenes import FaceSegmentProcessor
from src.generate_tracks import FaceTracker
import glob


class AutomaticActiveSpeakerDetector:
    """
    A class for detecting active speakers in video segments using SyncNet models.

    This detector processes video tracks and determines which speakers are active by:
    - Using SyncNet for all tracks (both grouped and ungrouped)
    - Applying 1.0 threshold to all sync scores for initial filtering
    - Applying offset-based filtering (-3 to 3 range) for final validation
    - Debug mode controls NPZ file saving and video copying based on final validation
    - In debug mode, generates SyncNet plots with offset-based labeling
    """

    def __init__(
        self,
        video_dir: str,
        npz_dir: str,
        output_dir: str,
        workers: int = 4,
        debug: bool = False,
    ):
        """
        Initialize the AutomaticActiveSpeakerDetector.

        Args:
            video_dir (str): Directory containing video files (.mp4)
            npz_dir (str): Directory containing track metadata files (.npz)
            output_dir (str): Directory to save results
            workers (int, optional): Number of parallel workers. Defaults to CPU count.
            debug (bool): If True, save all NPZ confidence files and generate plots. If False, remove videos that don't pass validation
        """
        self.video_path = Path(video_dir)
        self.npz_path = Path(npz_dir)
        self.output_path = Path(output_dir)
        self.workers = workers
        self.failed_tracks = []
        self.debug = debug

        # Two-stage filtering: score-based then offset-based
        self.high_score_videos = set()  # Videos with score > 1.0
        self.low_score_videos = set()  # Videos with score <= 1.0
        self.valid_videos = set()  # Videos with score > 1.0 AND offset -3 to 3
        self.invalid_videos = set()  # Videos that fail either condition

        self.plot_results = []  # Store plot data for debug mode

        # Create output directory if needed
        self.output_path.mkdir(parents=True, exist_ok=True)

    def _load_track_data(self):
        """
        Load and pair video files with their corresponding NPZ metadata files.

        Returns:
            list: List of track dictionaries containing video info and metadata
        """
        # Pair .mp4 & .npz by stem
        video_files = {f.stem: f for f in self.video_path.glob("*.mp4")}
        npz_files = {f.stem: f for f in self.npz_path.glob("*.npz")}
        pairs = [(video_files[s], npz_files[s]) for s in video_files if s in npz_files]

        def process_track(vid_file, npz_file):
            try:
                data = np.load(npz_file, allow_pickle=True)
                return {
                    "vid": vid_file,
                    "npz": npz_file,
                    "start_frame": data["start_frame"].item(),
                    "has_silence": bool(data["has_silence"]),
                    "track_id": str(data["track_id"]),
                }
            except Exception as e:
                track_id = npz_file.stem.split("_track")[-1]
                self.failed_tracks.append(
                    {
                        "track_id": track_id,
                        "video": vid_file.name,
                        "npz": npz_file.name,
                        "error": str(e),
                        "timestamp": datetime.datetime.now().isoformat(),
                    }
                )
                return None

        # Process all tracks in parallel
        with ThreadPoolExecutor(max_workers=self.workers) as executor:
            futures = [executor.submit(process_track, vid, npz) for vid, npz in pairs]
            tracks = [f.result() for f in as_completed(futures) if f.result()]

        return tracks

    def _categorize_tracks(self, tracks):
        """
        Categorize tracks into grouped and ungrouped based on start frame frequency.

        Args:
            tracks (list): List of track dictionaries

        Returns:
            tuple: (grouped_tracks, ungrouped_tracks, grouped_dict)
        """
        # Count occurrences of each start_frame
        start_frame_counts = {}
        for track in tracks:
            sf = track["start_frame"]
            start_frame_counts[sf] = start_frame_counts.get(sf, 0) + 1

        # Separate tracks into grouped and ungrouped
        grouped_tracks = []
        ungrouped_tracks = []

        for track in tracks:
            sf = track["start_frame"]
            if start_frame_counts[sf] >= 2:
                grouped_tracks.append(track)
            else:
                ungrouped_tracks.append(track)

        # Group tracks by start_frame
        grouped = {}
        for track in grouped_tracks:
            sf = track["start_frame"]
            if sf not in grouped:
                grouped[sf] = []
            grouped[sf].append(track)

        return grouped_tracks, ungrouped_tracks, grouped

    def _track_video_stats(self, track, score, offset):
        """
        Track video path for filtering based on two-stage process:
        1. Score-based filtering (threshold 1.0)
        2. Offset-based validation (-3 to 3 range)

        Args:
            track (dict): Track information dictionary
            score (float): Sync score
            offset (float): Sync offset
        """
        video_path = track["vid"]

        # Stage 1: Score-based filtering
        if score > 1.0:
            print(
                f"✅ High-score video: {video_path.name} (score: {score:.3f}, offset: {offset:.2f})"
            )
            self.high_score_videos.add(video_path)

            # Stage 2: Offset-based validation (only for high-score videos)
            if -3 <= offset <= 3:
                print(f"🎯 Valid video: {video_path.name} (offset in range)")
                self.valid_videos.add(video_path)
            else:
                print(f"❌ Invalid video: {video_path.name} (offset out of range)")
                self.invalid_videos.add(video_path)
        else:
            print(f"❌ Low-score video: {video_path.name} (score: {score:.3f})")
            self.low_score_videos.add(video_path)
            self.invalid_videos.add(
                video_path
            )  # Low score videos are automatically invalid

    def _generate_syncnet_plot(self, track, offset, score):
        """
        Generate SyncNet plot for a track in debug mode.

        Args:
            track (dict): Track information dictionary
            offset (float): SyncNet offset value
            score (float): SyncNet confidence score
        """
        if not self.debug:
            return

        try:
            # Extract video name and track ID for file naming
            video_name = track["vid"].stem
            track_id = track["track_id"]

            # Determine speaker label based on offset
            if -3 <= offset <= 3:
                speaker_label = "true_speaker"
            else:
                speaker_label = "false_speaker"

            # Construct paths for SyncNet output files
            # Assuming SyncNet wrapper saves files in a specific structure
            base_dir = self.output_path / f"{video_name}" / "pycrop" / f"{video_name}"
            video_identifier = "00000"  # Typically first face in track

            unsmooth_path = base_dir / f"{video_identifier}.avi_unprocessed_fconfm.npy"
            smooth_path = (
                base_dir / f"{video_identifier}_window_size_24_smoothened_fconfm.npy"
            )

            # Check if SyncNet output files exist
            if not (unsmooth_path.exists() and smooth_path.exists()):
                print(
                    f"⚠️  SyncNet output files not found for {video_name}, skipping plot"
                )
                return

            # Load SyncNet scores
            unsmooth_scores = np.load(unsmooth_path, allow_pickle=True)
            smooth_scores = np.load(smooth_path, allow_pickle=True)

            # Get min and max values for unsmooth scores
            max_score = np.max(unsmooth_scores)
            min_score = np.min(unsmooth_scores)
            max_index = np.argmax(unsmooth_scores)
            min_index = np.argmin(unsmooth_scores)

            # Get min and max values for smooth scores
            max_score_smooth = np.max(smooth_scores)
            min_score_smooth = np.min(smooth_scores)
            max_index_smooth = np.argmax(smooth_scores)
            min_index_smooth = np.argmin(smooth_scores)

            # Create the plot
            plt.figure(figsize=(12, 6))
            plt.plot(
                range(len(unsmooth_scores)),
                unsmooth_scores,
                linestyle="-",
                color="b",
                label="Raw SyncNet Score",
            )
            plt.plot(
                range(len(smooth_scores)),
                smooth_scores,
                linestyle="--",
                color="r",
                label="Smoothed SyncNet Score",
            )

            # Add max/min dots and annotations
            plt.scatter(
                max_index, max_score, color="blue", s=100, zorder=5, label="Max Raw"
            )
            plt.scatter(
                min_index, min_score, color="blue", s=100, zorder=5, label="Min Raw"
            )
            plt.text(
                max_index,
                max_score,
                f"Max: {max_score:.2f}",
                color="black",
                fontsize=11,
                ha="right",
                va="bottom",
            )
            plt.text(
                min_index,
                min_score,
                f"Min: {min_score:.2f}",
                color="black",
                fontsize=11,
                ha="right",
                va="top",
            )

            plt.scatter(
                max_index_smooth,
                max_score_smooth,
                color="red",
                s=100,
                zorder=5,
                label="Max Smooth",
            )
            plt.scatter(
                min_index_smooth,
                min_score_smooth,
                color="red",
                s=100,
                zorder=5,
                label="Min Smooth",
            )
            plt.text(
                max_index_smooth,
                max_score_smooth,
                f"Max: {max_score_smooth:.2f}",
                color="black",
                fontsize=11,
                ha="left",
                va="bottom",
            )
            plt.text(
                min_index_smooth,
                min_score_smooth,
                f"Min: {min_score_smooth:.2f}",
                color="black",
                fontsize=11,
                ha="left",
                va="top",
            )

            plt.title(
                f"SyncNet Scores - {speaker_label.replace('_', ' ').title()}\n{video_name} (track{track_id}) - Offset: {offset:.2f}, Score: {score:.3f}",
                fontsize=14,
            )
            plt.xlabel("Frame Index", fontsize=12)
            plt.ylabel("SyncNet Confidence", fontsize=12)
            plt.grid(True, alpha=0.3)
            plt.legend(fontsize=10)
            plt.xticks(fontsize=10)
            plt.yticks(fontsize=10)
            plt.tight_layout()

            # Save plot in root output directory with speaker label
            output_filename = (
                f"syncnet_{video_name}_track{track_id}_{speaker_label}.png"
            )
            output_path = self.output_path / output_filename
            plt.savefig(output_path, dpi=300, bbox_inches="tight")
            plt.close()  # Close the figure to free memory

            print(f"📊 Plot saved: {output_filename}")

            # Store plot info for summary
            self.plot_results.append(
                {
                    "video_name": video_name,
                    "track_id": track_id,
                    "offset": offset,
                    "score": score,
                    "speaker_label": speaker_label,
                    "plot_file": output_filename,
                }
            )

        except Exception as e:
            print(f"⚠️  Failed to generate plot for {track['vid'].name}: {e}")

    def _process_track_with_syncnet(self, track):
        """
        Process a single track using SyncNet (unified processing for all tracks).

        Args:
            track (dict): Track information dictionary

        Returns:
            dict or None: Score result or None if failed
        """
        try:
            sn = SyncNetWrapper(video_path=track["vid"], output_path=self.output_path)
            sn.preprocess_one_video()
            offset, score = sn.get_sync_score()

            # Track video score for two-stage filtering
            self._track_video_stats(track, score, offset)

            # Generate plot in debug mode
            if self.debug:
                self._generate_syncnet_plot(track, offset, score)

            return {
                "track_id": track["track_id"],
                "score": score,
                "offset": offset,
                "model": "syncnet",
            }
        except Exception as e:
            self.failed_tracks.append(
                {
                    "track_id": track["track_id"],
                    "video": track["vid"].name,
                    "error": str(e),
                    "timestamp": datetime.datetime.now().isoformat(),
                }
            )
            return None

    def _process_grouped_tracks(self, grouped):
        """
        Process all grouped tracks in parallel using SyncNet.

        Args:
            grouped (dict): Dictionary of grouped tracks by start_frame

        Returns:
            dict: Scored groups dictionary
        """
        scored_groups = {}
        for start_frame, tracks_group in grouped.items():
            scored_groups[start_frame] = []
            with ThreadPoolExecutor(max_workers=self.workers) as executor:
                futures = [
                    executor.submit(self._process_track_with_syncnet, track)
                    for track in tracks_group
                ]
                results = [f.result() for f in as_completed(futures) if f.result()]
                scored_groups[start_frame] = results

        return scored_groups

    def _process_ungrouped_tracks(self, ungrouped_tracks):
        """
        Process all ungrouped tracks in parallel using SyncNet.

        Args:
            ungrouped_tracks (list): List of ungrouped track dictionaries

        Returns:
            list: List of ungrouped results
        """
        ungrouped_results = []
        if ungrouped_tracks:
            with ThreadPoolExecutor(max_workers=self.workers) as executor:
                futures = [
                    executor.submit(self._process_track_with_syncnet, track)
                    for track in ungrouped_tracks
                ]
                ungrouped_results = [
                    f.result() for f in as_completed(futures) if f.result()
                ]

        return ungrouped_results

    def _cleanup_invalid_videos(self):
        """
        Remove invalid video files when not in debug mode.
        Invalid videos are those that either:
        1. Have score <= 1.0, OR
        2. Have score > 1.0 but offset outside -3 to 3 range
        """
        if self.debug:
            return  # Don't remove anything in debug mode

        removed_count = 0
        for video_file in self.invalid_videos:
            try:
                if video_file.exists():
                    video_file.unlink()
                    removed_count += 1
                    print(f"🗑️  Removed invalid video: {video_file.name}")
            except Exception as e:
                print(f"⚠️  Failed to remove {video_file.name}: {e}")

        if removed_count > 0:
            print(f"\n🧹 Cleaned up {removed_count} invalid videos")

    def _save_results(self, scored_groups, ungrouped_results):
        """
        Save detection results based on debug mode.
        - Debug mode: Save all NPZ score files and generate plots
        - Production mode: Only keep MP4 files that pass both score and offset validation

        Args:
            scored_groups (dict): Scored groups dictionary
            ungrouped_results (list): List of ungrouped results
        """
        if self.debug:
            # In debug mode, save all NPZ confidence files
            output_file = self.output_path / "scores.npz"
            np.savez(
                output_file,
                scored_groups=scored_groups,
                ungrouped=np.array(ungrouped_results, dtype=object),
                failed_tracks=np.array(self.failed_tracks, dtype=object),
                plot_results=np.array(self.plot_results, dtype=object),
                allow_pickle=True,
            )

            print(f"\n✅ [DEBUG MODE] Saved all confidence scores to {output_file}")
            print(
                f"   - {sum(len(group) for group in scored_groups.values())} tracks in groups"
            )
            print(f"   - {len(ungrouped_results)} ungrouped tracks")
            print(f"   - {len(self.failed_tracks)} failed tracks")
            print(f"   - {len(self.plot_results)} plots generated")

            # Print filtering summary
            print(f"\n📊 Filtering Summary:")
            print(f"   - {len(self.high_score_videos)} videos with score > 1.0")
            print(f"   - {len(self.low_score_videos)} videos with score <= 1.0")
            print(
                f"   - {len(self.valid_videos)} valid videos (score > 1.0 AND offset -3 to 3)"
            )
            print(
                f"   - {len(self.invalid_videos)} invalid videos (failed score or offset check)"
            )

            # Print plot summary
            if self.plot_results:
                true_speakers = sum(
                    1 for p in self.plot_results if p["speaker_label"] == "true_speaker"
                )
                false_speakers = sum(
                    1
                    for p in self.plot_results
                    if p["speaker_label"] == "false_speaker"
                )
                print(f"   - {true_speakers} true speakers (offset -3 to 3)")
                print(f"   - {false_speakers} false speakers (offset outside -3 to 3)")
        else:
            # In production mode, remove invalid videos and don't save NPZ files
            self._cleanup_invalid_videos()

            total_tracks = sum(len(group) for group in scored_groups.values()) + len(
                ungrouped_results
            )

            print(f"\n✅ [PRODUCTION MODE] Processing complete")
            print(f"   - {total_tracks} total tracks processed")
            print(
                f"   - {len(self.high_score_videos)} videos passed score check (> 1.0)"
            )
            print(
                f"   - {len(self.low_score_videos)} videos failed score check (<= 1.0)"
            )
            print(
                f"   - {len(self.valid_videos)} videos kept (score > 1.0 AND offset -3 to 3)"
            )
            print(f"   - {len(self.invalid_videos)} videos removed (failed validation)")
            print(f"   - {len(self.failed_tracks)} failed tracks")
            print(f"   - No NPZ score files or plots generated")

            if self.valid_videos:
                print(f"\n📋 Valid videos kept (score > 1.0 AND offset -3 to 3):")
                for video_path in sorted(self.valid_videos):
                    print(f"   - {video_path.name}")

    def detect(self):
        """
        Run the complete active speaker detection pipeline using only SyncNet.

        Returns:
            tuple: (scored_groups, ungrouped_results, failed_tracks)
        """
        # Reset state for new detection run
        self.failed_tracks = []
        self.high_score_videos = set()
        self.low_score_videos = set()
        self.valid_videos = set()
        self.invalid_videos = set()
        self.plot_results = []

        print(f"🔧 Running in {'DEBUG' if self.debug else 'PRODUCTION'} mode")
        print("🎯 Using SyncNet with two-stage filtering:")
        print("   Stage 1: Score threshold > 1.0")
        print("   Stage 2: Offset range -3 to 3")
        if self.debug:
            print("📊 SyncNet plots will be generated with offset-based labeling")

        # 1. Load track data
        tracks = self._load_track_data()

        # 2. Categorize tracks
        grouped_tracks, ungrouped_tracks, grouped = self._categorize_tracks(tracks)

        # 3. Process grouped tracks with SyncNet
        scored_groups = self._process_grouped_tracks(grouped)

        # 4. Process ungrouped tracks with SyncNet
        ungrouped_results = self._process_ungrouped_tracks(ungrouped_tracks)

        # 5. Save results based on debug mode (includes two-stage filtering)
        self._save_results(scored_groups, ungrouped_results)

        return scored_groups, ungrouped_results, self.failed_tracks


def is_active_speaker(
    video_dir: str, npz_dir: str, output_dir: str, workers: int, debug: bool = False
):
    """
    Legacy function wrapper for backward compatibility.

    Args:
        video_dir (str): Directory containing video files
        npz_dir (str): Directory containing NPZ metadata files
        output_dir (str): Output directory for results
        workers (int): Number of parallel workers
        debug (bool): Debug mode flag

    Returns:
        tuple: (scored_groups, ungrouped_results, failed_tracks)
    """
    detector = AutomaticActiveSpeakerDetector(
        video_dir, npz_dir, output_dir, workers, debug
    )
    return detector.detect()


def run_asd_pipeline(
    video_path: str,
    output_dir: str,
    min_duration: float = 3.0,
    workers: int = 4,
    debug: bool = False,
):
    """
    Run the complete face tracking, segmentation, and active speaker detection pipeline.

    Args:
        video_path (str): Path to input video file
        output_dir (str): Output directory for all results
        min_duration (float): Minimum segment duration in seconds
        workers (int): Number of parallel workers
        debug (bool): Debug mode flag
    """
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    video_name = os.path.splitext(os.path.basename(video_path))[0]
    tracks_dir = output_path / "tracks"
    tracks_dir.mkdir(exist_ok=True)

    print(f"🎬 Processing video: {video_path}")
    print(f"🔧 Debug mode: {'ON' if debug else 'OFF'}")

    # Step 1: Face tracking
    print("🔍 Running face tracking...")
    tracker = FaceTracker()
    tracker.process_video(video_path)
    tracks_data = tracker.get_tracks_data()

    # Save track data
    def save_track(track):
        track_id = track["track_id"]
        track_path = tracks_dir / f"{video_name}_track{track_id}.npz"
        np.savez(track_path, **track)
        return track_path

    with ThreadPoolExecutor(max_workers=workers) as executor:
        futures = [executor.submit(save_track, track) for track in tracks_data]
        for future in as_completed(futures):
            print(f"✅ Saved track: {future.result()}")

    # Step 2: Face segmentation
    print("\n🎥 Running face segmentation...")
    processor = FaceSegmentProcessor(
        video_path=video_path,
        output_dir=str(output_path),
        min_duration=min_duration,
        max_workers=3,
    )
    processor.run()
    print(f"✅ Segmented videos saved in: {output_path}")

    # Step 3: Active speaker detection using SyncNet with two-stage filtering
    print("\n🗣️  Running active speaker detection with SyncNet (two-stage filtering)...")
    detector = AutomaticActiveSpeakerDetector(
        video_dir=str(output_path),
        npz_dir=str(tracks_dir),
        output_dir=str(output_path),
        workers=workers,
        debug=debug,
    )

    scored_groups, ungrouped_results, failed_tracks = detector.detect()
    print(failed_tracks)

    if not debug:
        print("Cleaning up all the extra files .....")
        all_items = glob.glob(os.path.join(output_path, "*"))

        for item_path in all_items:
            if os.path.isfile(item_path):
                if not item_path.lower().endswith(".mp4"):
                    try:
                        os.remove(item_path)
                        print(f"Deleted file: {item_path}")
                    except Exception as e:
                        print(f"Failed to delete file {item_path}: {e}")
            elif os.path.isdir(item_path):
                try:
                    shutil.rmtree(item_path)
                    print(f"Deleted folder: {item_path}")
                except Exception as e:
                    print(f"Failed to delete folder {item_path}: {e}")

    print(f"\n✅ Pipeline complete! All results saved in: {output_path}")

    if failed_tracks:
        print(f"⚠️  {len(failed_tracks)} tracks failed processing")

    return scored_groups, ungrouped_results, failed_tracks


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Face Segmentation and Active Speaker Detection Pipeline (SyncNet Only) with Two-Stage Filtering"
    )
    parser.add_argument("--video", required=True, help="Input video file path")
    parser.add_argument(
        "--output-dir", required=True, help="Output directory for all results"
    )
    parser.add_argument(
        "--min-dur", type=float, default=3.0, help="Minimum segment duration in seconds"
    )
    parser.add_argument(
        "--workers", type=int, default=4, help="Number of parallel processing threads"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode to save all NPZ confidence files and generate SyncNet plots",
    )

    args = parser.parse_args()

    try:
        run_asd_pipeline(
            video_path=args.video,
            output_dir=args.output_dir,
            min_duration=args.min_dur,
            workers=args.workers,
            debug=args.debug,
        )
    except Exception as e:
        print(f"\n🚨 Pipeline failed: {e}")
        sys.exit(1)
