import cv2
import os
import kornia
import numpy as np
import torch
from einops import rearrange
from torchvision import transforms


class InferenceUtils:
    def __init__(self, resolution: int, mask_path: str):
        self.device: torch.device = torch.device(
            "cuda" if torch.cuda.is_available() else "cpu"
        )
        self.dtype = torch.float16
        self.mask_path = mask_path

        self.resolution = resolution

        # Initialize transforms
        self.resize = transforms.Resize(
            (resolution, resolution),
            interpolation=transforms.InterpolationMode.BILINEAR,
            antialias=True,
        )
        self.normalize = transforms.Normalize([0.5], [0.5], inplace=True)
        self.mask_image = self._load_fixed_mask(resolution, self.mask_path)

        ###########################################################################################
        ratio = resolution / 256 * 2.8
        self.crop_ratio = (ratio, ratio)
        self.face_size = (int(100 * self.crop_ratio[0]), int(100 * self.crop_ratio[1]))
        self.fill_value = torch.tensor(
            [127, 127, 127], device=self.device, dtype=self.dtype
        )
        self.mask = torch.ones(
            (1, 1, self.face_size[1], self.face_size[0]),
            device=self.device,
            dtype=self.dtype,
        )
        self.upscale_factor = 1

    def _load_fixed_mask(self, resolution: int, mask_path: str) -> torch.Tensor:
        """
        Load and preprocess fixed mask image.

        Args:
            resolution: Target resolution
            mask_path: Path to mask image

        Returns:
            torch.Tensor: Processed mask tensor
        """
        if not os.path.exists(mask_path):
            raise FileNotFoundError(f"Mask file not found: {mask_path}")

        try:
            mask_image = cv2.imread(mask_path)
            if mask_image is None:
                raise ValueError(f"Failed to load mask image: {mask_path}")

            mask_image = cv2.cvtColor(mask_image, cv2.COLOR_BGR2RGB)
            mask_image = (
                cv2.resize(
                    mask_image, (resolution, resolution), interpolation=cv2.INTER_AREA
                )
                / 255.0
            )
            return rearrange(torch.from_numpy(mask_image), "h w c -> c h w")
        except Exception as e:
            raise RuntimeError(f"Error loading mask: {str(e)}")

    def _preprocess_fixed_mask_image(
        self,
        image: torch.Tensor,
    ) -> tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Preprocess image with fixed mask.

        Args:
            image: Input image tensor
            affine_transform: Whether to apply affine transformation

        Returns:
            Tuple containing:
            - Original image tensor
            - Masked image tensor
            - Mask tensor
        """
        image = self.resize(image)

        pixel_values = self.normalize(image / 255.0)
        masked_pixel_values = pixel_values * self.mask_image
        return pixel_values, masked_pixel_values, self.mask_image[0:1]

    def prepare_partial_masks_and_masked_images(
        self,
        images: torch.Tensor | np.ndarray,
        K: int,
    ) -> tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Prepare masks and masked images with partial masking.

        Args:
            images: Batch of input images
            K: Number of unmasked images
            affine_transform: Whether to apply affine transformation

        Returns:
            Tuple containing:
            - Original image tensors
            - Masked image tensors
            - Mask tensors
        """
        # Convert input to tensor if needed
        if isinstance(images, np.ndarray):
            images = torch.from_numpy(images)

        if images.shape[3] == 3:
            images = rearrange(images, "b h w c -> b c h w")

        # Split images
        unmasked_images = images[:K]
        masked_images = images[K:]

        # Process unmasked images
        unmasked_results = []
        for image in unmasked_images:
            image = self.resize(image)
            pixel_values = self.normalize(image / 255.0)
            identity_mask = torch.ones((1, self.resolution, self.resolution))
            unmasked_results.append((pixel_values, pixel_values, identity_mask))

        # Process masked images
        masked_results = [
            self._preprocess_fixed_mask_image(image) for image in masked_images
        ]

        # Combine results
        results = unmasked_results + masked_results
        pixel_values_list, masked_pixel_values_list, masks_list = zip(*results)

        return (
            torch.stack(pixel_values_list),
            torch.stack(masked_pixel_values_list),
            torch.stack(masks_list),
        )

    #######################################################################################################################
    def _apply_blending(
        self,
        pasted_face_chw: torch.Tensor,
        original_input_img_chw: torch.Tensor,
        inv_mask_erosion_gpu: torch.Tensor,
        w_edge: int,
    ) -> torch.Tensor:
        # Define the erosion radius based on the edge width
        erosion_radius = w_edge * 2

        # Perform erosion on the CPU to avoid high GPU memory consumption
        # Convert the eroded mask to a NumPy array for OpenCV processing
        inv_mask_erosion_cpu = (
            inv_mask_erosion_gpu.squeeze().cpu().numpy().astype(np.float32)
        )
        # Erode the mask further using OpenCV to get the center of the mask
        inv_mask_center_cpu = cv2.erode(
            inv_mask_erosion_cpu, np.ones((erosion_radius, erosion_radius), np.uint8)
        )
        # Convert the center mask back to a PyTorch tensor and move it to the specified device
        inv_mask_center = torch.from_numpy(inv_mask_center_cpu).to(
            device=self.device, dtype=self.dtype
        )[None, None, ...]  # Add batch and channel dimensions

        # Determine the blur kernel size for Gaussian blur, ensuring it's an odd number
        blur_size = w_edge * 2 + 1
        # Calculate the sigma for Gaussian blur based on the blur size
        sigma = 0.3 * ((blur_size - 1) * 0.5 - 1) + 0.8
        # Apply Gaussian blur to the center mask to create a soft mask for smooth blending
        inv_soft_mask = kornia.filters.gaussian_blur2d(
            inv_mask_center, (blur_size, blur_size), (sigma, sigma)
        ).squeeze(0)  # Remove batch dimension
        # Expand the soft mask to match the number of channels of the pasted face
        inv_soft_mask_3d = inv_soft_mask.expand_as(pasted_face_chw)
        # Blend the pasted face with the original input image using the soft mask
        blended_img_chw = (
            inv_soft_mask_3d * pasted_face_chw
            + (1 - inv_soft_mask_3d) * original_input_img_chw
        )
        return blended_img_chw

    def restore_img(
        self,
        input_img_np: np.ndarray,
        face: torch.Tensor,
        affine_matrix: torch.Tensor
        | np.ndarray,  # Can accept np.ndarray or torch.Tensor
        apply_blending: bool = True,
    ) -> np.ndarray:
        # Get the height and width of the input image
        h, w, _ = input_img_np.shape

        # Convert affine_matrix to a PyTorch tensor if it's a NumPy array
        # This check remains to support callers passing np.ndarray for affine_matrix
        if isinstance(affine_matrix, np.ndarray):
            current_affine_matrix = (
                torch.from_numpy(affine_matrix)
                .to(device=self.device, dtype=self.dtype)
                .unsqueeze(0)  # Add a batch dimension
            )
        else:
            current_affine_matrix = affine_matrix.to(
                device=self.device, dtype=self.dtype
            )
            if current_affine_matrix.ndim == 2:  # Ensure batch dimension
                current_affine_matrix = current_affine_matrix.unsqueeze(0)

        # Invert the affine transformation matrix
        inv_affine_matrix = kornia.geometry.transform.invert_affine_transform(
            current_affine_matrix
        )
        # Convert face tensor to the correct data type and add a batch dimension
        face_tensor = face.to(dtype=self.dtype).unsqueeze(0)

        # Warp the face image back to the original image space using the inverted affine matrix
        inv_face_chw = kornia.geometry.transform.warp_affine(
            face_tensor,
            inv_affine_matrix,
            (h, w),  # Target output size (height, width of original image)
            mode="bilinear",  # Interpolation mode
            padding_mode="fill",  # How to handle areas outside the transformed image
            fill_value=self.fill_value,  # Value to use for filling
        ).squeeze(0)  # Remove the batch dimension
        # Normalize the inverted face image to the range [0, 255]
        inv_face_chw = (inv_face_chw / 2 + 0.5).clamp(0, 1) * 255

        # save inv_face_chw for debugging
        # cv2.imwrite("inv_face_chw.png", inv_face_chw.permute(1, 2, 0).cpu().numpy().astype(np.uint8))

        # Convert the input image from NumPy array to a PyTorch tensor
        # and rearrange dimensions from HWC (Height, Width, Channel) to CHW (Channel, Height, Width)
        input_img_chw = rearrange(
            torch.from_numpy(input_img_np).to(device=self.device, dtype=self.dtype),
            "h w c -> c h w",
        )
        # Warp the predefined mask using the inverted affine matrix to align it with the face in the original image
        inv_mask = kornia.geometry.transform.warp_affine(
            self.mask, inv_affine_matrix, (h, w), padding_mode="zeros"
        )  # (1, 1, h, w)

        # Erode the inverted mask to shrink its boundaries
        # The kernel size for erosion depends on the upscale_factor
        inv_mask_erosion_gpu = kornia.morphology.erosion(
            inv_mask,
            torch.ones(
                (int(2 * self.upscale_factor), int(2 * self.upscale_factor)),
                device=self.device,
                dtype=self.dtype,
            ),
        )

        # Remove the batch dimension from the eroded mask and expand it to match the number of channels of the inverted face
        inv_mask_erosion_t = inv_mask_erosion_gpu.squeeze(0).expand_as(inv_face_chw)
        # Apply the eroded mask to the inverted face (element-wise multiplication)
        pasted_face_chw = inv_mask_erosion_t * inv_face_chw

        # Calculate the total area of the eroded face mask
        total_face_area = torch.sum(inv_mask_erosion_gpu.float())
        # Determine a width for edge blending, proportional to the square root of the face area
        w_edge = int(total_face_area**0.5) // 20

        final_img_chw: torch.Tensor
        if apply_blending:
            # Call the new blending function
            final_img_chw = self._apply_blending(
                pasted_face_chw, input_img_chw, inv_mask_erosion_gpu, w_edge
            )
        else:
            # If not blending, the pasted face is the final CHW image
            final_img_chw = pasted_face_chw

        # Rearrange the final CHW image dimensions from CHW back to HWC
        # Convert data type to uint8 for image representation
        final_img_hwc = (
            rearrange(final_img_chw, "c h w -> h w c")
            .contiguous()
            .to(dtype=torch.uint8)
        )
        # Move the final image tensor to CPU and convert it to a NumPy array
        output_img_np = final_img_hwc.cpu().numpy()
        return output_img_np
