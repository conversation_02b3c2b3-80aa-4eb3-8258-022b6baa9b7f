padding:
  bbox: 0.1
  box_height_ratio: 1
  height_ratio: 0.8

image:
  size: 256

smoothing:
  smooth_type: laplacian # This might be deprecated or used as a fallback if savgol is not specified
  savgol_filter:
    window_length: 11  # Default, must be odd, positive integer
    polyorder: 3      # Default, must be positive integer and < window_length

detector:
  roi_detector: faceboxes
  det_config: src/models/threeddfa/threeddfa/configs/mb1_120x120.yml
  use_onnx: false

face_template:
  threeddfa:
    # Top-left BBOX vertex position [x, y]
    # - Affects overall face alignment and symmetry
    # - x: lower = narrower face, higher = wider face
    # - y: lower = nose higher in frame, higher = nose lower in frame
    - [9.8, 29.3]
    
    # Top-Right BBOX vertex position [x, y]
    # - Controls face width and horizontal alignment
    # - x: lower = narrower face, higher = wider face
    # - y: lower = nose higher in frame, higher = nose lower in frame
    - [90.2, 29.3]
    
    # BBox Center position [x, y]
    # - Determines vertical face proportions
    # - Higher y value = lower lip placement
    # - Lower y value = higher lip placement
    - [50.0, 61.32]