"""
Affine transformation utilities for face alignment.

This module provides utilities for aligning faces using affine transformations
based on facial landmarks. It includes functionality for warping faces to a
standard template and computing transformation matrices.
"""

import numpy as np
import torch
from einops import rearrange
import kornia


class AlignEyes(object):
    """
    Face alignment class using affine transformations.

    This class aligns faces to a standard template using facial landmarks
    and applies affine transformations for consistent face cropping.
    """

    def __init__(
        self, face_template=None, resolution=256, device="cpu", dtype=torch.float16
    ):
        """
        Initialize the AlignEyes face alignment processor.

        Args:
            face_template (np.ndarray, optional): Custom face template landmarks
            resolution (int): Target resolution for face alignment (default: 256)
            device (str): Device to run computations on ('cpu' or 'cuda')
            dtype (torch.dtype): Data type for tensor operations
        """
        self.upscale_factor = 1
        ratio = resolution / 256 * 2.8
        self.crop_ratio = (ratio, ratio)

        if face_template is not None:
            self.face_template = face_template
        else:
            # self.face_template = np.array([[19 - 10, 30 - 10], [56 + 10, 30 - 10], [37.5, 45 + 5]])
            self.face_template = np.array(
                [[19 - 19, 30 - 10], [56 + 19, 30 - 10], [37.5, 45 - 5]]
            )

        self.face_template = self.face_template * ratio
        self.face_size = (int(100 * self.crop_ratio[0]), int(100 * self.crop_ratio[1]))
        self.p_bias = None
        self.device = device
        self.dtype = dtype
        self.fill_value = torch.tensor([127, 127, 127], device=device, dtype=dtype)
        self.mask = torch.ones(
            (1, 1, self.face_size[1], self.face_size[0]), device=device, dtype=dtype
        )

    def align_warp_face(self, img, landmarks3, smooth=True):
        """
        Align and warp a face image using facial landmarks.

        Args:
            img (np.ndarray): Input face image
            landmarks3 (np.ndarray): Facial landmarks for alignment
            smooth (bool): Whether to apply smoothing to the transformation

        Returns:
            tuple: (cropped_face, affine_matrix) where cropped_face is the aligned
                   face image and affine_matrix is the transformation matrix used
        """
        affine_matrix, self.p_bias = self.transformation_from_points(
            landmarks3, self.face_template, smooth, self.p_bias
        )

        img = rearrange(
            torch.from_numpy(img).to(device=self.device, dtype=self.dtype),
            "h w c -> c h w",
        ).unsqueeze(0)
        affine_matrix = (
            torch.from_numpy(affine_matrix)
            .to(device=self.device, dtype=self.dtype)
            .unsqueeze(0)
        )

        cropped_face = kornia.geometry.transform.warp_affine(
            img,
            affine_matrix,
            (self.face_size[1], self.face_size[0]),
            mode="bilinear",
            padding_mode="fill",
            fill_value=self.fill_value,
        )
        cropped_face = (
            rearrange(cropped_face.squeeze(0), "c h w -> h w c")
            .cpu()
            .numpy()
            .astype(np.uint8)
        )
        return cropped_face, affine_matrix

    def transformation_from_points(
        self, points1: torch.Tensor, points0: torch.Tensor, smooth=True, p_bias=None
    ):
        """
        Compute affine transformation matrix from two sets of corresponding points.

        Args:
            points1 (torch.Tensor): Source points (facial landmarks)
            points0 (torch.Tensor): Target points (face template)
            smooth (bool): Whether to apply temporal smoothing
            p_bias: Previous bias for smoothing (internal state)

        Returns:
            tuple: (transformation_matrix, updated_bias) where transformation_matrix
                   is the 2x3 affine transformation matrix and updated_bias is the
                   smoothing bias for temporal consistency

        Note:
            Uses Procrustes analysis to find the optimal similarity transformation
            (rotation, scaling, translation) between the two point sets.
        """
        if isinstance(points0, np.ndarray):
            points2 = torch.tensor(points0, device=self.device, dtype=torch.float32)
        else:
            points2 = points0.clone()

        if isinstance(points1, np.ndarray):
            points1_tensor = torch.tensor(
                points1, device=self.device, dtype=torch.float32
            )
        else:
            points1_tensor = points1.clone()

        c1 = torch.mean(points1_tensor, dim=0)
        c2 = torch.mean(points2, dim=0)

        points1_centered = points1_tensor - c1
        points2_centered = points2 - c2

        s1 = torch.std(points1_centered)
        s2 = torch.std(points2_centered)

        points1_normalized = points1_centered / s1
        points2_normalized = points2_centered / s2

        covariance = torch.matmul(points1_normalized.T, points2_normalized)
        U, S, V = torch.svd(covariance)

        R = torch.matmul(V, U.T)

        det = torch.det(R)
        if det < 0:
            V[:, -1] = -V[:, -1]
            R = torch.matmul(V, U.T)

        sR = (s2 / s1) * R
        T = c2.reshape(2, 1) - (s2 / s1) * torch.matmul(R, c1.reshape(2, 1))

        M = torch.cat((sR, T), dim=1)

        if smooth:
            bias = points2_normalized[2] - points1_normalized[2]
            if p_bias is None:
                p_bias = bias
            else:
                bias = p_bias * 0.2 + bias * 0.8
            p_bias = bias
            M[:, 2] = M[:, 2] + bias

        return M.cpu().numpy(), p_bias
