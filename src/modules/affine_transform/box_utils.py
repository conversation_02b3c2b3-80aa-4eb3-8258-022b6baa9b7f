import os
import time
from dataclasses import dataclass

import cv2
import numpy as np
from omegaconf import DictConfig
from typing import Any

NOSE_TIP_INDEX = 8310  # Assuming this is the correct index for the nose tip


@dataclass
class Point2D:
    """2D point representation."""

    x: float
    y: float

    def to_array(self) -> np.ndarray:
        """Convert point to numpy array."""
        return np.array([self.x, self.y])


@dataclass
class PaddingConfig:
    """Configuration for padding settings."""

    bbox: float = 0.0
    box_height_ratio: float = 1.0


class BoxCalc:
    """Helper class for affine transformation calculations."""

    DEBUG_DIR: str = "debug_plots/debug_frames"
    DEBUG_BOX_COLOR: cv2.typing.Scalar = (0, 255, 0)  # Green
    DEBUG_CENTER_COLOR: cv2.typing.Scalar = (0, 0, 255)  # Red
    DEBUG_THICKNESS: int = 2
    DEBUG_CIRCLE_RADIUS: int = 5

    def __init__(
        self,
        config: DictConfig | None = None,
        debug_mode: bool = False,
    ):
        """
        Initialize BoxCalc.

        Args:
            config: Configuration dictionary. Expected to have a "padding" key
                    which is a dictionary with "bbox" and "box_height_ratio".
            debug_mode: Boolean flag to enable/disable debug mode.
        """
        self.config = config if config is not None else {}
        padding_data = self.config.get("padding", {})
        self.padding_config = PaddingConfig(
            bbox=padding_data.get("bbox", 0.0),
            box_height_ratio=padding_data.get("box_height_ratio", 1.0),
        )
        self.debug_mode = debug_mode
        print(
            f"DEBUG [BoxCalc.__init__]: Received debug_mode = {debug_mode}, self.debug_mode = {self.debug_mode}"
        )
        self._call_id_counter = 0  # For unique debug filenames per call

        if self.debug_mode:
            print(
                f"DEBUG [BoxCalc.__init__]: Checking/Creating DEBUG_DIR: {self.DEBUG_DIR}"
            )
            if not os.path.exists(self.DEBUG_DIR):
                os.makedirs(self.DEBUG_DIR)
                print(f"DEBUG [BoxCalc]: Created debug directory: {self.DEBUG_DIR}")

    def _log_and_visualize_debug_step(
        self,
        step_name: str,
        image_to_draw_on: np.ndarray,
        box_points_to_draw: np.ndarray | None = None,
        center_point_to_draw: np.ndarray | None = None,
        duration_ms: float | None = None,
        additional_info: dict[str, Any] | None = None,
        call_id: int = 0,
        step_sequence: int = 0,
    ):
        """Logs textual debug info and saves a visualization if debug_mode is on."""
        if not self.debug_mode:
            return

        log_message = (
            f"DEBUG [BoxCalc Call {call_id} - Step {step_sequence:02d}]: {step_name}"
        )
        if duration_ms is not None:
            log_message += f" | Duration: {duration_ms:.2f} ms"

        if additional_info:
            for key, value in additional_info.items():
                if isinstance(value, np.ndarray):
                    log_message += f" | {key}: {value.tolist()}"
                else:
                    log_message += f" | {key}: {value}"
        print(log_message)

        if image_to_draw_on is not None:
            debug_image = image_to_draw_on.copy()
            if box_points_to_draw is not None:
                cv2.polylines(
                    debug_image,
                    [box_points_to_draw.astype(np.int32)],
                    isClosed=True,
                    color=self.DEBUG_BOX_COLOR,
                    thickness=self.DEBUG_THICKNESS,
                )
            if center_point_to_draw is not None:
                cv2.circle(
                    debug_image,
                    tuple(map(int, center_point_to_draw)),
                    self.DEBUG_CIRCLE_RADIUS,
                    self.DEBUG_CENTER_COLOR,
                    -1,
                )

            filename: str = f"call_{call_id}_step_{step_sequence:02d}_{step_name.lower().replace(' ', '_')}.png"
            try:
                filepath = os.path.join(self.DEBUG_DIR, filename)
                cv2.imwrite(filepath, debug_image)
                print(
                    f"DEBUG [BoxCalc Call {call_id}]: Saved visualization to {filepath}"
                )
            except Exception as e:
                print(
                    f"ERROR [BoxCalc Call {call_id}]: Failed to save debug image {filename}. Error: {e}"
                )

    def _calculate_initial_box(
        self, jaw_points: np.ndarray, nose_point: np.ndarray
    ) -> np.ndarray:
        """
        Calculates the initial bounding box based on jaw points and nose point.
        (Docstring remains the same)
        """
        min_x = np.min(jaw_points[0, :])
        max_x = np.max(jaw_points[0, :])

        width = max_x - min_x
        pad_x = width * self.padding_config.bbox
        min_x -= pad_x
        max_x += pad_x

        top_left = np.array([min_x, nose_point[1]])
        top_right = np.array([max_x, nose_point[1]])

        max_y_jaw = np.max(jaw_points[1, :])
        height_from_nose = max_y_jaw - nose_point[1]
        desired_height = height_from_nose * self.padding_config.box_height_ratio

        bottom_left = np.array([min_x, nose_point[1] + desired_height])
        bottom_right = np.array([max_x, nose_point[1] + desired_height])

        return np.vstack([top_left, top_right, bottom_right, bottom_left])

    def _rotate_box(
        self, box_points: np.ndarray, roll_angle: float, center_of_rotation: np.ndarray
    ) -> np.ndarray:
        """
        Rotates the bounding box points around a center point.
        (Docstring remains the same)
        """
        cos_a = np.cos(roll_angle)
        sin_a = np.sin(roll_angle)
        R_matrix = np.array([[cos_a, -sin_a], [sin_a, cos_a]])
        translated_points = box_points - center_of_rotation
        # rotated_points = translated_points @ R_matrix.T
        rotated_points = translated_points @ R_matrix
        return rotated_points + center_of_rotation

    def _clip_box_to_image(
        self, box_points: np.ndarray, image_shape: tuple[int, int]
    ) -> np.ndarray:
        """
        Clips the bounding box coordinates to be within image boundaries.
        (Docstring remains the same)
        """
        img_height, img_width = image_shape
        return np.clip(box_points, [0, 0], [img_width - 1, img_height - 1])

    def __call__(
        self,
        jaw_points: np.ndarray,
        roll_angle: float,
        image: np.ndarray,
    ) -> np.ndarray:
        """
        Calculate base bounding box for face alignment.
        (Detailed docstring remains largely the same, can be updated to mention debug mode)
        """
        # print(f"DEBUG [BoxCalc.__call__]: Entered __call__ with current_call_id = {self._call_id_counter}, debug_mode = {self.debug_mode}")
        current_call_id = self._call_id_counter
        self._call_id_counter += 1
        step_seq = 0

        overall_start_time = time.perf_counter()

        # --- Step 0: Pre-computation (Nose Point Extraction) ---
        step_start_time = time.perf_counter()
        if jaw_points.shape[1] <= NOSE_TIP_INDEX:
            raise ValueError(
                f"jaw_points has {jaw_points.shape[0]} points, "
                f"but NOSE_TIP_INDEX is {NOSE_TIP_INDEX}. "
                "Ensure jaw_points contains enough landmarks."
            )
        nose_point = jaw_points[:2, NOSE_TIP_INDEX]
        step_duration_ms = (time.perf_counter() - step_start_time) * 1000
        step_seq += 1
        self._log_and_visualize_debug_step(
            step_name="Nose Point Extraction",
            image_to_draw_on=image,  # Show original image
            center_point_to_draw=nose_point,
            duration_ms=step_duration_ms,
            additional_info={"Nose Point": nose_point},
            call_id=current_call_id,
            step_sequence=step_seq,
        )

        # --- Step 1: Calculate Initial Box ---
        step_start_time = time.perf_counter()
        box = self._calculate_initial_box(jaw_points, nose_point)
        step_duration_ms = (time.perf_counter() - step_start_time) * 1000
        step_seq += 1
        self._log_and_visualize_debug_step(
            step_name="Initial Box Calculation",
            image_to_draw_on=image,
            box_points_to_draw=box,
            duration_ms=step_duration_ms,
            additional_info={"Initial Box": box},
            call_id=current_call_id,
            step_sequence=step_seq,
        )
        current_box_state = box  # Keep track of the box state for visualization

        # --- Step 2: Rotate Box (if needed) ---
        rotated_box_duration_ms = 0.0
        if not np.isclose(roll_angle, 0.0):
            step_start_time = time.perf_counter()
            box = self._rotate_box(box, roll_angle, nose_point)
            rotated_box_duration_ms = (time.perf_counter() - step_start_time) * 1000
            step_seq += 1
            self._log_and_visualize_debug_step(
                step_name="Box Rotation",
                image_to_draw_on=image,
                box_points_to_draw=box,
                center_point_to_draw=nose_point,  # Show center of rotation
                duration_ms=rotated_box_duration_ms,
                additional_info={"Rotated Box": box, "Roll Angle (rad)": roll_angle},
                call_id=current_call_id,
                step_sequence=step_seq,
            )
            current_box_state = box
        else:
            step_seq += (
                1  # Increment sequence even if step is skipped for consistent numbering
            )
            self._log_and_visualize_debug_step(
                step_name="Box Rotation (Skipped)",
                image_to_draw_on=image,  # Show previous state
                box_points_to_draw=current_box_state,
                duration_ms=0.0,
                additional_info={"Roll Angle (rad)": roll_angle},
                call_id=current_call_id,
                step_sequence=step_seq,
            )

        # --- Step 3: Clip Box to Image ---
        step_start_time = time.perf_counter()
        box = self._clip_box_to_image(box, image.shape[:2])
        step_duration_ms = (time.perf_counter() - step_start_time) * 1000
        step_seq += 1
        self._log_and_visualize_debug_step(
            step_name="Box Clipping",
            image_to_draw_on=image,
            box_points_to_draw=box,
            duration_ms=step_duration_ms,
            additional_info={"Clipped Box": box},
            call_id=current_call_id,
            step_sequence=step_seq,
        )

        # --- Step 4: Calculate Final Center & Assemble Output ---
        step_start_time = time.perf_counter()
        center = np.mean(box, axis=0)
        rect_points_input = np.vstack([box, center])
        step_duration_ms = (time.perf_counter() - step_start_time) * 1000
        step_seq += 1
        self._log_and_visualize_debug_step(
            step_name="Final Assembly (Center Calc & Output Stack)",
            image_to_draw_on=image,
            box_points_to_draw=box,
            center_point_to_draw=center,
            duration_ms=step_duration_ms,
            additional_info={"Final Box": box, "Center": center},
            call_id=current_call_id,
            step_sequence=step_seq,
        )

        overall_duration_ms = (time.perf_counter() - overall_start_time) * 1000
        if self.debug_mode:
            print(
                f"DEBUG [BoxCalc Call {current_call_id}]: Overall __call__ Duration: {overall_duration_ms:.2f} ms"
            )

        return rect_points_input
