import logging
import os

import cv2
import numpy as np
import torch
import yaml
from einops import rearrange
from insightface.app import FaceAnalysis
from omegaconf import DictConfig, ListConfig
from threeddfa.FaceBoxes import FaceBoxes
from threeddfa.FaceBoxes.FaceBoxes_ONNX import FaceBoxes_ONNX
from threeddfa.TDDFA import TDDFA
from threeddfa.TDDFA_ONNX import TDDFA_ONNX
from threeddfa.utils.pose import (
    calc_pose,
    calculate_2d_inplane_rotation_from_3d_orientation,
)
from tqdm import tqdm

from src.modules.utils.filters import apply_savgol_filter

from .affine_utils import Align<PERSON>yes
from .box_utils import BoxCalc

logger = logging.getLogger(__name__)  # Added logger


class AffineProcessor:
    def __init__(
        self,
        det_config: dict,
        use_onnx: bool,
        device: str,
        config: DictConfig | ListConfig | None = None,
        debug_mode: bool = False,
        annotation: bool = False,
    ):
        self.config: DictConfig = config
        self.det_config: dict = det_config
        self.use_onnx: bool = use_onnx
        self.device: str = device
        self.debug_mode: bool = debug_mode
        logger.debug(f"AffineProcessor initialized with debug_mode = {self.debug_mode}")

        self.resolution = self.config.image.size

        self.face_detector, self.face_landmarker = self._setup_face_detector()
        self._set_face_template()
        self._setup_savgol_filter()

        logger.debug(f"Initializing BoxCalc with debug_mode = {self.debug_mode}")
        self.box_calc = BoxCalc(self.config, self.debug_mode)
        self.affine_transformer = AlignEyes(
            face_template=self.face_template,
            resolution=self.resolution,
            device=self.device,
        )

        self.curr_frame_idx: int = 0
        self.yaw_list: list[float] | None = None
        self.effective_video_length: int | None = None

        # Cache for angle-based reference selection
        self._cached_sorted_indices: np.ndarray | None = None
        self._cache_invalid: bool = True

        # Roll angle storage
        self.store_roll_angle: bool = False
        self.roll_angles: list[float] | None = None

        # Annotation mode
        self.annotation = annotation
        if self.annotation:
            logger.info("AffineProcessor initialized in annotation mode")
            from threeddfa.utils.render_ctypes import render
            self.render = render

    def reset_state(self, preserve_keys=None):
        """
        Clears all instance variables except those specified in preserve_keys.

        Args:
            preserve_keys (list[str], optional): List of instance variable names to preserve.
                                               Defaults to configuration-related attributes.
        """
        if preserve_keys is None:
            preserve_keys = [
                "config",
                "det_config",
                "use_onnx",
                "device",
                "debug_mode",
                "resolution",
                "face_detector",
                "face_landmarker",
                "face_template",
                "savgol_window",
                "savgol_polyorder",
                "box_calc",
                "affine_transformer",
            ]

        # Store preserved variables
        preserved = {
            key: getattr(self, key) for key in preserve_keys if hasattr(self, key)
        }

        # Clear instance dict
        self.__dict__.clear()

        # Restore preserved variables
        self.__dict__.update(preserved)

        # Reset frame counter and cache
        self.curr_frame_idx = 0
        self.yaw_list = None
        self.effective_video_length = None

        self._cached_sorted_indices = None
        self._cache_invalid = True

        self.store_roll_angle = False
        self.roll_angles: list[float] | None = None

    def _setup_savgol_filter(self):
        # Load Savitzky-Golay filter parameters
        try:
            savgol_config = self.config.smoothing.savgol_filter
            self.savgol_window = int(savgol_config.window_length)
            self.savgol_polyorder = int(savgol_config.polyorder)
            logger.info(
                "Savitzky-Golay filter configured with window: %s, polyorder: %s",
                self.savgol_window,
                self.savgol_polyorder,
            )
        except AttributeError:
            logger.warning(
                "Savitzky-Golay filter configuration not found or incomplete in config.smoothing.savgol_filter. "
                "Smoothing will use raw points if savgol is attempted."
            )
            self.savgol_window = -1  # Indicate misconfiguration
            self.savgol_polyorder = -1
        except ValueError:
            logger.error(
                "Invalid non-integer value for Savitzky-Golay window_length or polyorder in config."
            )
            self.savgol_window = -1  # Indicate misconfiguration
            self.savgol_polyorder = -1

    def _setup_face_detector(
        self,
    ) -> tuple[FaceBoxes_ONNX | FaceAnalysis | FaceBoxes, TDDFA_ONNX | TDDFA]:
        """
        Setup the face detector based on the configuration.
        """
        cfg = yaml.load(open(self.det_config), Loader=yaml.SafeLoader)

        print(f"BBOX Detector: {self.config.detector.roi_detector}")



        # Init FaceBoxes and TDDFA, recommend using onnx flag
        if self.use_onnx:
            os.environ["KMP_DUPLICATE_LIB_OK"] = "True"
            os.environ["OMP_NUM_THREADS"] = "4"

            from threeddfa.FaceBoxes.FaceBoxes_ONNX import FaceBoxes_ONNX
            from threeddfa.TDDFA_ONNX import TDDFA_ONNX

            if self.config.detector.roi_detector == "faceboxes":
                face_bbox_detector = FaceBoxes_ONNX()
            elif self.config.detector.roi_detector == "insightface":
                face_bbox_detector = FaceAnalysis(
                    name="buffalo_l", allowed_modules="detection"
                )
                face_bbox_detector.prepare(ctx_id=0, det_size=(640, 640))
            else:
                logger.exception(
                    f"Invalid face detector in config: {self.config.detector.roi_detector}"
                )

            tddfa = TDDFA_ONNX(**cfg)
        else:
            gpu_mode = True if self.device == "cuda" else False
            tddfa = TDDFA(gpu_mode=gpu_mode, **cfg)

            if self.config.detector.roi_detector == "faceboxes":
                face_bbox_detector = FaceBoxes()
            elif self.config.detector.roi_detector == "insightface":
                face_bbox_detector = FaceAnalysis(
                    name="buffalo_l", allowed_modules="detection"
                )
                face_bbox_detector.prepare(ctx_id=0, det_size=(640, 640))
            else:
                logger.exception(
                    f"Invalid face detector in config: {self.config.detector.roi_detector}"
                )

        return face_bbox_detector, tddfa

    def _set_face_template(self):
        if self.config and hasattr(self.config, "face_template"):
            # Load templates from config
            threeddfa = self.config.face_template.threeddfa

            self.face_template = np.array(
                [
                    threeddfa[0],  # Top left vertex
                    threeddfa[1],  # Top right vertex
                    threeddfa[2],  # Enclosing box center
                ]
            )
        else:
            # Fallback to default templates
            self.face_template = np.array(
                [[19 - 10, 30 - 10], [56 + 10, 30 - 10], [37.5, 45 + 5]]
            )

    def _get_roll(self, param):
        # calc_pose now returns P, pose, R
        # P is the camera matrix, pose is [yaw, pitch, roll], R is the 3x3 rotation matrix
        _P, pose, R = calc_pose(
            param[0]
        )  # We get P and R, but only R is used here for now

        # Calculate the 2D in-plane rotation angle using the new method
        # This angle is designed to make the head's "up" direction vertical in the 2D image
        inplane_rotation_angle_rad = calculate_2d_inplane_rotation_from_3d_orientation(
            R
        )  # This is in RADIANS

        # For logging purposes, convert the new angle to degrees
        inplane_rotation_angle_deg = np.degrees(inplane_rotation_angle_rad)

        # Log the original 3D pose angles (which are in degrees from calc_pose)
        # and the new 2D in-plane rotation angle in degrees for comparison/debugging
        logger.debug(
            f"Original 3D Pose - Yaw: {pose[0]:.1f}, Pitch: {pose[1]:.1f}, Roll: {pose[2]:.1f} (degrees). "
            f"Calculated 2D In-plane Rotation: {inplane_rotation_angle_deg:.1f} (degrees)."
        )
        # print(f'yaw: {pose[0]:.1f}, pitch: {pose[1]:.1f}, roll: {pose[2]:.1f}, new_angle_deg: {inplane_rotation_angle_deg:.1f}')
        if self.yaw_list:
            self.yaw_list.append(pose[0])
        else:
            self.yaw_list = [pose[0]]

        # Invalidate cache when yaw_list changes
        self._cache_invalid = True

        # Return the angle in RADIANS as BoxCalc expects
        return inplane_rotation_angle_rad

    def get_reference_idxs(
        self, number_of_ref: int, mode: str = "equidistant"
    ) -> list[int]:
        """
        Get the frame indices for reference images based on the yaw list.

        Args:
            number_of_ref (int): Number of reference images to select.
            mode (str): Mode for selecting reference images. Default is "equidistant".
                        Options are "equidistant" | "angle_based"
        Returns:
            list[int]: List of frame indices for reference images.
        """
        # Input validation - early return for invalid inputs
        if number_of_ref <= 0:
            logger.warning(
                "Number of references must be positive. Returning empty list."
            )
            return []

        # Handle missing or insufficient data with proper null checks
        effective_length = self.effective_video_length or 0
        if not self.yaw_list or mode == "equidistant" or effective_length == 0:
            if mode != "equidistant" and not self.yaw_list:
                logger.warning(
                    "Yaw list is empty. Returning default equidistant reference indices."
                )
            elif mode != "equidistant" and effective_length == 0:
                logger.warning("Effective video length is 0. Returning empty list.")
                return []
            return self._get_equidistant_indices(number_of_ref, effective_length)

        if len(self.yaw_list) < number_of_ref:
            logger.warning(
                f"Yaw list has only {len(self.yaw_list)} entries, which is less than the requested {number_of_ref} references."
            )
            return self._get_equidistant_indices(number_of_ref, len(self.yaw_list))

        # Angle-based selection with caching
        return self._get_angle_based_indices(number_of_ref)

    def _get_equidistant_indices(self, number_of_ref: int, length: int) -> list[int]:
        """
        Get equidistant indices for reference selection.

        Args:
            number_of_ref (int): Number of reference images to select.
            length (int): Total length to distribute indices across.

        Returns:
            list[int]: List of equidistant indices.
        """
        if length <= 0:
            return []
        if number_of_ref >= length:
            return list(range(length))

        return np.linspace(0, length, number_of_ref, dtype=int, endpoint=False).tolist()

    def _get_angle_based_indices(self, number_of_ref: int) -> list[int]:
        """
        Get angle-based indices for reference selection with caching.

        Args:
            number_of_ref (int): Number of reference images to select.

        Returns:
            list[int]: List of angle-based indices.
        """
        # Type guard - this method should only be called when yaw_list is not None
        if self.yaw_list is None:
            logger.error("_get_angle_based_indices called with None yaw_list")
            return []

        # Use cached sorted indices if available and valid
        if self._cached_sorted_indices is None or self._cache_invalid:
            self._cached_sorted_indices = np.argsort(self.yaw_list)
            self._cache_invalid = False
            if logger.isEnabledFor(logging.DEBUG):
                logger.debug(
                    f"Cached sorted yaw indices: {self._cached_sorted_indices}"
                )

        # Select evenly spaced indices from sorted array
        indices = np.linspace(
            0,
            len(self._cached_sorted_indices),
            number_of_ref,
            dtype=int,
            endpoint=False,
        )

        # Map back to original indices
        reference_idxs = self._cached_sorted_indices[indices]

        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(f"Selected reference indices: {reference_idxs}")

        return reference_idxs.tolist()

    # TODO: Add confidence based face selection:
    # Challenges:
    # - Handle identity change, What if the most confident face in next frame is of different identity?
    #   [Negated by Single face filter now]
    def _combined_score(self, box, frame_shape) -> float:
        """
        Calculate combined score based on detection confidence and bbox size

        Args:
            box: Detection box with bbox and det_score attributes
            frame_shape: Shape of the frame (height, width, channels) for normalization

        Returns:
            float: Combined score (higher is better)
        """
        # Calculate bbox area
        bbox_area = (box.bbox[2] - box.bbox[0]) * (box.bbox[3] - box.bbox[1])
        # Normalize area by frame size
        normalized_area = (bbox_area / (frame_shape[0] * frame_shape[1])) * 10
        # Weighted combination (adjust weights as needed)
        print(f"Normalized Area: {normalized_area} | det_score: {box.det_score}")
        return 0.5 * box.det_score + 0.5 * normalized_area

    def _detect_facial_landmarks(self, frame_bgr: np.ndarray) -> tuple[list, float]:
        if self.curr_frame_idx == 0:
            if self.config.detector.roi_detector == "faceboxes":
                boxes = self.face_detector(frame_bgr)
                boxes = [boxes[0]]
                print(f"[PROFILING] FACE_BOXES: {boxes}")
            elif self.config.detector.roi_detector == "insightface":
                boxes = self.face_detector.get(frame_bgr)
                # Find highest confidence detection directly
                best_box = max(
                    boxes, key=lambda box: self._combined_score(box, frame_bgr.shape)
                )
                boxes = [np.append(best_box.bbox, best_box.det_score)]
                print(f"[PROFILING] IF_BOXES: {boxes}")

            param_lst, roi_box_lst = self.face_landmarker(frame_bgr, boxes)
            landmarks = self.face_landmarker.recon_vers(
                param_lst, roi_box_lst, dense_flag=True
            )[0]

            # refine
            param_lst, roi_box_lst = self.face_landmarker(
                frame_bgr, [landmarks], crop_policy="landmark"
            )
            landmarks = self.face_landmarker.recon_vers(
                param_lst, roi_box_lst, dense_flag=True
            )[0]
        elif self.curr_frame_idx >= 1 and self.prev_landmarks is not None:
            param_lst, roi_box_lst = self.face_landmarker(
                frame_bgr, [self.prev_landmarks], crop_policy="landmark"
            )
            roi_box = roi_box_lst[0]
            if abs(roi_box[2] - roi_box[0]) * abs(roi_box[3] - roi_box[1]) < 2020:
                if self.config.detector.roi_detector == "faceboxes":
                    boxes = self.face_detector(frame_bgr)
                    boxes = [boxes[0]]
                    print(f"[PROFILING] FACE_BOXES: {boxes}")
                elif self.config.detector.roi_detector == "insightface":
                    boxes = self.face_detector.get(frame_bgr)
                    # Find highest confidence detection directly
                    best_box = max(
                        boxes,
                        key=lambda box: self._combined_score(box, frame_bgr.shape),
                    )
                    boxes = [np.append(best_box.bbox, best_box.det_score)]
                    print(f"[PROFILING] IF_BOXES: {boxes}")

                param_lst, roi_box_lst = self.face_landmarker(frame_bgr, boxes)

            landmarks = self.face_landmarker.recon_vers(
                param_lst, roi_box_lst, dense_flag=True
            )[0]
        else:
            if self.config.detector.roi_detector == "faceboxes":
                boxes = self.face_detector(frame_bgr)
                boxes = [boxes[0]]
                print(f"[PROFILING] FACE_BOXES: {boxes}")
            elif self.config.detector.roi_detector == "insightface":
                boxes = self.face_detector.get(frame_bgr)
                # Find highest confidence detection directly
                best_box = max(
                    boxes, key=lambda box: self._combined_score(box, frame_bgr.shape)
                )
                boxes = [np.append(best_box.bbox, best_box.det_score)]
                print(f"[PROFILING] IF_BOXES: {boxes}")

            param_lst, roi_box_lst = self.face_landmarker(frame_bgr, boxes)
            landmarks = self.face_landmarker.recon_vers(
                param_lst, roi_box_lst, dense_flag=True
            )[0]

            # refine
            param_lst, roi_box_lst = self.face_landmarker(
                frame_bgr, [landmarks], crop_policy="landmark"
            )
            landmarks = self.face_landmarker.recon_vers(
                param_lst, roi_box_lst, dense_flag=True
            )[0]

        roll_angle = self._get_roll(param_lst)
        
        if self.store_roll_angle:
            if self.roll_angles is None:
                self.roll_angles = [roll_angle]
            else:
                self.roll_angles.append(roll_angle)

        return landmarks, roll_angle

    def _detect_facial_landmarks_video(
        self, capture: cv2.VideoCapture
    ) -> tuple[list[np.ndarray], list[np.ndarray]]:
        """
        Detect facial landmarks in a video.

        Args:
            video_path: Path to the input video file

        Returns:
            List of detected landmarks for each frame
        """
        frames = []
        rect_points = []

        self.effective_video_length = int(capture.get(cv2.CAP_PROP_FRAME_COUNT))

        while capture.isOpened():
            ret, frame = capture.read()
            if not ret:
                break

            landmarks, roll_angle = self._detect_facial_landmarks(frame)

            # Calculate reference points
            rect_point = self.box_calc(
                landmarks,
                roll_angle,
                frame,
            )

            if self.annotation and self.render is not None:
                frame = self.render(frame, landmarks, self.face_landmarker.tri)

            frames.append(frame)
            rect_points.append(rect_point)

            self.prev_landmarks = landmarks
            self.curr_frame_idx += 1

        capture.release()

        return frames, rect_points

    def _detect_facial_landmarks_frames(
        self, video_frames: np.ndarray
    ) -> tuple[list[np.ndarray], list[np.ndarray]]:
        """
        Detect facial landmarks in a video.

        Args:
            video_frames: Numpy array of BGR video frames

        Returns:
            List of detected landmarks for each frame
        """
        frames = []
        rect_points = []

        self.effective_video_length = len(video_frames)

        for frame in video_frames:
            frame_bgr = frame[:, :, ::-1]

            landmarks, roll_angle = self._detect_facial_landmarks(frame_bgr)

            # Calculate reference points
            rect_point = self.box_calc(
                landmarks,
                roll_angle,
                frame_bgr,
            )

            if self.annotation and self.render is not None:
                frame_bgr = self.render(frame_bgr, landmarks, self.face_landmarker.tri)

            frames.append(frame_bgr)
            rect_points.append(rect_point)

            self.prev_landmarks = landmarks
            self.curr_frame_idx += 1

        return frames, rect_points

    def _affine_transform(
        self,
        frame: np.ndarray,
        ref_points: np.ndarray,
    ) -> tuple[torch.Tensor, list[int], np.ndarray]:
        """
        Apply affine transformation to align face.

        Args:
            image: Input image tensor

        Returns:
            Tuple containing:
            - Transformed image tensor
            - Bounding box coordinates
            - Affine transformation matrix

        Raises:
            FaceDetectionError: If face detection fails or if detector is NO_DETECT
        """

        try:
            # Backup template
            template_backup = self.affine_transformer.face_template

            # self.affine_transformer.face_template = self.face_template

            idx = [0, 1, -1]  # top-left, top-right, center
            face, affine_matrix = self.affine_transformer.align_warp_face(
                frame.copy(),
                landmarks3=ref_points[idx],
            )

            # Calculate bounding box
            box = [0, 0, face.shape[1], face.shape[0]]

            # Resize with appropriate interpolation
            if face.shape[1] < self.resolution:
                face = cv2.resize(
                    face,
                    (self.resolution, self.resolution),
                    interpolation=cv2.INTER_LANCZOS4,
                )
            else:
                face = cv2.resize(
                    face,
                    (self.resolution, self.resolution),
                    interpolation=cv2.INTER_AREA,
                )

            # Restore template
            self.affine_transformer.face_template = template_backup
            return face, box, affine_matrix

        except Exception as e:
            raise RuntimeError(f"Affine transformation failed: {str(e)}")

    def _apply_smoothing_and_transforms(
        self,
        frames_iterable: list[np.ndarray],
        rect_points_iterable: list[np.ndarray],
        return_boxes_config: bool,
    ) -> tuple[list[np.ndarray], list[np.ndarray], list[list[int]]]:
        raw_transformed_frames = []
        affine_matrices_collected = []
        boxes_collected = []

        # Convert list of rect_points to a NumPy array
        if rect_points_iterable:
            rect_points_np = np.array(rect_points_iterable)
            logger.debug("Original rect_points_np shape: %s", rect_points_np.shape)

            # Apply Savitzky-Golay smoothing if configured and data is valid
            if (
                hasattr(self, "savgol_window")
                and self.savgol_window > 0
                and hasattr(self, "savgol_polyorder")
                and self.savgol_polyorder >= 0
            ):
                if (
                    rect_points_np.ndim == 3
                    and rect_points_np.shape[0] >= self.savgol_window
                ):
                    logger.debug(
                        "Attempting to apply Savitzky-Golay filter to rect_points of shape: %s",
                        rect_points_np.shape,
                    )
                    try:
                        smoothed_rect_points = apply_savgol_filter(
                            rect_points_np,
                            window_length=self.savgol_window,
                            polyorder=self.savgol_polyorder,
                            axis=0,
                            debug_plot=self.debug_mode,
                            debug_plot_indices=(0, 0),
                            debug_save_path="debug_output/filters_test",
                            debug_plot_filename_prefix="common_savgol",
                        )
                    except Exception as e:
                        logger.error(
                            "Error applying Savitzky-Golay filter: %s. Using raw points.",
                            e,
                            exc_info=True,
                        )
                        smoothed_rect_points = rect_points_np.copy()
                elif rect_points_np.ndim != 3:
                    logger.warning(
                        "Rect points array is not 3-dimensional (shape: %s). Skipping Savitzky-Golay filter.",
                        rect_points_np.shape,
                    )
                    smoothed_rect_points = rect_points_np.copy()
                else:  # Not enough frames
                    logger.warning(
                        "Not enough frames (%s) to apply Savitzky-Golay filter with window %s. Using raw points.",
                        rect_points_np.shape[0],
                        self.savgol_window,
                    )
                    smoothed_rect_points = rect_points_np.copy()
            else:
                logger.info(
                    "Savitzky-Golay filter not configured or misconfigured. Using raw rect_points."
                )
                smoothed_rect_points = rect_points_np.copy()
        else:
            logger.warning("No rect_points detected. Cannot apply smoothing.")
            smoothed_rect_points = np.array([])  # Empty array if no points

        if smoothed_rect_points.size > 0:
            logger.debug("Smoothed_rect_points shape: %s", smoothed_rect_points.shape)
        else:
            logger.debug("Smoothed_rect_points is empty.")

        # Apply affine transformation
        # If smoothed_rect_points is empty, zip will yield no items, and empty lists will be returned.
        for frame, rect_point in tqdm(
            zip(frames_iterable, smoothed_rect_points), total=len(frames_iterable)
        ):
            # transformed_frame_bgr is a BGR numpy array from _affine_transform
            transformed_frame_bgr, box, affine_matrix = self._affine_transform(
                frame, rect_point
            )
            # Convert BGR to RGB before appending
            transformed_frame_rgb = cv2.cvtColor(
                transformed_frame_bgr, cv2.COLOR_BGR2RGB
            )
            raw_transformed_frames.append(transformed_frame_rgb)
            affine_matrices_collected.append(affine_matrix)

            if return_boxes_config:
                boxes_collected.append(box)

        return raw_transformed_frames, affine_matrices_collected, boxes_collected

    def _process_for_inference(self, video_frames, return_boxes):
        """
        Process the video to apply affine transformation.

        Args:
            video_path: Path to the input video file
            video_frames: Numpy array of BGR video frames
        """
        frames, rect_points = self._detect_facial_landmarks_frames(video_frames)

        # raw_frames_rgb are now RGB numpy arrays from _apply_smoothing_and_transforms
        raw_frames_rgb, affine_matrices, boxes_collected = (
            self._apply_smoothing_and_transforms(
                frames_iterable=frames,
                rect_points_iterable=rect_points,
                return_boxes_config=return_boxes,
            )
        )

        processed_frames_for_tensor = []
        if raw_frames_rgb:
            for frame_rgb in raw_frames_rgb:
                # Frames are already RGB
                # Convert to tensor
                frame_tensor = rearrange(torch.from_numpy(frame_rgb), "h w c -> c h w")
                processed_frames_for_tensor.append(frame_tensor)

            transformed_frames_tensor = torch.stack(processed_frames_for_tensor)
        else:
            # Handle case with no frames, return empty tensor
            transformed_frames_tensor = torch.empty(
                (0, 3, self.resolution, self.resolution), dtype=torch.uint8
            )  # Assuming 3 channels and resolution

        if return_boxes:
            return transformed_frames_tensor, boxes_collected, affine_matrices
        else:
            return transformed_frames_tensor, [], affine_matrices

    def _process_for_preprocessing(self, video_path):
        """
        Process the video to apply affine transformation.

        Args:
            video_path: Path to the input video file
            output_path: Path to save the output video
        """
        capture = cv2.VideoCapture(video_path)

        # Detect face landmarks in the video
        frames, rect_points = self._detect_facial_landmarks_video(capture)

        # raw_frames_rgb are now RGB numpy arrays from _apply_smoothing_and_transforms
        # For preprocessing, we don't need bounding boxes from the common function.
        raw_frames_rgb, affine_matrices_collected, _ = (
            self._apply_smoothing_and_transforms(
                frames_iterable=frames,
                rect_points_iterable=rect_points,
                return_boxes_config=False,
            )
        )

        # The _apply_smoothing_and_transforms method returns RGB frames.

        return raw_frames_rgb, affine_matrices_collected

    def __call__(
        self,
        video_path: str | None = None,
        video_frames: np.ndarray | None = None,
        return_boxes: bool = False,
    ):
        if not video_path and video_frames is not None and len(video_frames) > 0:
            transformed_frames_tensor, boxes, affine_matrices = (
                self._process_for_inference(
                    video_frames=video_frames, return_boxes=return_boxes
                )
            )
            return transformed_frames_tensor, boxes, affine_matrices
        elif video_frames is None and video_path is not None:
            transformed_frames, affine_matrices = self._process_for_preprocessing(
                video_path
            )
            return transformed_frames, affine_matrices
        else:
            print(f"WRONG TRIGGER")

    def calculate_angles(self, video_path: str | None = None, video_frames: np.ndarray | None = None):
        if not video_path and video_frames is not None and len(video_frames) > 0:
            self.store_roll_angle = True
            landmarks, _ = self._detect_facial_landmarks_frames(video_frames)
            return landmarks, self.roll_angles
        elif video_frames is None and video_path is not None:
            self.store_roll_angle = True
            capture = cv2.VideoCapture(video_path)
            landmarks, _ = self._detect_facial_landmarks_video(capture)
            return landmarks, self.roll_angles
        else:
            raise ValueError("Either video_path or video_frames must be provided")


def main():
    from omegaconf import OmegaConf

    # TODO: Config file path a configurable parameter
    CONFIG = OmegaConf.load("src/modules/affine_transform/config.yaml")

    device = "cuda" if torch.cuda.is_available() else "cpu"

    affine_processor: AffineProcessor = AffineProcessor(
        config=CONFIG,
        det_config=CONFIG.detector.det_config,
        use_onnx=CONFIG.detector.use_onnx,
        device=device,
        debug_mode=False,
    )

    frame_bgr = cv2.imread("assets/test.png")

    affine_processor._detect_facial_landmarks(frame_bgr)


if __name__ == "__main__":
    main()
