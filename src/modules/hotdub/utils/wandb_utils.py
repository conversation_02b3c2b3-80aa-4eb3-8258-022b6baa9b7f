import wandb
import logging, os
from omegaconf import OmegaConf

logger = logging.getLogger(__name__)


def setup_wandb(config, exp_name):
    """Initialize wandb with error handling"""
    if not config.log.use_wandb:
        return False

    try:
        wandb.init(
            project=config.log.project_name,
            name=exp_name,
            config=OmegaConf.to_container(config),
            resume="allow",
        )

        # Define separate metrics for training and testing
        wandb.define_metric("train/step")
        wandb.define_metric("train/*", step_metric="train/step")

        wandb.define_metric("test/step")
        wandb.define_metric("test/*", step_metric="test/step")

        wandb.define_metric("validation/step")
        wandb.define_metric("validation/*", step_metric="validation/step")

        # Log the mask image
        mask_image_path = "hotdub/utils/mask.png"
        wandb.log({"mask_image": wandb.Image(mask_image_path)})

        return True
    except Exception as e:
        logger.error(f"Failed to initialize wandb: {str(e)}")
        return False


class LossAccumulator:
    def __init__(self):
        self.losses = []
        self.optional_metrics = {}

    def add(self, metrics):
        self.losses.append(metrics["loss"])
        # Track optional metrics separately
        for metric in [
            "recon_loss",
            "sync_loss",
            "perceptual_loss",
            "trepa_loss",
            "ms_ssim_score",
            "fvd_score",
        ]:
            if metric in metrics:
                if metric not in self.optional_metrics:
                    self.optional_metrics[metric] = []
                self.optional_metrics[metric].append(metrics[metric])

    def get_means_and_reset(self):
        if not self.losses:
            return {}

        means = {
            "step_loss": sum(self.losses) / len(self.losses),
            "total_loss": sum(self.losses) / len(self.losses),
        }

        # Calculate means for optional metrics
        for metric, values in self.optional_metrics.items():
            if values:
                means[metric] = sum(values) / len(values)

        # Reset accumulators
        self.losses = []
        self.optional_metrics = {}

        return means


def log_training_step(
    metrics, global_step, config, is_main_process, enable_wandb, loss_accumulator
):
    """Consolidated training step logging"""
    if not is_main_process:
        return

    # Always accumulate metrics, regardless of whether we're logging this step
    loss_accumulator.add(metrics)

    # Only log at specified intervals
    if not (enable_wandb and global_step % config.log.log_interval == 0):
        return

    mean_metrics = loss_accumulator.get_means_and_reset()

    wandb_logs = {
        "train/step": global_step,
        "train/learning_rate": metrics["lr"],
        "train/step_loss": mean_metrics["step_loss"],
        "train/total_loss": mean_metrics["total_loss"],
    }

    # Add optional metrics with train/ prefix
    for metric in [
        "recon_loss",
        "sync_loss",
        "perceptual_loss",
        "trepa_loss",
        "ms_ssim_score",
        "fvd_score",
    ]:
        if metric in mean_metrics:
            wandb_logs[f"train/{metric}"] = mean_metrics[metric]

    wandb.log(wandb_logs)


def log_checkpoint(checkpoint_data, global_step, is_main_process, enable_wandb):
    """Consolidated checkpoint logging"""
    if not (is_main_process and enable_wandb):
        return

    wandb_logs = {
        "validation/step": global_step,
    }

    # Log multiple validation videos if available
    if "validation_videos" in checkpoint_data:
        for idx, video_data in enumerate(checkpoint_data["validation_videos"]):
            video_name = (
                os.path.basename(video_data["video"]).split("_")[-1].split(".")[0]
            )
            wandb_logs[f"vv_{video_name}"] = wandb.Video(video_data["video"])
            if video_data["sync_score"] is not None:
                wandb_logs[f"sync_score_{idx}"] = video_data["sync_score"]

    # Add loss charts if available
    if "recon_loss_chart" in checkpoint_data:
        wandb_logs["recon_loss_chart"] = wandb.Image(
            checkpoint_data["recon_loss_chart"]
        )
    if "sync_loss_chart" in checkpoint_data:
        wandb_logs["Sync_loss_chart"] = wandb.Image(checkpoint_data["sync_loss_chart"])
    if "sync_conf_chart" in checkpoint_data:
        wandb_logs["Sync_confidence_chart"] = wandb.Image(
            checkpoint_data["sync_conf_chart"]
        )

    wandb.log(wandb_logs)
