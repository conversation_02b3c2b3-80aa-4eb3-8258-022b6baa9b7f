# Adapted from https://github.com/guanjz20/StyleSync/blob/main/utils.py

import numpy as np
import cv2
from scipy.signal import savgol_filter
from filterpy.kalman import Ka<PERSON>Filter
from collections import deque


def transformation_from_points(points1, points0, smooth=True, p_bias=None):
    points2 = np.array(points0)
    points2 = points2.astype(np.float64)
    points1 = points1.astype(np.float64)
    c1 = np.mean(points1, axis=0)
    c2 = np.mean(points2, axis=0)
    points1 -= c1
    points2 -= c2
    s1 = np.std(points1)
    s2 = np.std(points2)
    points1 /= s1
    points2 /= s2
    U, S, Vt = np.linalg.svd(np.matmul(points1.T, points2))
    R = (np.matmul(U, Vt)).T
    sR = (s2 / s1) * R
    T = c2.reshape(2, 1) - (s2 / s1) * np.matmul(R, c1.reshape(2, 1))
    M = np.concatenate((sR, T), axis=1)
    if smooth:
        bias = points2[2] - points1[2]
        if p_bias is None:
            p_bias = bias
        else:
            bias = p_bias * 0.2 + bias * 0.8
        p_bias = bias
        M[:, 2] = M[:, 2] + bias
    return M, p_bias


class AlignRestore(object):
    def __init__(self, align_points=3, face_template=None):
        if align_points == 3:
            self.upscale_factor = 1
            self.ratio = 2.8
            self.crop_ratio = (self.ratio, self.ratio)

            if face_template is None:
                self.face_template = np.array(
                    [[19 - 19, 30 - 10], [56 + 19, 30 - 10], [37.5, 45 - 5]]
                )
            else:
                self.face_template = face_template

            self.face_template = self.face_template * self.ratio
            self.face_size = (
                int(100 * self.crop_ratio[0]),
                int(100 * self.crop_ratio[1]),
            )
            self.p_bias = None

    def process(self, img, lmk_align=None, smooth=True, align_points=3):
        aligned_face, affine_matrix = self.align_warp_face(img, lmk_align, smooth)
        restored_img = self.restore_img(img, aligned_face, affine_matrix)
        cv2.imwrite("restored.jpg", restored_img)
        cv2.imwrite("aligned.jpg", aligned_face)
        return aligned_face, restored_img

    def align_warp_face(self, img, landmark, smooth=True, border_mode="constant"):
        affine_matrix, self.p_bias = transformation_from_points(
            landmark, self.face_template, smooth, self.p_bias
        )
        if border_mode == "constant":
            border_mode = cv2.BORDER_CONSTANT
        elif border_mode == "reflect101":
            border_mode = cv2.BORDER_REFLECT101
        elif border_mode == "reflect":
            border_mode = cv2.BORDER_REFLECT

        cropped_face = cv2.warpAffine(
            img,
            affine_matrix,
            self.face_size,
            flags=cv2.INTER_LANCZOS4,
            borderMode=border_mode,
            borderValue=[127, 127, 127],
        )
        return cropped_face, affine_matrix

    def align_warp_face2(self, img, landmark, border_mode="constant"):
        affine_matrix = cv2.estimateAffinePartial2D(landmark, self.face_template)[0]
        if border_mode == "constant":
            border_mode = cv2.BORDER_CONSTANT
        elif border_mode == "reflect101":
            border_mode = cv2.BORDER_REFLECT101
        elif border_mode == "reflect":
            border_mode = cv2.BORDER_REFLECT
        cropped_face = cv2.warpAffine(
            img,
            affine_matrix,
            self.face_size,
            borderMode=border_mode,
            borderValue=(135, 133, 132),
            flags=cv2.INTER_LANCZOS4,
        )
        return cropped_face, affine_matrix

    def restore_img(self, input_img, face, affine_matrix):
        h, w, _ = input_img.shape
        h_up, w_up = int(h * self.upscale_factor), int(w * self.upscale_factor)
        upsample_img = cv2.resize(
            input_img, (w_up, h_up), interpolation=cv2.INTER_LANCZOS4
        )
        inverse_affine = cv2.invertAffineTransform(affine_matrix)
        inverse_affine *= self.upscale_factor
        if self.upscale_factor > 1:
            extra_offset = 0.5 * self.upscale_factor
        else:
            extra_offset = 0
        inverse_affine[:, 2] += extra_offset
        inv_restored = cv2.warpAffine(
            face, inverse_affine, (w_up, h_up), flags=cv2.INTER_LANCZOS4
        )
        mask = np.ones((self.face_size[1], self.face_size[0]), dtype=np.float32)
        inv_mask = cv2.warpAffine(mask, inverse_affine, (w_up, h_up))
        inv_mask_erosion = cv2.erode(
            inv_mask,
            np.ones(
                (int(2 * self.upscale_factor), int(2 * self.upscale_factor)), np.uint8
            ),
        )
        pasted_face = inv_mask_erosion[:, :, None] * inv_restored
        total_face_area = np.sum(inv_mask_erosion)
        w_edge = int(total_face_area**0.5) // 20
        erosion_radius = w_edge * 2
        inv_mask_center = cv2.erode(
            inv_mask_erosion, np.ones((erosion_radius, erosion_radius), np.uint8)
        )
        blur_size = w_edge * 2
        inv_soft_mask = cv2.GaussianBlur(
            inv_mask_center, (blur_size + 1, blur_size + 1), 0
        )
        inv_soft_mask = inv_soft_mask[:, :, None]
        upsample_img = inv_soft_mask * pasted_face + (1 - inv_soft_mask) * upsample_img
        if np.max(upsample_img) > 256:
            upsample_img = upsample_img.astype(np.uint16)
        else:
            upsample_img = upsample_img.astype(np.uint8)
        return upsample_img


class laplacianSmooth:
    def __init__(self, smoothAlpha=0.3):
        self.smoothAlpha = smoothAlpha
        self.pts_last = None

    def smooth(self, pts_cur):
        if self.pts_last is None:
            self.pts_last = pts_cur.copy()
            return pts_cur.copy()
        x1 = min(pts_cur[:, 0])
        x2 = max(pts_cur[:, 0])
        y1 = min(pts_cur[:, 1])
        y2 = max(pts_cur[:, 1])
        width = x2 - x1
        pts_update = []
        for i in range(len(pts_cur)):
            x_new, y_new = pts_cur[i]
            x_old, y_old = self.pts_last[i]
            tmp = (x_new - x_old) ** 2 + (y_new - y_old) ** 2
            w = np.exp(-tmp / (width * self.smoothAlpha))
            x = x_old * w + x_new * (1 - w)
            y = y_old * w + y_new * (1 - w)
            pts_update.append([x, y])
        pts_update = np.array(pts_update)
        self.pts_last = pts_update.copy()

        return pts_update


class SavGolSmooth:
    """Applies Savitzky-Golay smoothing to a sequence of points."""

    def __init__(self, window_length, polyorder):
        """
        Initializes the SavGolSmooth filter.

        Args:
            window_length (int): The length of the filter window (must be a positive odd integer).
            polyorder (int): The order of the polynomial used to fit the samples
                             (must be less than window_length).
        """
        if (
            not isinstance(window_length, int)
            or window_length <= 0
            or window_length % 2 == 0
        ):
            raise ValueError("window_length must be a positive odd integer.")
        if not isinstance(polyorder, int) or polyorder >= window_length:
            raise ValueError("polyorder must be an integer less than window_length.")

        self.window_length = window_length
        self.polyorder = polyorder
        # Use deque for efficient history management
        self.history = deque(maxlen=window_length)

    def smooth(self, pts_cur):
        """
        Smooths the current set of points based on the history.

        Args:
            pts_cur (np.ndarray): The current points array of shape (N, 2).

        Returns:
            np.ndarray: The smoothed points array of shape (N, 2).
        """
        if (
            not isinstance(pts_cur, np.ndarray)
            or pts_cur.ndim != 2
            or pts_cur.shape[1] != 2
        ):
            raise ValueError("pts_cur must be a numpy array of shape (N, 2).")

        self.history.append(pts_cur)

        if len(self.history) < self.window_length:
            # Not enough data yet, return current points
            return pts_cur.copy()

        # Stack history: shape becomes (window_length, N, 2)
        history_stack = np.stack(self.history, axis=0)

        # Apply Savitzky-Golay filter to x and y coordinates separately
        # Ensure float type for filtering
        smoothed_x = savgol_filter(
            history_stack[:, :, 0].astype(float),
            self.window_length,
            self.polyorder,
            axis=0,
        )
        smoothed_y = savgol_filter(
            history_stack[:, :, 1].astype(float),
            self.window_length,
            self.polyorder,
            axis=0,
        )

        # Return the last smoothed point (corresponding to pts_cur)
        # Maintain original dtype if possible, otherwise float
        smoothed_pts = np.stack([smoothed_x[-1, :], smoothed_y[-1, :]], axis=-1)
        try:
            return smoothed_pts.astype(pts_cur.dtype)
        except (
            ValueError
        ):  # Handle potential casting issues (e.g., if original was int)
            return smoothed_pts


class EMAFilter:
    """Applies Exponential Moving Average smoothing to a sequence of points."""

    def __init__(self, alpha=0.5):
        """
        Initializes the EMAFilter.

        Args:
            alpha (float): The smoothing factor (0 < alpha <= 1).
                           Higher alpha gives more weight to recent points.
        """
        if not 0 < alpha <= 1:
            raise ValueError("alpha must be between 0 (exclusive) and 1 (inclusive).")
        self.alpha = alpha
        self.pts_last = None

    def smooth(self, pts_cur):
        """
        Smooths the current set of points using EMA.

        Args:
            pts_cur (np.ndarray): The current points array of shape (N, 2).

        Returns:
            np.ndarray: The smoothed points array of shape (N, 2).
        """
        if (
            not isinstance(pts_cur, np.ndarray)
            or pts_cur.ndim != 2
            or pts_cur.shape[1] != 2
        ):
            raise ValueError("pts_cur must be a numpy array of shape (N, 2).")

        if self.pts_last is None:
            self.pts_last = pts_cur.astype(
                float
            ).copy()  # Store as float for calculations
            return pts_cur.copy()

        # Ensure calculation is done in float
        pts_cur_float = pts_cur.astype(float)
        pts_smooth = self.alpha * pts_cur_float + (1 - self.alpha) * self.pts_last
        self.pts_last = pts_smooth.copy()

        # Return in original dtype if possible
        try:
            return pts_smooth.astype(pts_cur.dtype)
        except ValueError:
            return pts_smooth


class KalmanSmoother:
    """Applies Kalman smoothing to a sequence of points independently."""

    def __init__(
        self,
        process_noise_scale=1e-4,
        measurement_noise_scale=5e-2,
        initial_cov_scale=10.0,
        dt=0.5,
    ):
        """
        Initializes the KalmanSmoother.

        Args:
            process_noise_scale (float): Scale factor for process noise covariance (Q).
            measurement_noise_scale (float): Scale factor for measurement noise covariance (R).
            initial_cov_scale (float): Scale factor for initial state covariance (P).
            dt (float): Time step between measurements.
        """
        self.process_noise_scale = process_noise_scale
        self.measurement_noise_scale = measurement_noise_scale
        self.initial_cov_scale = initial_cov_scale
        self.dt = dt
        self.kalman_filters = None

    def _create_filter(self, initial_point):
        """Creates and configures a single Kalman filter instance."""
        kf = KalmanFilter(dim_x=4, dim_z=2)  # State: [x, y, vx, vy]
        # Ensure initial state is float
        kf.x = np.array([initial_point[0], initial_point[1], 0.0, 0.0], dtype=float)

        # State Transition Matrix (constant velocity model)
        kf.F = np.array(
            [[1, 0, self.dt, 0], [0, 1, 0, self.dt], [0, 0, 1, 0], [0, 0, 0, 1]],
            dtype=float,
        )

        # Measurement Function (we only measure position x, y)
        kf.H = np.array([[1, 0, 0, 0], [0, 1, 0, 0]], dtype=float)

        # Initial State Covariance
        kf.P = np.eye(4, dtype=float) * self.initial_cov_scale

        # Measurement Noise Covariance
        kf.R = np.eye(2, dtype=float) * self.measurement_noise_scale

        # Process Noise Covariance
        # Using filterpy's Q_discrete_white_noise might be more standard if needed
        q_base = np.array(
            [
                [self.dt**4 / 4, 0, self.dt**3 / 2, 0],
                [0, self.dt**4 / 4, 0, self.dt**3 / 2],
                [self.dt**3 / 2, 0, self.dt**2, 0],
                [0, self.dt**3 / 2, 0, self.dt**2],
            ],
            dtype=float,
        )
        kf.Q = q_base * self.process_noise_scale

        return kf

    def smooth(self, pts_cur):
        """
        Smooths the current set of points using Kalman filters.

        Args:
            pts_cur (np.ndarray): The current points array of shape (N, 2).

        Returns:
            np.ndarray: The smoothed points array of shape (N, 2).
        """
        if (
            not isinstance(pts_cur, np.ndarray)
            or pts_cur.ndim != 2
            or pts_cur.shape[1] != 2
        ):
            raise ValueError("pts_cur must be a numpy array of shape (N, 2).")

        pts_cur_float = pts_cur.astype(float)  # Work with floats

        if self.kalman_filters is None:
            # Initialize filters on the first call
            N = pts_cur_float.shape[0]
            self.kalman_filters = [
                self._create_filter(pts_cur_float[i]) for i in range(N)
            ]
            return pts_cur.copy()  # Return original points on first frame

        # Ensure number of filters matches number of points
        if len(self.kalman_filters) != pts_cur_float.shape[0]:
            # Re-initialize if the number of points changes
            N = pts_cur_float.shape[0]
            self.kalman_filters = [
                self._create_filter(pts_cur_float[i]) for i in range(N)
            ]
            # Decide whether to return current or smoothed based on re-init logic
            # For simplicity, returning current on re-init might be safer
            # Or attempt smoothing with the new filters immediately? Let's smooth immediately.
            # return pts_cur.copy() # Option 1: Return current on re-init

        smoothed_pts_float = np.zeros_like(pts_cur_float)
        for i, kf in enumerate(self.kalman_filters):
            kf.predict()
            kf.update(pts_cur_float[i])
            smoothed_pts_float[i] = kf.x[:2]  # Extract smoothed [x, y]

        # Return in original dtype if possible
        try:
            return smoothed_pts_float.astype(pts_cur.dtype)
        except ValueError:
            return smoothed_pts_float


class MovingAverageFilter:
    """Applies Moving Average smoothing to a sequence of points."""

    def __init__(self, window_size=5):
        """
        Initializes the MovingAverageFilter.

        Args:
            window_size (int): The number of past points to average.
        """
        if not isinstance(window_size, int) or window_size <= 0:
            raise ValueError("window_size must be a positive integer.")
        self.window_size = window_size
        # Store history as float for accurate averaging
        self.history = deque(maxlen=window_size)

    def smooth(self, pts_cur):
        """
        Smooths the current set of points using a moving average.

        Args:
            pts_cur (np.ndarray): The current points array of shape (N, 2).

        Returns:
            np.ndarray: The smoothed points array of shape (N, 2).
        """
        if (
            not isinstance(pts_cur, np.ndarray)
            or pts_cur.ndim != 2
            or pts_cur.shape[1] != 2
        ):
            raise ValueError("pts_cur must be a numpy array of shape (N, 2).")

        # Append current points as float
        self.history.append(pts_cur.astype(float))

        # Calculate the mean over the history buffer
        # Convert deque to numpy array for mean calculation
        mean_pts_float = np.mean(np.array(list(self.history)), axis=0)

        # Return in original dtype if possible
        try:
            return mean_pts_float.astype(pts_cur.dtype)
        except ValueError:
            return mean_pts_float
