"""
Utility functions for uploading training outputs to S3 and sending notifications
"""

import os
import yaml
import boto3
import requests
from typing import Op<PERSON>
from pathlib import Path
from botocore.client import Config
from datetime import datetime
from omegaconf import OmegaConf


def load_s3_config(main_config) -> dict:
    """
    Load S3 credentials from the config file path specified in the main config

    Args:
        main_config: OmegaConf configuration object containing bucket settings

    Returns:
        dict: S3 credentials loaded from the specified path

    Raises:
        TypeError: If main_config is not an OmegaConf object
        KeyError: If required credentials are missing
        FileNotFoundError: If credentials file does not exist
    """
    try:
        if not OmegaConf.is_config(main_config):
            raise TypeError("Expected OmegaConf config object, got {type(main_config)}")

        if not hasattr(main_config, "bucket"):
            main_config.bucket = {}
        if not hasattr(main_config.bucket, "credentials_path"):
            raise KeyError("bucket.credentials_path not found in config")

        creds_path = main_config.bucket.credentials_path
        print(f"Loading S3 credentials from {creds_path}")
        if not os.path.exists(creds_path):
            raise FileNotFoundError(f"Credentials file not found at {creds_path}")

        with open(creds_path) as f:
            creds = yaml.safe_load(f)

        required_keys = ["ENDPOINT_URL", "ACCESS_KEY", "SECRET_KEY", "BUCKET_NAME"]
        missing_keys = [k for k in required_keys if k not in creds]

        if missing_keys:
            raise KeyError(f"Missing required credentials: {missing_keys}")

        return creds
    except Exception as e:
        raise Exception(f"Error loading S3 config: {str(e)}")


def upload_directory_to_s3(
    local_dir: str, config_path: str, s3_prefix: Optional[str] = None
) -> bool:
    """
    Upload entire directory to S3 bucket

    Args:
        local_dir: Local directory path to upload
        config_path: Path to YAML config file containing bucket credentials path
        s3_prefix: Optional prefix/folder in S3 bucket (default: training_output_<timestamp>)

    Returns:
        bool: True if upload successful, False otherwise
    """
    try:
        config = OmegaConf.load(config_path)
        creds = load_s3_config(config)

        s3_client = boto3.client(
            "s3",
            endpoint_url=creds["ENDPOINT_URL"],
            aws_access_key_id=creds["ACCESS_KEY"],
            aws_secret_access_key=creds["SECRET_KEY"],
            config=Config(signature_version="s3v4"),
        )

        if not os.path.exists(local_dir):
            print(f"Error: Directory {local_dir} does not exist")
            return False

        # Create s3 prefix with timestamp if not provided
        if s3_prefix is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = os.path.basename(os.path.normpath(local_dir))
            s3_prefix = f"{output_dir}_{timestamp}"

        # Upload all files recursively
        for root, _, files in os.walk(local_dir):
            for file in files:
                local_path = os.path.join(root, file)

                # Create S3 key maintaining directory structure
                relative_path = os.path.relpath(local_path, local_dir)
                s3_key = f"{s3_prefix}/{relative_path}"

                print(f"Uploading {local_path} to {s3_key}")
                s3_client.upload_file(local_path, creds["BUCKET_NAME"], s3_key)

        print(
            f"\nUpload complete! Files uploaded to: s3://{creds['BUCKET_NAME']}/{s3_prefix}/"
        )
        return True

    except Exception as e:
        print(f"Error uploading to S3: {str(e)}")
        return False


def send_slack_notification(
    webhook_url: str, success: bool = True, message: Optional[str] = None
):
    """
    Send training completion notification via Slack

    Args:
        webhook_url: Slack webhook URL for posting messages
        success: Whether training completed successfully
        message: Optional message to include
    """
    if not webhook_url:
        print("Slack webhook URL not provided")
        return

    status = "✅ completed successfully" if success else "❌ failed"
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    payload = {"text": f"Training {status} at {timestamp}"}

    if message:
        payload["text"] += f"\nMessage: {message}"

    try:
        response = requests.post(webhook_url, json=payload)
        response.raise_for_status()
        print("Slack notification sent successfully")
    except Exception as e:
        print(f"Error sending Slack notification: {str(e)}")


def send_completion_notification(success: bool = True, message: Optional[str] = None):
    """
    Send training completion notification via console (fallback if Slack not configured)

    Args:
        success: Whether training completed successfully
        message: Optional message to include
    """
    status = "completed successfully" if success else "failed"
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    notif = f"Training {status} at {timestamp}"
    if message:
        notif += f"\nMessage: {message}"

    print("\n" + "=" * 50)
    print(notif)
    print("=" * 50)
