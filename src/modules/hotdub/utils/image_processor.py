"""
Enhanced image processing module for facial landmark detection and manipulation.

This module provides improved implementations of facial landmark detection, affine transformations,
and image masking operations with better error handling, optimized calculations, and comprehensive
documentation.

Key Features:
- Face detection using MediaPipe or Face Alignment
- Affine transformations for face alignment
- Multiple masking strategies (fixed, mouth, face, advanced)
- Temporal smoothing for stable video processing
- Comprehensive error handling and edge cases
"""

from __future__ import annotations
from enum import Enum, auto
from dataclasses import dataclass
import logging, sys
from pathlib import Path
import warnings
import random  # Add this import at the top of your file
from typing import Union, Tuple, List, Optional, Dict, Any

import os
import cv2
import mediapipe as mp
import numpy as np
import torch
from torchvision import transforms
from einops import rearrange
import face_alignment
from insightface.app import FaceAnalysis

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from .affine_transform import (
    AlignRestore,
    laplacianSmooth,
    SavGolSmooth,
    EMAFilter,
    <PERSON><PERSON>,
    MovingAverageFilter,
)


def draw_custom_bbox(
    image: np.ndarray, bbox: List[np.ndarray], color: Tuple[int, int, int] = (0, 255, 0)
) -> None:
    """
    Draw a custom bounding box on an image with lines connecting corners and center point.

    Args:
        image: Input image in BGR format (modified in-place)
        bbox: List of points [top_left, top_right, bottom_right, bottom_left, center]
              Each point should be a numpy array or list of [x, y] coordinates
        color: BGR color tuple for drawing (default: green)
    """
    # Convert points to integer coordinates
    points = np.array([bbox[:4]], dtype=np.int32)
    center = np.array(bbox[4], dtype=np.int32)

    # Draw the quadrilateral
    cv2.polylines(image, points, isClosed=True, color=color, thickness=2)

    # Draw lines from center to each corner
    for corner in bbox[:4]:
        corner_int = np.array(corner, dtype=np.int32)
        cv2.line(image, tuple(center), tuple(corner_int), color, thickness=2)

    # Draw center point
    cv2.circle(image, tuple(center), radius=3, color=color, thickness=-1)


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FaceDetectionError(Exception):
    """Raised when face detection fails."""

    pass


class MaskType(Enum):
    """Supported mask types for image processing."""

    FIXED = "fix_mask"
    MOUTH = "mouth"
    FACE = "face"
    HALF = "half"
    EYE = "eye"
    ADVANCED = "advanced"


class FaceDetector(Enum):
    """Supported face detection methods."""

    MEDIAPIPE = "mediapipe"
    FACE_ALIGNMENT = "face_alignment"
    NO_DETECT = "No_detect"

    @classmethod
    def from_str(cls, value: str) -> "FaceDetector":
        """Convert string to FaceDetector enum."""
        try:
            return cls(value)
        except ValueError:
            raise ValueError(
                f"Invalid face detector: {value}. Must be one of {[e.value for e in cls]}"
            )


@dataclass
class Point2D:
    """2D point representation."""

    x: float
    y: float

    def to_array(self) -> np.ndarray:
        """Convert point to numpy array."""
        return np.array([self.x, self.y])


class AffineHelper:
    """Helper class for affine transformation calculations."""

    def __init__(
        self,
        config: Optional[Dict] = None,
        debug_mode: bool = False,
        debug_level: int = 1,
    ):
        """
        Initialize AffineHelper.

        Args:
            config: Configuration dictionary containing padding settings
        """
        self.config = config or {}
        self.debug_mode = debug_mode
        self.debug_level = debug_level
        self.debug_dir = "debug_plots/debug_frames"

    def _calculate_angle(self, p1: Point2D, p2: Point2D) -> float:
        """
        Calculate the angle between two points with respect to the x-axis.

        Args:
            p1: First point
            p2: Second point

        Returns:
            float: Angle in radians
        """
        delta = np.array([p2.x - p1.x, p2.y - p1.y])
        return np.arctan2(delta[1], delta[0])

    def _rotate_point(self, point: Point2D, center: Point2D, angle: float) -> Point2D:
        """
        Rotate a point around a center by given angle.

        Args:
            point: Point to rotate
            center: Center of rotation
            angle: Rotation angle in radians

        Returns:
            Point2D: Rotated point
        """
        # Vectorized implementation using numpy
        p = np.array([point.x, point.y])
        c = np.array([center.x, center.y])

        # Create rotation matrix
        cos_a = np.cos(angle)
        sin_a = np.sin(angle)
        rotation_matrix = np.array([[cos_a, -sin_a], [sin_a, cos_a]])

        # Apply rotation
        translated = p - c
        rotated = rotation_matrix @ translated
        final = rotated + c

        return Point2D(final[0], final[1])

    def _rotate_rectangle(self, vertices: List[Point2D], angle: float) -> List[Point2D]:
        """
        Rotate rectangle vertices around their center.

        Args:
            vertices: List of rectangle vertices
            angle: Rotation angle in radians

        Returns:
            List[Point2D]: Rotated vertices
        """
        # Calculate center using numpy operations
        points = np.array([[v.x, v.y] for v in vertices])
        center = Point2D(*points.mean(axis=0))

        # Rotate each vertex
        return [self._rotate_point(v, center, angle) for v in vertices]

    def calculate_base_bbox(
        self,
        jaw_points: np.ndarray,
        left_eye: np.ndarray,
        right_eye: np.ndarray,
        nose: np.ndarray,
        image: np.ndarray,
    ) -> np.ndarray:
        """
        Calculate base bounding box for face alignment.
        Returns rectangle points in order: [top_left, top_right, bottom_right, bottom_left, center]
        """
        # Calculate the angle between eyes
        eye_angle = np.arctan2(right_eye[1] - left_eye[1], right_eye[0] - left_eye[0])

        # Find the minimum and maximum x-coordinates of jaw points
        min_x = np.min(jaw_points[:, 0])
        max_x = np.max(jaw_points[:, 0])

        bbox_padding = self.config.padding.bbox if self.config else 0.0

        # Calculate width and add padding
        width = max_x - min_x
        pad_x = width * bbox_padding
        min_x -= pad_x
        max_x += pad_x

        # Create top edge points that pass through the nose
        top_left = np.array([min_x, nose[1]])
        top_right = np.array([max_x, nose[1]])

        # Calculate height using jaw points
        max_y = np.max(jaw_points[:, 1])
        height = max_y - nose[1]
        height_ratio = self.config.padding.box_height_ratio if self.config else 1.0
        desired_height = height * height_ratio

        # Create bottom points
        bottom_left = np.array([min_x, nose[1] + desired_height])
        bottom_right = np.array([max_x, nose[1] + desired_height])

        # Assemble box points
        box = np.vstack(
            [
                top_left,  # top_left
                top_right,  # top_right
                bottom_right,  # bottom_right
                bottom_left,  # bottom_left
            ]
        )

        # Rotate box to align with eye angle if needed
        if not np.isclose(eye_angle, 0):
            # Calculate center of rotation (nose point)
            center = nose

            # Create rotation matrix
            cos_a = np.cos(eye_angle)
            sin_a = np.sin(eye_angle)
            R = np.array([[cos_a, -sin_a], [sin_a, cos_a]])

            # Rotate each point around the nose
            box = np.array([(R @ (p - center)) + center for p in box])

        frame_id = random.randint(0, 10000)
        center = np.mean(box, axis=0)

        if self.debug_mode and self.debug_level in [2, 3]:
            if self.debug_level == 2:
                debug_img = image.copy()
            elif self.debug_level == 3:
                debug_img = image
            # Draw jaw points
            for point in jaw_points:
                cv2.circle(
                    debug_img, (int(point[0]), int(point[1])), 2, (0, 255, 0), -1
                )
            # Draw eyes and nose
            cv2.circle(
                debug_img, (int(left_eye[0]), int(left_eye[1])), 5, (0, 0, 255), -1
            )
            cv2.circle(
                debug_img, (int(right_eye[0]), int(right_eye[1])), 5, (0, 0, 255), -1
            )
            cv2.circle(debug_img, (int(nose[0]), int(nose[1])), 5, (0, 0, 255), -1)

            # Draw box with labeled corners
            box_int = box.astype(np.int32)
            cv2.drawContours(debug_img, [box_int], 0, (255, 0, 0), 2)
            labels = ["tl(0)", "tr(1)", "br(2)", "bl(3)"]
            for i, (x, y) in enumerate(box_int):
                cv2.putText(
                    debug_img,
                    labels[i],
                    (int(x), int(y)),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.5,
                    (255, 0, 0),
                    1,
                )

            if self.debug_level == 2:
                cv2.imwrite(
                    f"debug_plots/debug_frames/bbox_final_{frame_id}.png", debug_img
                )

        # Ensure bounds are within image
        height, width = image.shape[:2]
        box = np.clip(box, [0, 0], [width, height])

        # Calculate center point
        center = np.mean(box, axis=0)

        # Prepare final output with center point
        rect_points_input = np.vstack(
            [box, center]
        )  # [top_left, top_right, bottom_right, bottom_left, center]

        return rect_points_input


class ImageProcessor:
    """
    Advanced image processor for facial detection and manipulation.

    This class provides functionality for:
    - Face detection and landmark extraction
    - Image masking with various strategies
    - Affine transformations for face alignment
    - Temporal smoothing for video processing

    Attributes:
        resolution (int): Target resolution for processed images
        mask_type (MaskType): Type of mask to apply
        device (str): Computing device (cpu/cuda)
        debug_mode (bool): Enable debug mode for additional logging
    """

    def __init__(
        self,
        resolution: int = 512,
        mask_type: Union[str, MaskType] = MaskType.FIXED,
        device: str = "cpu",
        mask_path: Optional[str] = "hotdub/utils/mask.png",
        face_detector: Union[str, FaceDetector] = FaceDetector.MEDIAPIPE,
        mask_image: Optional[torch.Tensor] = None,
        smooth_type: str = "savgol",
        debug_mode: bool = False,
        debug_level: int = 1,
        config: Optional[Dict] = None,
        local_rank: int = 0,
    ):
        """
        Initialize the ImageProcessor.

        Args:
            resolution: Output image resolution
            mask_type: Type of mask to apply
            device: Computing device (cpu/cuda)
            mask_path: Path to fixed mask image
            face_detector: Face detection method
            mask_image: Pre-loaded mask tensor
            smooth_type: Smoothing algorithm (savgol/laplacian)
            debug_mode: Enable debug logging
            config: Image processor configuration from YAML
        """
        # Store config
        self.config = config or {}

        # Input validation
        if isinstance(mask_type, str):
            try:
                self.mask_type = MaskType(mask_type)
            except ValueError:
                raise ValueError(f"Invalid mask_type: {mask_type}")
        else:
            self.mask_type = mask_type

        if isinstance(face_detector, str):
            try:
                self.face_detector = FaceDetector.from_str(face_detector)
            except ValueError as e:
                raise ValueError(str(e))
        else:
            self.face_detector = face_detector

        if not isinstance(resolution, int) or resolution <= 0:
            raise ValueError("Resolution must be a positive integer")

        self.resolution = resolution
        self.device = device
        self.mask_path = mask_path
        self.mask_image = mask_image
        self.smooth_type = smooth_type
        self.debug_mode = debug_mode
        self.debug_level = debug_level
        self.local_rank = local_rank

        # Initialize transforms
        self.resize = transforms.Resize(
            (resolution, resolution),
            interpolation=transforms.InterpolationMode.BILINEAR,
            antialias=True,
        )
        self.normalize = transforms.Normalize([0.5], [0.5], inplace=True)

        # Initialize smoothing
        if smooth_type == "laplacian":
            self.rect_smoother = laplacianSmooth(0.3)
            self.mask_smoother = laplacianSmooth(0.3)
        elif smooth_type == "savgol":
            self.rect_smoother = SavGolSmooth(21, 4)
            self.mask_smoother = SavGolSmooth(2, 1)
            self.double_smoother = EMAFilter(0.2)
        elif smooth_type == "EMA":
            self.rect_smoother = EMAFilter(0.5)
            self.mask_smoother = EMAFilter(0.5)
        elif smooth_type == "kalman":
            self.rect_smoother = KalmanSmoother(
                process_noise_scale=1e-4,
                measurement_noise_scale=5e-2,
                initial_cov_scale=10.0,
                dt=0.5,
            )
            self.double_smoother = EMAFilter(0.2)
            self.mask_smoother = KalmanSmoother()
        elif smooth_type == "moving_average":
            self.rect_smoother = MovingAverageFilter(5, False)
            self.mask_smoother = MovingAverageFilter(5, False)
        else:
            raise ValueError(f"Invalid smooth_type: {smooth_type}")

        # Initialize face detection
        self._setup_face_detector()

        # Initialize helpers
        self.affine_helper = AffineHelper(
            self.config, self.debug_mode, self.debug_level
        )

        # Load fixed mask if needed
        if self.mask_type == MaskType.FIXED:
            if self.mask_image is None and self.mask_path:
                self.mask_image = self._load_fixed_mask(resolution, self.mask_path)
            elif self.mask_image is None:
                raise ValueError(
                    "Either mask_image or mask_path must be provided for fixed mask"
                )

        # Debug tracking
        if debug_mode:
            self.rect_points_history = {
                "frame_idx": [],
                "raw": {"top_left": [], "top_right": [], "nose_mid": []},
                "smooth": {"top_left": [], "top_right": [], "nose_mid": []},
            }
            self.current_frame = 0

    def _setup_face_detector(self):
        """Initialize face detection based on selected method."""
        if self.face_detector == FaceDetector.NO_DETECT:
            return  # Skip face detector setup when NO_DETECT is selected

        if self.config and "face_templates" in self.config:
            # Load templates from config
            mp_template = self.config["face_templates"]["mediapipe"]
            fa_template = self.config["face_templates"]["face_alignment"]

            self.mediapipe_template = np.array(
                [
                    mp_template[0],  # Left eye position
                    mp_template[1],  # Right eye position
                    mp_template[2],  # Nose position
                ]
            )
            self.face_alignment_template = np.array(
                [
                    fa_template[0],  # Left eye position
                    fa_template[1],  # Right eye position
                    fa_template[2],  # Nose position
                ]
            )
        else:
            # Fallback to default templates
            self.mediapipe_template = np.array(
                [[19 - 10, 30 - 10], [56 + 10, 30 - 10], [37.5, 45 + 5]]
            )
            self.face_alignment_template = np.array(
                [[19 - 11, 30 - 10], [56 + 11, 30 - 10], [37.5, 45 - 5]]
            )

        if self.face_detector == FaceDetector.FACE_ALIGNMENT:
            if self.device == "cpu":
                raise ValueError("Face alignment requires GPU. Use mediapipe for CPU.")
            self.restorer = AlignRestore(face_template=self.face_alignment_template)
            self.fa = face_alignment.FaceAlignment(
                face_alignment.LandmarksType.TWO_D, flip_input=False, device=self.device
            )
        elif self.face_detector == FaceDetector.MEDIAPIPE:  # MEDIAPIPE
            self.restorer = AlignRestore(face_template=self.mediapipe_template)
            detector_model_path = self.config.mediapipe_model_path

            # Initialize mediapipe components
            BaseOptions = mp.tasks.BaseOptions
            VisionRunningMode = mp.tasks.vision.RunningMode

            self.FaceLandmarker = mp.tasks.vision.FaceLandmarker
            FaceLandmarkerOptions = mp.tasks.vision.FaceLandmarkerOptions

            # Create a face detector instance with the image mode:

            self.options = FaceLandmarkerOptions(
                base_options=BaseOptions(
                    model_asset_path=detector_model_path,
                    delegate=BaseOptions.Delegate.CPU,
                ),
                num_faces=1,
                output_face_blendshapes=False,  # Disable blendshapes output
                output_facial_transformation_matrixes=False,  # Disable transformation matrices
                min_face_detection_confidence=0.5,
                min_face_presence_confidence=0.5,
                min_tracking_confidence=0.5,
                running_mode=VisionRunningMode.IMAGE,
            )

            # Setup insightface
            self.if_det = FaceAnalysis(name="buffalo_l")
            self.if_det.prepare(ctx_id=self.local_rank, det_size=(640, 640))
        else:
            logger.warning(f"Unsupported face detector: {self.face_detector}")

    def _load_fixed_mask(self, resolution: int, mask_path: str) -> torch.Tensor:
        """
        Load and preprocess fixed mask image.

        Args:
            resolution: Target resolution
            mask_path: Path to mask image

        Returns:
            torch.Tensor: Processed mask tensor
        """
        if not os.path.exists(mask_path):
            raise FileNotFoundError(f"Mask file not found: {mask_path}")

        try:
            mask_image = cv2.imread(mask_path)
            if mask_image is None:
                raise ValueError(f"Failed to load mask image: {mask_path}")

            mask_image = cv2.cvtColor(mask_image, cv2.COLOR_BGR2RGB)
            mask_image = (
                cv2.resize(
                    mask_image, (resolution, resolution), interpolation=cv2.INTER_AREA
                )
                / 255.0
            )
            return rearrange(torch.from_numpy(mask_image), "h w c -> c h w")
        except Exception as e:
            raise RuntimeError(f"Error loading mask: {str(e)}")

    def _crop_face_region(
        self,
        image: np.ndarray,
    ) -> Tuple[np.ndarray, Tuple[int, int, int, int]]:
        """
        Crop the image around the largest detected face with padding.

        Args:
            image: Input image
            padding_factor: Amount of padding around face bbox (0.3 = 30%)

        Returns:
            Tuple containing:
            - Cropped image array
            - Tuple of crop coordinates (x1, y1, x2, y2)

        Raises:
            FaceDetectionError: If no faces detected
        """
        height, width = image.shape[:2]
        padding_factor = (
            self.config.padding.insightface_pad_factor if self.config else 0.3
        )

        # Detect using insightface
        faces = self.if_det.get(image)
        if len(faces) == 0:
            raise FaceDetectionError("No faces detected by insightface")

        # Get the largest face
        largest_face = max(faces, key=lambda x: x.bbox[2] * x.bbox[3])

        # Get bbox coordinates
        x1, y1, x2, y2 = largest_face.bbox[:4]

        # Calculate padding
        bbox_width = x2 - x1
        bbox_height = y2 - y1
        pad_x = int(bbox_width * padding_factor)
        pad_y = int(bbox_height * padding_factor)

        # Calculate padded coordinates
        crop_x1 = max(0, int(x1 - pad_x))
        crop_y1 = max(0, int(y1 - pad_y))
        crop_x2 = min(width, int(x2 + pad_x))
        crop_y2 = min(height, int(y2 + pad_y))

        # Crop the image
        cropped_image = image[crop_y1:crop_y2, crop_x1:crop_x2]

        if self.debug_mode and self.debug_level == 0:
            # Print shape, Colorspace, and dtype of cropped image
            print(f"Cropped image shape: {cropped_image.shape}")
            print(f"Cropped image dtype: {cropped_image.dtype}")
            cv2.imwrite(
                "debug_plots/debug_frames/cropped_frame.jpg",
                cv2.cvtColor(cropped_image, cv2.COLOR_RGB2BGR),
            )

        return cropped_image, (crop_x1, crop_y1, crop_x2, crop_y2)

    def _get_largest_face(
        self,
        landmarks: Any,
        detector_type: Union[str, FaceDetector] = FaceDetector.MEDIAPIPE,
    ) -> Any:
        """
        Select the face with largest bounding box from multiple detections.

        Args:
            landmarks: Face landmarks from detector
            detector_type: Type of face detector used

        Returns:
            Landmarks for the largest detected face

        Raises:
            FaceDetectionError: If no faces are detected
        """
        if detector_type == FaceDetector.MEDIAPIPE:
            if not landmarks.face_landmarks:
                raise FaceDetectionError("No faces detected in the image")

            largest_face = None
            max_area = 0

            for face_landmarks in landmarks.face_landmarks:
                # Find bounds efficiently using numpy
                points = np.array([[lm.x, lm.y] for lm in face_landmarks])
                min_xy = points.min(axis=0)
                max_xy = points.max(axis=0)
                area = np.prod(max_xy - min_xy)

                if area > max_area:
                    max_area = area
                    largest_face = face_landmarks

            return largest_face

        elif detector_type == FaceDetector.FACE_ALIGNMENT:
            if landmarks is None:
                raise FaceDetectionError("No faces detected in the image")
            return landmarks[0]

        elif detector_type == FaceDetector.INSIGHTFACE:
            if landmarks is None:
                raise FaceDetectionError("No faces detected in the image")

            # Get the largest face
            largest_face = max(landmarks, key=lambda x: x.bbox[2] * x.bbox[3])
            return largest_face

        else:
            raise ValueError(f"Unsupported detector type: {detector_type}")

    def _detect_facial_landmarks(
        self, image: np.ndarray, fallback: bool = False
    ) -> np.ndarray:
        """
        Detect facial landmarks in image.

        Args:
            image: Input image
            fallback: Whether to try face_alignment if mediapipe fails

        Returns:
            np.ndarray: Array of landmark coordinates

        Raises:
            FaceDetectionError: If face detection fails or if detector is NO_DETECT
        """
        if self.face_detector == FaceDetector.NO_DETECT:
            raise FaceDetectionError(
                "Face detection was called but face_detector is set to NO_DETECT"
            )

        height, width = image.shape[:2]
        self.mediapipe_failed = False

        try:
            if self.face_detector == FaceDetector.MEDIAPIPE:
                if self.debug_mode and self.debug_level == 0:
                    os.makedirs("debug_plots/debug_frames", exist_ok=True)
                    cv2.imwrite(
                        "debug_plots/debug_frames/input_frame.jpg",
                        cv2.cvtColor(image, cv2.COLOR_RGB2BGR),
                    )

                # Crop around face region
                cropped_image, (crop_x1, crop_y1, crop_x2, crop_y2) = (
                    self._crop_face_region(image.copy())
                )
                # convert cropped image to uint8 and RGB
                # cropped_image = cv2.cvtColor(cropped_image, cv2.COLOR_RGB2BGR)
                cropped_image = cropped_image.astype(np.uint8)

                # Detect landmarks on cropped image
                with self.FaceLandmarker.create_from_options(
                    self.options
                ) as landmarker:
                    mp_frame = mp.Image(
                        image_format=mp.ImageFormat.SRGB, data=cropped_image
                    )
                    results = landmarker.detect(mp_frame)

                if not results.face_landmarks:
                    if fallback:
                        logger.warning(
                            "MediaPipe failed, falling back to face_alignment"
                        )
                        self.mediapipe_failed = True
                        fa = face_alignment.FaceAlignment(
                            face_alignment.LandmarksType.TWO_D,
                            flip_input=False,
                            device=self.device,
                        )
                        results = fa.get_landmarks(image)
                    else:
                        # if self.debug_mode:
                        cv2.imwrite(
                            "debug_plots/debug_frames/failed_frame.jpg",
                            cv2.cvtColor(image, cv2.COLOR_RGB2BGR),
                        )
                        raise FaceDetectionError(
                            f"No faces detected by MediaPipe and frame saved as debug_plots/debug_frames/failed_frame.jpg"
                        )

                if results is None:
                    # if self.debug_mode:
                    cv2.imwrite(
                        "debug_plots/debug_frames/failed_frame.jpg",
                        cv2.cvtColor(image, cv2.COLOR_RGB2BGR),
                    )
                    raise FaceDetectionError(
                        "Face detection failed and frame saved as debug_plots/debug_frames/failed_frame.jpg"
                    )

                if self.mediapipe_failed:
                    landmark_coordinates = self._get_largest_face(
                        results, FaceDetector.FACE_ALIGNMENT
                    )
                    jaw_points = landmark_coordinates[1:16]
                    left_eye = landmark_coordinates[17:22].mean(0)
                    right_eye = landmark_coordinates[22:27].mean(0)
                    nose = landmark_coordinates[27:36].mean(0)
                else:
                    face_landmarks = self._get_largest_face(
                        results, FaceDetector.MEDIAPIPE
                    )
                    # Convert relative coordinates to absolute coordinates in original frame
                    cropped_h, cropped_w = cropped_image.shape[:2]
                    landmark_coordinates = np.array(
                        [
                            [
                                int(lm.x * cropped_w) + crop_x1,
                                int(lm.y * cropped_h) + crop_y1,
                            ]
                            for lm in face_landmarks
                        ]
                    )
                    jaw_points = landmark_coordinates[jaw_line_ext_idx]
                    left_eye = landmark_coordinates[
                        right_eye_idx + right_iris_idx
                    ].mean(0)
                    right_eye = landmark_coordinates[left_eye_idx + left_iris_idx].mean(
                        0
                    )
                    # nose = landmark_coordinates[nose_idx].mean(0)
                    nose = landmark_coordinates[4]

            elif self.face_detector == FaceDetector.FACE_ALIGNMENT:
                landmarks = self.fa.get_landmarks(image)
                if landmarks is None:
                    raise FaceDetectionError("No faces detected by face_alignment")
                landmark_coordinates = self._get_largest_face(
                    landmarks, FaceDetector.FACE_ALIGNMENT
                )
                jaw_points = landmark_coordinates[1:16]
                left_eye = landmark_coordinates[17:22].mean(0)
                right_eye = landmark_coordinates[22:27].mean(0)
                nose = landmark_coordinates[27:36].mean(0)

            else:
                raise ValueError(
                    f"Unsupported detector type: {self.face_detector.value}"
                )

            kps = np.array([left_eye, right_eye, nose])

            return landmark_coordinates, jaw_points, kps

        except Exception as e:
            logger.error(f"Face detection error: {str(e)}")
            raise FaceDetectionError(f"Face detection failed: {str(e)}")

    def _preprocess_one_masked_image(
        self, image: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Preprocess single image with masking.

        Args:
            image: Input image tensor

        Returns:
            Tuple containing:
            - Original image tensor
            - Masked image tensor
            - Mask tensor
        """
        image = self.resize(image)

        if self.mask_type in [MaskType.MOUTH, MaskType.FACE]:
            landmark_coordinates, jaw_points, kps = self._detect_facial_landmarks(image)
            surround_landmarks = (
                mouth_surround_landmarks
                if self.mask_type == MaskType.MOUTH
                else face_surround_landmarks
            )

            points = np.array([landmark_coordinates[idx] for idx in surround_landmarks])
            mask = np.ones((self.resolution, self.resolution))
            mask = cv2.fillPoly(mask, pts=[points], color=(0, 0, 0))
            mask = torch.from_numpy(mask).unsqueeze(0)

        elif self.mask_type == MaskType.HALF:
            mask = torch.ones((self.resolution, self.resolution))
            mask[self.resolution // 2 :, :] = 0
            mask = mask.unsqueeze(0)

        elif self.mask_type == MaskType.EYE:
            mask = torch.ones((self.resolution, self.resolution))
            landmark_coordinates = self._detect_facial_landmarks(image)
            y = landmark_coordinates[195][1]
            mask[y:, :] = 0
            mask = mask.unsqueeze(0)

        elif self.mask_type == MaskType.ADVANCED:
            rgb_image = rearrange(image.clone(), "c h w -> h w c").numpy()
            rgb_image = cv2.cvtColor(rgb_image, cv2.COLOR_RGB2BGR)

            # Save and read image to avoid mp.Image error
            # cv2.imwrite("temp/input_frame.png", rgb_image)
            # _read_image = cv2.imread("temp/input_frame.png")

            landmark_coordinates, jaw_points, kps = self._detect_facial_landmarks(
                rgb_image
            )

            # Get nose midpoint factor from config or use default
            nose_mid_factor = (
                self.config.padding.nose_mid_factor if self.config else 0.8
            )

            left_eye = kps[0]
            right_eye = kps[1]
            nose = kps[2]

            # Calculate key points
            eye_center = (left_eye + right_eye) / 2
            nose_mid = eye_center + (nose - eye_center) * nose_mid_factor

            # Calculate bounds
            min_x = np.min(landmark_coordinates[:, 0])
            max_x = np.max(landmark_coordinates[:, 0])
            min_y = nose[1]
            max_y = np.max(landmark_coordinates[:, 1])

            # Get detector-specific padding from config or use defaults
            if self.config and "padding" in self.config:
                pad_factor = (
                    self.config.padding.mediapipe
                    if self.face_detector == FaceDetector.MEDIAPIPE
                    else self.config.padding.face_alignment
                )
            else:
                pad_factor = (
                    0.05 if self.face_detector == FaceDetector.MEDIAPIPE else 0.1
                )
            pad_x = (max_x - min_x) * pad_factor
            pad_y = (max_y - min_y) * pad_factor

            # Ensure bounds are within image
            min_x = np.clip(min_x - pad_x, 0, image.shape[2])
            max_x = np.clip(max_x + pad_x, 0, image.shape[2])
            min_y = np.clip(min_y - pad_y, 0, image.shape[1])
            max_y = np.clip(max_y + pad_y, 0, image.shape[1])

            # Create rectangle points
            rect_points = np.array(
                [
                    [min_x, min_y],  # top-left
                    [max_x, min_y],  # top-right
                    [min_x, max_y],  # bottom-left
                    [max_x, max_y],  # bottom-right
                    nose_mid,  # nose
                ]
            )

            if self.debug_mode and self.debug_level == 1:
                self.rect_points_history["frame_idx"].append(self.current_frame)
                self.rect_points_history["raw"]["top_left"].append(
                    rect_points[0].copy()
                )
                self.rect_points_history["raw"]["top_right"].append(
                    rect_points[1].copy()
                )
                self.rect_points_history["raw"]["nose_mid"].append(
                    rect_points[4].copy()
                )

            # Apply smoothing
            rect_points = self.mask_smoother.smooth(rect_points)

            if self.debug_mode and self.debug_level == 1:
                self.rect_points_history["smooth"]["top_left"].append(
                    rect_points[0].copy()
                )
                self.rect_points_history["smooth"]["top_right"].append(
                    rect_points[1].copy()
                )
                self.rect_points_history["smooth"]["nose_mid"].append(
                    rect_points[4].copy()
                )
                self.current_frame += 1

            # Extract points
            t_l, t_r, _, _, n_m = rect_points

            # Get height ratio from config or use default
            height_ratio = self.config.padding.height_ratio if self.config else 0.95

            # Calculate dimensions
            width = np.linalg.norm(t_l - t_r)
            height = width * height_ratio

            # Create mask
            x_0 = int(t_l[0])
            x_1 = int(t_r[0])
            y_0 = int(t_l[1])
            y_1 = int(min(t_l[1] + height, self.resolution))

            # TODO: Add Smoother

            mask = np.ones((self.resolution, self.resolution))
            mask[y_0:y_1, x_0:x_1] = 0

            # Smooth mask edges
            mask = cv2.GaussianBlur(mask, (9, 9), 0)
            mask = torch.from_numpy(mask).unsqueeze(0)

        else:
            raise ValueError(f"Invalid mask type: {self.mask_type}")

        # Normalize image
        image = image.to(dtype=torch.float32)
        pixel_values = self.normalize(image / 255.0)
        masked_pixel_values = pixel_values * mask

        if self.mask_type != MaskType.ADVANCED:
            mask = 1 - mask

        return pixel_values, masked_pixel_values, mask

    def _preprocess_fixed_mask_image(
        self, image: torch.Tensor, affine_transform: bool = False
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Preprocess image with fixed mask.

        Args:
            image: Input image tensor
            affine_transform: Whether to apply affine transformation

        Returns:
            Tuple containing:
            - Original image tensor
            - Masked image tensor
            - Mask tensor
        """
        if affine_transform:
            image, _, _ = self.affine_transform(image)
        else:
            image = self.resize(image)

        pixel_values = self.normalize(image / 255.0)
        masked_pixel_values = pixel_values * self.mask_image
        return pixel_values, masked_pixel_values, self.mask_image[0:1]

    def affine_transform(
        self, image: torch.Tensor
    ) -> Tuple[torch.Tensor, List[int], np.ndarray]:
        """
        Apply affine transformation to align face.

        Args:
            image: Input image tensor

        Returns:
            Tuple containing:
            - Transformed image tensor
            - Bounding box coordinates
            - Affine transformation matrix

        Raises:
            FaceDetectionError: If face detection fails or if detector is NO_DETECT
        """
        if self.face_detector == FaceDetector.NO_DETECT:
            raise FaceDetectionError(
                "Affine transform was called but face_detector is set to NO_DETECT"
            )

        try:
            _, jaw_points, kps = self._detect_facial_landmarks(image, fallback=True)

            left_eye = kps[0]
            right_eye = kps[1]
            nose = kps[2]

            # Backup template
            template_backup = self.restorer.face_template

            # Select appropriate template
            if (
                self.mediapipe_failed
                or self.face_detector == FaceDetector.FACE_ALIGNMENT
            ):
                self.restorer.face_template = (
                    self.face_alignment_template * self.restorer.ratio
                )
            else:
                self.restorer.face_template = (
                    self.mediapipe_template * self.restorer.ratio
                )

            # Calculate reference points
            ref_points = self.affine_helper.calculate_base_bbox(
                jaw_points, left_eye, right_eye, nose, image
            )

            if self.debug_mode:
                draw_custom_bbox(image, ref_points, color=(0, 0, 255))

            if self.debug_mode and self.debug_level == 1:
                self.rect_points_history["frame_idx"].append(self.current_frame)
                self.rect_points_history["raw"]["top_left"].append(ref_points[0].copy())
                self.rect_points_history["raw"]["top_right"].append(
                    ref_points[1].copy()
                )
                self.rect_points_history["raw"]["nose_mid"].append(ref_points[4].copy())

            ref_points = self.rect_smoother.smooth(ref_points)
            if self.debug_mode:
                draw_custom_bbox(image, ref_points, color=(0, 255, 0))

            if self.debug_mode and self.debug_level == 1:
                self.rect_points_history["smooth"]["top_left"].append(
                    ref_points[0].copy()
                )
                self.rect_points_history["smooth"]["top_right"].append(
                    ref_points[1].copy()
                )
                self.rect_points_history["smooth"]["nose_mid"].append(
                    ref_points[4].copy()
                )
                self.current_frame += 1

            if self.smooth_type in ["kalman"]:
                ref_points = self.double_smoother.smooth(ref_points)

            # Get alignment points
            idx = [0, 1, -1]  # top-left, top-right, center
            if self.config.face_templates.type == "three_point":
                face, affine_matrix = self.restorer.align_warp_face(
                    image.copy(), landmark=ref_points[idx], border_mode="constant"
                )
            elif self.config.face_templates.type == "four_point":
                face, affine_matrix = self.restorer.align_warp_face_rect(
                    image.copy(), landmark=ref_points, border_mode="constant"
                )
            else:
                raise ValueError(
                    f"Invalid face template type: {self.config.face_templates.type}"
                )

            # Calculate bounding box
            box = [0, 0, face.shape[1], face.shape[0]]

            # Resize with appropriate interpolation
            if face.shape[1] < self.resolution:
                face = cv2.resize(
                    face,
                    (self.resolution, self.resolution),
                    interpolation=cv2.INTER_LANCZOS4,
                )
            else:
                face = cv2.resize(
                    face,
                    (self.resolution, self.resolution),
                    interpolation=cv2.INTER_AREA,
                )

            # Convert to tensor
            face = rearrange(torch.from_numpy(face), "h w c -> c h w")

            # Restore template
            self.restorer.face_template = template_backup

            if (
                self.debug_mode
                and len(self.rect_points_history["frame_idx"]) > 0
                and self.debug_level == 1
            ):
                self.plot_rect_points_debug()

            return face, box, affine_matrix

        except Exception as e:
            logger.error(f"Affine transform error: {str(e)}")
            raise RuntimeError(f"Affine transformation failed: {str(e)}")

    def prepare_partial_masks_and_masked_images(
        self,
        images: Union[torch.Tensor, np.ndarray],
        K: int,
        affine_transform: bool = False,
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Prepare masks and masked images with partial masking.

        Args:
            images: Batch of input images
            K: Number of unmasked images
            affine_transform: Whether to apply affine transformation

        Returns:
            Tuple containing:
            - Original image tensors
            - Masked image tensors
            - Mask tensors
        """
        # Convert input to tensor if needed
        if isinstance(images, np.ndarray):
            images = torch.from_numpy(images)
        if images.shape[3] == 3:
            images = rearrange(images, "b h w c -> b c h w")

        # Split images
        unmasked_images = images[:K]
        masked_images = images[K:]

        # Process unmasked images
        unmasked_results = []
        for image in unmasked_images:
            if affine_transform:
                image, _, _ = self.affine_transform(image)
            else:
                image = self.resize(image)
            pixel_values = self.normalize(image / 255.0)
            identity_mask = torch.ones((1, self.resolution, self.resolution))
            unmasked_results.append((pixel_values, pixel_values, identity_mask))

        # Process masked images
        if self.mask_type == MaskType.FIXED:
            masked_results = [
                self._preprocess_fixed_mask_image(image, affine_transform)
                for image in masked_images
            ]
        else:
            masked_results = [
                self._preprocess_one_masked_image(image) for image in masked_images
            ]

        # Combine results
        results = unmasked_results + masked_results
        pixel_values_list, masked_pixel_values_list, masks_list = zip(*results)

        return (
            torch.stack(pixel_values_list),
            torch.stack(masked_pixel_values_list),
            torch.stack(masks_list),
        )

    def prepare_masks_and_masked_images(
        self, images: Union[torch.Tensor, np.ndarray], affine_transform: bool = False
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Prepare masks and masked images for all images.

        Args:
            images: Batch of input images
            affine_transform: Whether to apply affine transformation

        Returns:
            Tuple containing:
            - Original image tensors
            - Masked image tensors
            - Mask tensors
        """
        if isinstance(images, np.ndarray):
            images = torch.from_numpy(images)
        if images.shape[3] == 3:
            images = rearrange(images, "b h w c -> b c h w")
        if self.mask_type == MaskType.FIXED:
            results = [
                self._preprocess_fixed_mask_image(image, affine_transform)
                for image in images
            ]
        else:
            results = [self._preprocess_one_masked_image(image) for image in images]
        pixel_values_list, masked_pixel_values_list, masks_list = zip(*results)

        # if self.debug_mode and len(self.rect_points_history['frame_idx']) > 0:
        #     self.plot_rect_points_debug()

        return (
            torch.stack(pixel_values_list),
            torch.stack(masked_pixel_values_list),
            torch.stack(masks_list),
        )

    def process_images(self, images: Union[torch.Tensor, np.ndarray]) -> torch.Tensor:
        """
        Process images without masking.

        Args:
            images: Batch of input images

        Returns:
            torch.Tensor: Processed image tensors
        """
        if isinstance(images, np.ndarray):
            images = torch.from_numpy(images)
        if images.shape[3] == 3:
            images = rearrange(images, "f h w c -> f c h w")
        images = self.resize(images)
        return self.normalize(images / 255.0)

    def close(self):
        """Release resources used by face detectors."""
        if self.face_detector == FaceDetector.MEDIAPIPE:
            if hasattr(self, "face_mesh") and self.face_mesh is not None:
                self.face_mesh.close()

    def plot_rect_points_debug(self, save_path: str = "debug_plots"):
        """
        Generate enhanced debug plots for rectangle points trajectories.

        Args:
            save_path: Directory to save debug plots
        """
        import matplotlib.pyplot as plt
        from matplotlib.patches import Ellipse

        os.makedirs(save_path, exist_ok=True)

        points_to_plot = ["top_left", "top_right", "nose_mid"]
        coordinates = ["x", "y"]

        # Calculate metrics
        metrics = {}
        for point in points_to_plot:
            raw = np.array(self.rect_points_history["raw"][point])
            smooth = np.array(self.rect_points_history["smooth"][point])

            # Calculate smoothing metrics
            mse = np.mean((raw - smooth) ** 2)
            max_deviation = np.max(np.abs(raw - smooth))
            temporal_consistency = np.mean(np.abs(np.diff(smooth, axis=0)))

            metrics[point] = {
                "mse": mse,
                "max_deviation": max_deviation,
                "temporal_consistency": temporal_consistency,
            }

        # Enhanced trajectory plots
        for point in points_to_plot:
            plt.figure(figsize=(15, 12))

            for idx, coord in enumerate(coordinates):
                plt.subplot(3, 1, idx + 1)

                # Get coordinate data
                raw_coords = np.array(self.rect_points_history["raw"][point])
                smooth_coords = np.array(self.rect_points_history["smooth"][point])
                frames = self.rect_points_history["frame_idx"]

                # Calculate confidence intervals
                window_size = 5
                std_dev = np.array(
                    [
                        np.std(raw_coords[max(0, i - window_size) : i + 1], axis=0)
                        for i in range(len(raw_coords))
                    ]
                )

                # Plot with confidence intervals
                plt.fill_between(
                    frames,
                    smooth_coords[:, idx] - 2 * std_dev[:, idx],
                    smooth_coords[:, idx] + 2 * std_dev[:, idx],
                    color="blue",
                    alpha=0.2,
                    label="95% Confidence",
                )

                plt.plot(
                    frames,
                    raw_coords[:, idx],
                    "r-",
                    label=f"Raw {coord}",
                    alpha=0.5,
                    linewidth=2,
                )
                plt.plot(
                    frames,
                    smooth_coords[:, idx],
                    "b-",
                    label=f"Smoothed {coord}",
                    linewidth=1,
                )

                # Add velocity arrows periodically
                if len(frames) > 1:
                    velocities = np.diff(smooth_coords[:, idx])
                    for i in range(0, len(frames) - 1, 5):
                        plt.arrow(
                            frames[i],
                            smooth_coords[i, idx],
                            1,
                            velocities[i],
                            head_width=0.5,
                            head_length=0.3,
                            fc="g",
                            ec="g",
                            alpha=0.3,
                        )

                plt.title(f"{point} - {coord} coordinate over frames")
                plt.xlabel("Frame")
                plt.ylabel(f"{coord} position")
                plt.legend()
                plt.grid(True)

            # Add metrics subplot
            plt.subplot(3, 1, 3)
            metrics_text = f"Smoothing Metrics:\n"
            metrics_text += f"MSE: {metrics[point]['mse']:.4f}\n"
            metrics_text += f"Max Deviation: {metrics[point]['max_deviation']:.4f}\n"
            metrics_text += (
                f"Temporal Consistency: {metrics[point]['temporal_consistency']:.4f}"
            )
            plt.text(0.1, 0.5, metrics_text, fontsize=10, transform=plt.gca().transAxes)
            plt.axis("off")

            plt.tight_layout()
            plt.savefig(os.path.join(save_path, f"{point}_trajectory.png"))
            plt.close()

        # Enhanced combined visualization
        plt.figure(figsize=(15, 10))

        # Plot trajectories with motion prediction
        for point in points_to_plot:
            raw_points = np.array(self.rect_points_history["raw"][point])
            smooth_points = np.array(self.rect_points_history["smooth"][point])

            # Plot main trajectories
            plt.plot(
                raw_points[:, 0],
                raw_points[:, 1],
                "r.",
                label=f"{point} Raw",
                alpha=0.5,
                markersize=8,
            )
            plt.plot(
                smooth_points[:, 0],
                smooth_points[:, 1],
                "b-",
                label=f"{point} Smooth",
                linewidth=1,
            )

            # Add uncertainty ellipses at key points
            for i in range(0, len(smooth_points), 5):
                if i > 0:
                    # Calculate local covariance
                    local_points = smooth_points[max(0, i - 5) : i + 1]
                    cov = np.cov(local_points.T)
                    eigenvals, eigenvecs = np.linalg.eigh(cov)
                    angle = np.degrees(np.arctan2(eigenvecs[1, 0], eigenvecs[0, 0]))

                    # Create uncertainty ellipse
                    ellip = Ellipse(
                        xy=smooth_points[i],
                        width=2 * np.sqrt(eigenvals[0]),
                        height=2 * np.sqrt(eigenvals[1]),
                        angle=angle,
                        alpha=0.1,
                        color="gray",
                    )
                    plt.gca().add_patch(ellip)

        plt.title("Combined Points Trajectory with Uncertainty")
        plt.xlabel("X position")
        plt.ylabel("Y position")
        plt.legend()
        plt.grid(True)

        # Add overall metrics
        metrics_text = "Overall Metrics:\n"
        for point in points_to_plot:
            metrics_text += f"\n{point}:\n"
            metrics_text += f"MSE: {metrics[point]['mse']:.4f}\n"
            metrics_text += (
                f"Temporal Consistency: {metrics[point]['temporal_consistency']:.4f}"
            )

        plt.text(
            1.02,
            0.5,
            metrics_text,
            transform=plt.gca().transAxes,
            bbox=dict(facecolor="white", alpha=0.8),
        )

        plt.tight_layout()
        plt.savefig(
            os.path.join(save_path, "combined_trajectory.png"),
            bbox_inches="tight",
            dpi=300,
        )
        plt.close()


def mediapipe_lm478_to_face_alignment_lm68(
    lm478: np.ndarray, return_2d: bool = True
) -> np.ndarray:
    """
    Convert MediaPipe 478 landmarks to Face Alignment 68 landmarks format.

    Args:
        lm478: MediaPipe landmarks array of shape [478, 2] or [478, 3]
        return_2d: Whether to return 2D coordinates only

    Returns:
        np.ndarray: Face alignment landmarks array of shape [68, 2]
    """
    landmarks_extracted = [(lm478[idx][0], lm478[idx][1]) for idx in landmark_points_68]
    return np.array(landmarks_extracted)


# Mapping from MediaPipe 478 landmarks to Face Alignment 68 landmarks
# landmark_points_68 = [
#     162, 234, 93,  58,  172, 136, 149, 148, 152, 377,  # Jaw line (0-9)
#     378, 365, 397, 288, 323, 454, 389, 71,  63,  105,  # Jaw line (10-19)
#     66,  107, 336, 296, 334, 293, 301, 168, 197, 5,    # Nose (20-29)
#     4,   75,  97,  2,   326, 305, 33,  160, 158, 133,  # Nose (30-39)
#     153, 144, 362, 385, 387, 263, 373, 380, 61,  39,   # Right eye (40-49)
#     37,  0,   267, 269, 291, 405, 314, 17,  84,  181,  # Left eye (50-59)
#     78,  82,  13,  312, 308, 317, 14,  87              # Mouth (60-67)
# ]

# right_eye_landmarks = [
#     153, 144, 362, 385, 387, 263, 373, 380, 61,  39,   # Right eye (40-49)
# ]

# left_eye_landmarks = [
#     37,  0,   267, 269, 291, 405, 314, 17,  84,  181,  # Left eye (50-59)
# ]

# # Landmarks for jaw line
# jaw_landmarks = [
#     162, 234, 93,  58,  172, 136, 149, 148, 152, 377,  # Jaw line (0-9)
#     378, 365, 397, 288, 323, 454, 389, 71,  63,  105   # Jaw line (10-19)
# ]

# # Landmarks surrounding the mouth region
# mouth_surround_landmarks = [
#     164, 165, 167, 92,  186, 57,  43,  106, 182, 83,
#     18,  313, 406, 335, 273, 287, 410, 322, 391, 393
# ]

# # Landmarks surrounding the face region
# face_surround_landmarks = [
#     152, 377, 400, 378, 379, 365, 397, 288, 435, 433,  # Right side
#     411, 425, 423, 327, 326, 94,  97,  98,  203, 205,  # Chin and left cheek
#     187, 213, 215, 58,  172, 136, 150, 149, 176, 148   # Left side and forehead
# ]

# Landmarks surrounding the mouth region
mouth_surround_landmarks = [
    164,
    165,
    167,
    92,
    186,
    57,
    43,
    106,
    182,
    83,
    18,
    313,
    406,
    335,
    273,
    287,
    410,
    322,
    391,
    393,
]

# Landmarks surrounding the face region
face_surround_landmarks = [
    152,
    377,
    400,
    378,
    379,
    365,
    397,
    288,
    435,
    433,  # Right side
    411,
    425,
    423,
    327,
    326,
    94,
    97,
    98,
    203,
    205,  # Chin and left cheek
    187,
    213,
    215,
    58,
    172,
    136,
    150,
    149,
    176,
    148,  # Left side and forehead
]

# Mapping from MediaPipe 478 landmarks to Face Alignment 68 landmarks
landmark_points_68 = [
    162,
    234,
    93,
    58,
    172,
    136,
    149,
    148,
    152,
    377,  # Jaw line (0-9)
    378,
    365,
    397,
    288,
    323,
    454,
    389,
    71,
    63,
    105,  # Jaw line (10-19)
    66,
    107,
    336,
    296,
    334,
    293,
    301,
    168,
    197,
    5,  # Nose (20-29)
    4,
    75,
    97,
    2,
    326,
    305,
    33,
    160,
    158,
    133,  # Nose (30-39)
    153,
    144,
    362,
    385,
    387,
    263,
    373,
    380,
    61,
    39,  # Right eye (40-49)
    37,
    0,
    267,
    269,
    291,
    405,
    314,
    17,
    84,
    181,  # Left eye (50-59)
    78,
    82,
    13,
    312,
    308,
    317,
    14,
    87,  # Mouth (60-67)
]

# Eye landmarks
left_eye_idx = [
    362,
    382,
    381,
    380,
    374,
    373,
    390,
    249,
    263,
    466,
    388,
    387,
    386,
    385,
    384,
    398,
]
left_iris_idx = [473, 474, 475, 476, 477]

right_eye_idx = [
    33,
    7,
    163,
    144,
    145,
    153,
    154,
    155,
    133,
    173,
    157,
    158,
    159,
    160,
    161,
    246,
]
right_iris_idx = [468, 469, 470, 471, 472]

# Nose landmarks
nose_outline_idx = [
    6,
    122,
    351,
    412,
    188,
    114,
    343,
    217,
    437,
    198,
    420,
    429,
    209,
    279,
    49,
    129,
    358,
    327,
    326,
    2,
    97,
    98,
]
nose_idx = [
    6,
    122,
    351,
    412,
    188,
    114,
    343,
    217,
    437,
    198,
    420,
    429,
    209,
    279,
    49,
    129,
    358,
    327,
    326,
    2,
    97,
    98,
    197,
    195,
    5,
    4,
    1,
    19,
    94,
    196,
    174,
    419,
    399,
    3,
    236,
    248,
    456,
    51,
    134,
    281,
    363,
    45,
    220,
    115,
    275,
    440,
    344,
    278,
    48,
    102,
    331,
    294,
    64,
    240,
    460,
    99,
    326,
    290,
    60,
    75,
    305,
    235,
    455,
    219,
    439,
    344,
    115,
    440,
    220,
    45,
    275,
    44,
    274,
    457,
    237,
    438,
    218,
    79,
    309,
    250,
    20,
    242,
    462,
    141,
    370,
    125,
    354,
    461,
    241,
    274,
]

# Mouth landmarks
mouth_outline_idx = [
    0,
    267,
    269,
    270,
    409,
    291,
    375,
    321,
    405,
    314,
    17,
    84,
    84,
    181,
    91,
    146,
    61,
    185,
    40,
    39,
    37,
]
mouth_idx = [
    0,
    267,
    269,
    270,
    409,
    291,
    375,
    321,
    405,
    314,
    17,
    84,
    84,
    181,
    91,
    146,
    61,
    185,
    40,
    39,
    37,
    11,
    302,
    304,
    408,
    306,
    307,
    320,
    404,
    315,
    16,
    85,
    180,
    90,
    77,
    76,
    184,
    74,
    73,
    72,
    12,
    268,
    271,
    272,
    407,
    292,
    325,
    319,
    403,
    316,
    15,
    86,
    179,
    89,
    96,
    62,
    78,
    191,
    80,
    81,
    82,
    13,
    312,
    311,
    310,
    415,
    308,
    324,
    318,
    402,
    317,
    14,
    87,
    178,
    88,
    95,
]

# Jaw landmarks
jaw_line_idx = [
    93,
    323,
    361,
    288,
    397,
    365,
    379,
    378,
    400,
    377,
    152,
    148,
    176,
    149,
    150,
    136,
    172,
    58,
    132,
]
jaw_line_ext_idx = [
    93,
    323,
    361,
    288,
    397,
    365,
    379,
    378,
    400,
    377,
    152,
    148,
    176,
    149,
    150,
    136,
    172,
    58,
    132,
    123,
    50,
    36,
    49,
    131,
    360,
    279,
    266,
    280,
    352,
]
