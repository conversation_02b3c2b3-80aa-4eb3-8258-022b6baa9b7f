import multiprocessing as mp
import os
import time
from concurrent.futures import Process<PERSON>ool<PERSON>xecutor
from typing import Dict, Optional
from tqdm import tqdm

import torch

# from src.models.faceDetector.s3fd import S3FD
from threeddfa.FaceBoxes import FaceBoxes
from src.utils.logger import configure_logger
from src.utils.video_utils import (
    check_video_corruption,
    collect_video_paths_recursively,
    load_video_with_decord,
)

logger = configure_logger(__name__)


class SingleFaceFilter:
    """Filter videos containing exactly one face using S3FD face detector."""

    def __init__(self, device: str = None):
        """
        Initialize the face detector.

        Args:
            device (str, optional): Device to run face detection on. If None, will auto-select.
        """
        # Set up device management
        if device is None:
            if torch.cuda.is_available():
                # Get least used GPU
                device_id = torch.cuda.current_device()
                device = f"cuda:{device_id}"
                torch.cuda.empty_cache()
            else:
                device = "cpu"

        self.device = device
        logger.info(f"Initializing face detector on device: {device}")
        # self.face_detector = S3FD(device)
        self.face_detector = FaceBoxes(timer_flag=False, device=device)
        logger.info("Face detector initialized successfully")

    def __del__(self):
        """Cleanup when object is destroyed"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

    def process_video(
        self,
        video_path: str,
        confidence_threshold: float = 0.9,
        sample_rate: int = 1,
        ratio: float = 0.8,
        batch_size: int = 32,
    ) -> bool:
        """
        Process a video to check if it contains exactly one face.

        Args:
            video_path (str): Path to the video file
            confidence_threshold (float): Minimum confidence for face detection
            sample_rate (int): Sample one frame every N frames
            ratio (float): Ratio of frames with single face required for filtering
            batch_size (int): Number of frames to process in a single batch

        Returns:
            bool: True if video contains exactly one face in the specified ratio, False otherwise
        """
        try:
            # Check if video is corrupted
            if check_video_corruption(video_path):
                logger.error(f"Corrupted video: {video_path}")
                return False

            # Load video frames
            video_frames = load_video_with_decord(video_path)
            if len(video_frames) == 0:
                logger.error(f"No frames loaded from video: {video_path}")
                return False

            # Sample frames at given rate
            sampled_frames = video_frames[::sample_rate]

            # Process frames in batches to manage memory
            face_counts = []

            # for i in range(0, len(sampled_frames), batch_size):
            #     batch_frames = sampled_frames[i:i + batch_size]

            try:
                # Process batch at once instead of frame-by-frame for better efficiency
                for frame in tqdm(sampled_frames, total=len(sampled_frames)):
                    detections = self.face_detector(frame)
                    face_count = len(detections)
                    face_counts.append(face_count)

                    # Early exit if multiple faces found and ratio is 1.0
                    if ratio == 1.0 and face_count != 1:
                        logger.info(
                            f"Multiple faces ({face_count}) found in frame, skipping video"
                        )
                        return False

            except Exception as e:
                logger.warning(f"Error processing {video_path}: {str(e)}")

            finally:
                # Ensure CUDA memory is cleared after each batch
                if torch.cuda.is_available() and self.device.startswith("cuda"):
                    torch.cuda.empty_cache()

            # Analyze face counts
            if not face_counts:
                logger.warning(f"No valid frames processed in video: {video_path}")
                return False

            # Check if enough frames have exactly one face
            one_face_frames = sum(count == 1 for count in face_counts)
            one_face_ratio = one_face_frames / len(face_counts)

            # Require at least the specified ratio of frames to have exactly one face
            has_single_face = one_face_ratio >= ratio
            logger.info(
                f"Video {video_path}: {one_face_ratio:.2%} frames have single face ({one_face_frames}/{len(face_counts)})"
            )

            return has_single_face

        except Exception as e:
            logger.exception(f"Error processing video {video_path}: {e}")
            return False


# Worker for processing videos with dedicated GPU
def gpu_worker_process(
    gpu_id: int,
    task_queue: mp.Queue,
    result_queue: mp.Queue,
    confidence_threshold: float,
    sample_rate: int,
    ratio: float,
    batch_size: int,
):
    """Worker process dedicated to a specific GPU"""
    try:
        device = f"cuda:{gpu_id}"
        logger.info(f"GPU worker {gpu_id} starting on device {device}")

        # Initialize detector only once per process
        face_filter = SingleFaceFilter(device=device)

        while True:
            try:
                # Get next task or exit if None
                task = task_queue.get()
                if task is None:
                    logger.info(f"GPU worker {gpu_id} received exit signal")
                    break

                video_path, video_idx = task

                # Process video
                start_time = time.time()
                has_single_face = face_filter.process_video(
                    video_path, confidence_threshold, sample_rate, ratio, batch_size
                )
                elapsed = time.time() - start_time

                # Send result back
                result_queue.put((video_path, has_single_face, video_idx))
                logger.info(f"GPU {gpu_id} processed {video_path} in {elapsed:.2f}s")

            except Exception as e:
                logger.error(f"GPU worker {gpu_id} error processing video: {str(e)}")
                # Return failure result instead of crashing
                if "video_path" in locals() and "video_idx" in locals():
                    result_queue.put((video_path, False, video_idx))

    except Exception as e:
        logger.exception(f"GPU worker {gpu_id} fatal error: {str(e)}")
    finally:
        # Clean up resources
        logger.info(f"GPU worker {gpu_id} shutting down")
        if torch.cuda.is_available():
            torch.cuda.empty_cache()


# Worker for CPU processing
def cpu_worker_process(video_args):
    """Worker function to process a single video on CPU"""
    video_path, video_idx, confidence_threshold, sample_rate, ratio, batch_size = (
        video_args
    )
    try:
        # Initialize detector in worker
        face_filter = SingleFaceFilter(device="cpu")
        start_time = time.time()
        has_single_face = face_filter.process_video(
            video_path, confidence_threshold, sample_rate, ratio, batch_size
        )
        elapsed = time.time() - start_time
        logger.info(f"CPU processed {video_path} in {elapsed:.2f}s")
        return (video_path, has_single_face, video_idx)
    except Exception as e:
        logger.error(f"CPU worker error processing {video_path}: {str(e)}")
        return (video_path, False, video_idx)


def process_sf_videos_parallel(
    input_dir: str,
    output_dir: Optional[str] = None,
    delete_multi_face: bool = False,
    dry_run: bool = False,
    num_workers: Optional[int] = None,
    confidence_threshold: float = 0.9,
    sample_rate: int = 1,
    ratio: float = 0.8,
    batch_size: int = 32,
) -> Dict[str, bool]:
    """
    Process multiple videos in parallel to filter ones with single face.

    Args:
        input_dir (str): Directory containing input videos
        output_dir (Optional[str]): Directory to copy filtered videos to
        delete_multi_face (bool): If True, delete videos without single face
        dry_run (bool): If True, only log actions without deleting
        num_workers (Optional[int]): Number of worker processes
        confidence_threshold (float): Minimum confidence for face detection
        sample_rate (int): Sample one frame every N frames
        ratio (float): Ratio of frames with single face required for filtering
        batch_size (int): Number of frames to process in a single batch

    Returns:
        Dict[str, bool]: Dictionary mapping video paths to their single-face status
    """
    if not os.path.isdir(input_dir):
        raise FileNotFoundError(f"Input directory not found: {input_dir}")

    # Create output directory if specified
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)

    # Get all video paths
    video_paths = collect_video_paths_recursively(input_dir)
    if not video_paths:
        logger.warning(f"No videos found in {input_dir}")
        return {}

    total_videos = len(video_paths)
    results = {}

    # Decide processing strategy based on available resources
    gpu_count = torch.cuda.device_count() if torch.cuda.is_available() else 0
    logger.info(f"Found {gpu_count} CUDA devices")

    if gpu_count > 1:
        # Multi-GPU processing approach
        logger.info(f"Using multi-GPU approach with {gpu_count} GPUs")

        # Create task and result queues
        task_queue = mp.Queue()
        result_queue = mp.Queue()

        # Fill task queue with work
        for i, video_path in enumerate(video_paths):
            task_queue.put((video_path, i))

        # Add termination signals
        for _ in range(gpu_count):
            task_queue.put(None)

        # Start GPU worker processes
        processes = []
        for gpu_id in range(gpu_count):
            p = mp.Process(
                target=gpu_worker_process,
                args=(
                    gpu_id,
                    task_queue,
                    result_queue,
                    confidence_threshold,
                    sample_rate,
                    ratio,
                    batch_size,
                ),
            )
            p.start()
            processes.append(p)

        # Collect results
        completed = 0
        result_map = {}

        while completed < total_videos:
            try:
                video_path, has_single_face, video_idx = result_queue.get(
                    timeout=300
                )  # 5 minute timeout
                result_map[video_path] = has_single_face
                completed += 1

                # Handle output directory if specified
                if output_dir and has_single_face:
                    rel_path = os.path.relpath(video_path, input_dir)
                    out_path = os.path.join(output_dir, rel_path)
                    os.makedirs(os.path.dirname(out_path), exist_ok=True)
                    os.symlink(os.path.abspath(video_path), out_path)

                # Display progress
                if completed % 10 == 0 or completed == total_videos:
                    logger.info(
                        f"Progress: {completed}/{total_videos} videos processed"
                    )

            except Exception as e:
                logger.error(f"Error collecting results: {str(e)}")
                break

        # Wait for processes to finish
        for p in processes:
            p.join(timeout=10)
            if p.is_alive():
                p.terminate()

        results = result_map

    elif gpu_count == 1:
        # Single GPU approach - process serially on the GPU
        logger.info("Using single GPU approach")

        face_filter = SingleFaceFilter(device="cuda:0")

        for i, video_path in enumerate(video_paths):
            try:
                has_single_face = face_filter.process_video(
                    video_path, confidence_threshold, sample_rate, ratio, batch_size
                )
                results[video_path] = has_single_face

                # Handle output directory if specified
                if output_dir and has_single_face:
                    rel_path = os.path.relpath(video_path, input_dir)
                    out_path = os.path.join(output_dir, rel_path)
                    os.makedirs(os.path.dirname(out_path), exist_ok=True)
                    os.symlink(os.path.abspath(video_path), out_path)

                # Display progress
                if (i + 1) % 10 == 0 or (i + 1) == total_videos:
                    logger.info(f"Progress: {i + 1}/{total_videos} videos processed")

            except Exception as e:
                logger.exception(f"Error processing video {video_path}: {e}")
                results[video_path] = False

            # Make sure to clean up CUDA memory after each video
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

    else:
        # CPU-only approach
        if num_workers is None:
            num_workers = max(1, os.cpu_count() // 2)

        logger.info(f"Using CPU-only approach with {num_workers} workers")

        # Prepare arguments for workers
        worker_args = [
            (path, i, confidence_threshold, sample_rate, ratio, batch_size)
            for i, path in enumerate(video_paths)
        ]

        # Use process pool for CPU processing
        with ProcessPoolExecutor(max_workers=num_workers) as executor:
            futures = [
                executor.submit(cpu_worker_process, args) for args in worker_args
            ]

            # Collect results
            for i, future in enumerate(futures):
                try:
                    video_path, has_single_face, _ = future.result(
                        timeout=300
                    )  # 5 minute timeout
                    results[video_path] = has_single_face

                    # Handle output directory if specified
                    if output_dir and has_single_face:
                        rel_path = os.path.relpath(video_path, input_dir)
                        out_path = os.path.join(output_dir, rel_path)
                        os.makedirs(os.path.dirname(out_path), exist_ok=True)
                        os.symlink(os.path.abspath(video_path), out_path)

                    # Display progress
                    if (i + 1) % 10 == 0 or (i + 1) == total_videos:
                        logger.info(
                            f"Progress: {i + 1}/{total_videos} videos processed"
                        )

                except Exception as e:
                    logger.error(f"Error processing future: {str(e)}")
                    if hasattr(future, "video_path"):
                        results[future.video_path] = False

    # Handle deletion of multi-face videos if requested
    if delete_multi_face:
        multi_face_videos = [
            path for path, has_single in results.items() if not has_single
        ]
        if multi_face_videos:
            if dry_run:
                logger.info(
                    "DRY RUN: Would delete these videos with multiple/no faces:"
                )
                for video_path in multi_face_videos:
                    logger.info(f"  - {video_path}")
            else:
                logger.info(
                    f"Deleting {len(multi_face_videos)} videos with multiple/no faces..."
                )
                deleted_count = 0
                failed_count = 0

                for video_path in multi_face_videos:
                    try:
                        os.remove(video_path)
                        deleted_count += 1
                        logger.info(f"Deleted: {video_path}")
                    except OSError as e:
                        failed_count += 1
                        logger.error(f"Failed to delete {video_path}: {e}")

                logger.info(
                    f"Deletion complete: {deleted_count} deleted, {failed_count} failed"
                )

    # Log summary
    filtered_count = sum(1 for v in results.values() if v)
    logger.info(f"Found {filtered_count}/{len(video_paths)} videos with single face")

    return results


def main():
    """CLI entry point"""
    import argparse

    parser = argparse.ArgumentParser(description="Filter videos containing single face")
    parser.add_argument("input_dir", help="Input directory containing videos")
    parser.add_argument("--output-dir", help="Output directory for filtered videos")
    parser.add_argument(
        "--delete-multi",
        action="store_true",
        help="Delete videos with multiple/no faces",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be deleted without actually deleting",
    )
    parser.add_argument("--workers", type=int, help="Number of worker processes")
    parser.add_argument(
        "--confidence",
        type=float,
        default=0.9,
        help="Face detection confidence threshold",
    )
    parser.add_argument(
        "--sample-rate", type=int, default=5, help="Sample one frame every N frames"
    )
    parser.add_argument(
        "--ratio",
        type=float,
        default=0.8,
        help="Ratio of frames that must have single face",
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=32,
        help="Number of frames to process in one batch",
    )

    args = parser.parse_args()

    try:
        results = process_sf_videos_parallel(
            input_dir=args.input_dir,
            output_dir=args.output_dir,
            delete_multi_face=args.delete_multi,
            dry_run=args.dry_run,
            num_workers=args.workers,
            confidence_threshold=args.confidence,
            sample_rate=args.sample_rate,
            ratio=args.ratio,
            batch_size=args.batch_size,
        )

        # Print results summary
        print("\nResults Summary:")
        print(f"Total videos processed: {len(results)}")
        print(f"Videos with single face: {sum(1 for v in results.values() if v)}")
        print(
            f"Videos with multiple/no faces: {sum(1 for v in results.values() if not v)}"
        )

    except Exception as e:
        logger.exception(f"Error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    # Set multiprocessing start method
    if hasattr(mp, "set_start_method"):
        mp.set_start_method("spawn", force=True)
    exit(main())
