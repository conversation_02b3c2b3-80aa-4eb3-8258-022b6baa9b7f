import multiprocessing
import os
import os.path
import shutil
import subprocess
import sys
from dataclasses import dataclass
from typing import Dict, List

import numpy as np
from omegaconf import OmegaConf

from src.utils.logger import configure_logger

# Set multiprocessing start method before any other imports
multiprocessing.set_start_method("spawn", force=True)

logger = configure_logger(__name__)

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.append(project_root)

from src.models.talknet.talknet_wrapper import get_video_scores
from src.utils.audio_utils import extract_audio, speaker_count


@dataclass
class TimeSegment:
    start: float  # Start time in seconds
    end: float  # End time in seconds


def load_config() -> Dict:
    """Load configuration from paths.yaml"""
    config_path = os.path.join(project_root, "src/configs", "paths.yaml")
    return OmegaConf.load(config_path)


def find_continuous_positive_segments(
    scores: np.ndarray, fps: float = 25.0
) -> List[TimeSegment]:
    """
    Find continuous segments with positive scores.

    Args:
        scores (np.ndarray): Array of scores for a track
        fps (float): Frames per second of the video

    Returns:
        List[TimeSegment]: List of segments with continuous positive scores
    """
    segments = []
    start_idx = None

    for i, score in enumerate(scores):
        if score > 0:
            if start_idx is None:
                start_idx = i
        elif start_idx is not None:
            segments.append(TimeSegment(start=start_idx / fps, end=i / fps))
            start_idx = None

    # Handle case where positive segment extends to end
    if start_idx is not None:
        segments.append(TimeSegment(start=start_idx / fps, end=len(scores) / fps))

    return segments


def merge_overlapping_segments(segments: List[TimeSegment]) -> List[TimeSegment]:
    """
    Merge overlapping time segments.

    Args:
        segments (List[TimeSegment]): List of time segments

    Returns:
        List[TimeSegment]: Merged segments
    """
    if not segments:
        return []

    # Sort segments by start time
    sorted_segments = sorted(segments, key=lambda x: x.start)
    merged = [sorted_segments[0]]

    for current in sorted_segments[1:]:
        previous = merged[-1]
        if current.start <= previous.end:
            # Segments overlap, merge them
            previous.end = max(previous.end, current.end)
        else:
            # No overlap, add new segment
            merged.append(current)

    return merged


def extract_segment(video_path: str, segment: TimeSegment, output_path: str) -> None:
    """
    Extract a segment from the video using FFmpeg with re-encoding for better compatibility.

    Args:
        video_path (str): Path to input video
        segment (TimeSegment): Time segment to extract
        output_path (str): Path to save extracted segment

    Raises:
        RuntimeError: If FFmpeg operation fails
        ValueError: If input parameters are invalid
    """
    # Input validation
    if not os.path.exists(video_path):
        raise ValueError(f"Input video not found: {video_path}")
    if segment.start < 0 or segment.end <= segment.start:
        raise ValueError(
            f"Invalid segment times: start={segment.start}, end={segment.end}"
        )

    duration = segment.end - segment.start

    # FFmpeg command with re-encoding for better compatibility
    command = [
        "ffmpeg",
        "-hide_banner",
        "-loglevel",
        "error",  # Only show errors
        "-i",
        video_path,
        "-ss",
        str(segment.start),
        "-t",
        str(duration),
        # Video settings
        "-c:v",
        "libx264",  # Re-encode video with H.264
        "-preset",
        "medium",  # Balance speed/quality
        "-crf",
        "18",  # Constant Rate Factor (quality level)
        "-vf",
        "fps=25",  # Force constant framerate
        # Audio settings
        "-c:a",
        "aac",  # Re-encode audio with AAC
        "-y",  # Overwrite output file if exists
        output_path,
    ]

    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        logger.debug(f"Successfully extracted segment to {output_path}")
    except subprocess.CalledProcessError as e:
        error_msg = e.stderr if e.stderr else str(e)
        logger.error(f"FFmpeg extraction failed: {error_msg}")
        raise RuntimeError(
            f"FFmpeg failed to extract segment:\n"
            f"Command: {' '.join(command)}\n"
            f"Error: {error_msg}"
        ) from e


def segment_video_by_speech(
    video_path: str, output_dir: str = None, debug: bool = False, cleanup: bool = False
) -> List[str]:
    """
    Process video based on speaker count:
    - For single speaker: Keep video if positive scores > negative scores
    - For multiple speakers: Segment video into parts where any speaker is talking

    Args:
        video_path (str): Path to the input video
        debug (bool): If True, keep TalkNet temporary files for debugging. Defaults to False.
        cleanup (bool): If True, remove output segments after returning paths. Defaults to False.
                       Useful when you want to process segments and clean up afterward.

    Returns:
        List[str]: For single speaker - either [video_path] or [] based on scores
                  For multiple speakers - paths to the created segment files
    """
    # Load configuration
    if output_dir is None:
        config = load_config()
        output_dir = os.path.join(project_root, config.processing.output_dir)

    os.makedirs(output_dir, exist_ok=True)

    # Extract audio for speaker counting
    temp_audio_path = os.path.join(
        output_dir, f"{os.path.splitext(os.path.basename(video_path))[0]}_temp.wav"
    )
    try:
        extract_audio(video_path, temp_audio_path)
        num_speakers = speaker_count(temp_audio_path)
        logger.info(f"Detected {num_speakers} speaker(s) in video")

        if num_speakers == 1:
            # Single speaker case - evaluate overall positive vs negative scores
            scores = get_video_scores(video_path, debug=debug)
            total_positive = sum(score > 0 for track in scores for score in track)
            total_negative = sum(score <= 0 for track in scores for score in track)

            logger.info(
                f"Single speaker analysis - Positive frames: {total_positive}, Negative frames: {total_negative}"
            )

            if total_positive > total_negative:
                # Copy accepted video to output directory
                video_name = os.path.basename(video_path)
                output_video_path = os.path.join(output_dir, video_name)
                if (
                    video_path != output_video_path
                ):  # Only copy if not already in output dir
                    try:
                        shutil.copy2(video_path, output_video_path)
                        logger.info(
                            "Single speaker with majority positive scores - video copied to output directory"
                        )
                    except IOError as e:
                        logger.error(f"Error copying video to output directory: {e}")
                        raise
                return [output_video_path]  # Return path to copied video
            else:
                logger.info(
                    "Single speaker with majority negative scores - rejecting video"
                )
                return []  # Reject video

        # Multiple speakers case - continue with existing segmentation logic
        logger.info("Multiple speakers detected - proceeding with segmentation")
        scores = get_video_scores(video_path, debug=debug)

    except Exception as e:
        logger.error(f"Error in speaker analysis: {e}")
        raise
    finally:
        # Clean up temporary audio file
        try:
            if os.path.exists(temp_audio_path):
                os.remove(temp_audio_path)
        except Exception as e:
            logger.warning(f"Failed to remove temporary audio file: {e}")

    # Find positive segments for each track
    all_segments = []
    for track_scores in scores:
        segments = find_continuous_positive_segments(track_scores)
        all_segments.extend(segments)

    # Merge overlapping segments
    merged_segments = merge_overlapping_segments(all_segments)

    # Extract and save segments
    segment_paths = []
    video_name = os.path.splitext(os.path.basename(video_path))[0]
    for i, segment in enumerate(merged_segments):
        output_path = os.path.join(output_dir, f"{video_name}_segment_{i:03d}.mp4")
        extract_segment(video_path, segment, output_path)
        segment_paths.append(output_path)

    # Clean up segments if requested
    if cleanup:
        logger.info(f"Cleaning up {len(segment_paths)} output segments...")
        for path in segment_paths:
            try:
                os.remove(path)
                logger.debug(f"Removed segment: {path}")
            except OSError as e:
                logger.warning(f"Could not remove segment {path}: {e}")

    return segment_paths


def process_video(video_path, debug=False):
    """
    Process a video and print its active speaker detection scores.

    Args:
        video_path (str): Path to the video file
        debug (bool): If True, keep temporary files for debugging. Defaults to False.
    """
    logger.info(f"Processing video: {os.path.basename(video_path)}")
    scores = get_video_scores(video_path, debug=debug)

    logger.info(f"Found {len(scores)} tracks with scores:")
    for i, track_scores in enumerate(scores):
        logger.info(f"Track {i}: {len(track_scores)} frame scores")

    if not debug:
        logger.info("All temporary files have been removed")
    else:
        logger.info(
            f"Temporary files kept for debugging in {os.path.basename(video_path)}_talknet directory"
        )

    return scores


def process_videos_parallel(
    input_dir: str = None,
    output_dir: str = None,
    debug: bool = False,
    cleanup: bool = False,
) -> Dict[str, List[str]]:
    """
    Process all videos in the configured input directory in parallel, segmenting each by speech.
    Uses available GPUs for parallel processing, with one video per GPU.

    Args:
        debug (bool): If True, keep temporary files. Defaults to False.
        cleanup (bool): If True, remove output segments after processing. Defaults to False.

    Returns:
        Dict[str, List[str]]: Dictionary mapping video paths to their segment paths

    Example:
        >>> results = process_videos_parallel(debug=False)
        >>> for video_path, segments in results.items():
        ...     print(f"Processed {os.path.basename(video_path)}: {len(segments)} segments")
    """
    import glob
    from concurrent.futures import ProcessPoolExecutor, as_completed
    from functools import partial

    import torch

    # Get number of available GPUs
    num_gpus = torch.cuda.device_count()
    if num_gpus == 0:
        logger.error("No CUDA-capable GPUs found for parallel processing")
        raise RuntimeError("No CUDA-capable GPUs found")

    # Get input directory from config
    if input_dir is None:
        config = load_config()
        input_dir = os.path.join(project_root, config.talknet.demo_video_folder)

    os.makedirs(input_dir, exist_ok=True)

    # Get all video files
    video_extensions = [".mp4", ".avi", ".mov", ".mkv", ".wmv"]
    videos = []
    for ext in video_extensions:
        videos.extend(glob.glob(os.path.join(input_dir, f"*{ext}")))
    videos = sorted(videos)

    if not videos:
        logger.error(f"No videos found in directory: {input_dir}")
        raise ValueError(f"No videos found in {input_dir}")

    logger.info(f"Found {len(videos)} videos in {input_dir}")
    logger.info(f"Using {num_gpus} GPUs for parallel processing")

    # Create ProcessPoolExecutor with number of processes equal to number of GPUs
    results = {}
    with ProcessPoolExecutor(max_workers=num_gpus) as executor:
        # Process videos in parallel, assigning each to a GPU round-robin
        process_fn = partial(
            segment_video_by_speech, output_dir=output_dir, debug=debug, cleanup=cleanup
        )
        future_to_video = {
            executor.submit(process_fn, video_path): video_path for video_path in videos
        }

        # Get results as they complete
        for future in as_completed(future_to_video):
            video_path = future_to_video[future]
            try:
                results[video_path] = future.result()
            except Exception as e:
                logger.error(f"Failed to process {video_path}: {str(e)}")
                results[video_path] = []

    return results


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(
        description="Process videos for active speaker detection and segmentation"
    )
    parser.add_argument(
        "--parallel",
        action="store_true",
        help="Process all videos in parallel using available GPUs",
    )
    parser.add_argument(
        "--debug", action="store_true", help="Keep temporary files for debugging"
    )
    parser.add_argument(
        "--cleanup", action="store_true", help="Remove output segments after processing"
    )
    parser.add_argument(
        "--video",
        type=str,
        help="Process a single video instead of all videos in input directory",
    )
    args = parser.parse_args()

    try:
        if args.video:
            # Process single video
            if not os.path.exists(args.video):
                logger.error(f"Video file not found at {args.video}")
                sys.exit(1)

            logger.info(f"Processing single video: {args.video}")
            segments = segment_video_by_speech(
                args.video, debug=args.debug, cleanup=args.cleanup
            )
            if len(segments) == 1 and segments[0] == args.video:
                logger.info(
                    "Video accepted: Single speaker with majority positive scores"
                )
            elif len(segments) == 0:
                logger.info(
                    "Video rejected: Single speaker with majority negative scores"
                )
            else:
                logger.info(
                    f"Created {len(segments)} segments (multiple speakers detected):"
                )
                for path in segments:
                    logger.info(f"- {path}")
                if not args.cleanup:
                    logger.info("Output segments are preserved in the output directory")
                    logger.info("To clean up segments, use --cleanup flag")
        else:
            # Process all videos in parallel using configured input directory
            logger.info("Starting parallel video processing")
            results = process_videos_parallel(debug=args.debug, cleanup=args.cleanup)
            total_segments = 0

            for video_path, segments in results.items():
                logger.info(
                    f"Processed {os.path.basename(video_path)}: {len(segments)} segments"
                )
                total_segments += len(segments)
                if not args.cleanup:
                    for path in segments:
                        logger.debug(f"- {path}")

            logger.info(f"Total segments created: {total_segments}")
            if not args.cleanup:
                logger.info("Output segments are preserved in the output directory")
                logger.info("To clean up segments, use --cleanup flag")

    except Exception as e:
        logger.error(f"Error: {str(e)}", exc_info=True)
        sys.exit(1)
