"""
Shot detection module for video processing.

This module provides functionality to detect and split videos into shots/scenes
using the PySceneDetect library. It supports multiprocessing for efficient
batch processing of multiple videos.
"""

import os
import subprocess
import tqdm
from concurrent.futures import ProcessPoolExecutor
from src.utils.video_utils import collect_video_paths_recursively

paths = []


def prepare_paths(input_dir, output_dir):
    """
    Prepare video paths for shot detection processing.

    Args:
        input_dir (str): Directory containing input videos
        output_dir (str): Directory where shot videos will be saved

    Note:
        This function modifies the global 'paths' list by appending
        [video_input_path, output_directory] pairs for processing.
        Videos that already have output files are skipped.
    """
    global paths
    video_paths = collect_video_paths_recursively(input_dir)

    for video_input in video_paths:
        # Maintain same directory structure in output
        rel_path = os.path.relpath(video_input, input_dir)
        video_output = os.path.join(output_dir, rel_path)

        # Skip if output already exists
        if os.path.isfile(video_output):
            continue

        paths.append([video_input, os.path.dirname(video_output)])


def detect_shot(video_input, output_dir):
    """
    Detect shots in a video and split it into separate files.

    Args:
        video_input (str): Path to the input video file
        output_dir (str): Directory where shot videos will be saved

    Note:
        Uses PySceneDetect with adaptive threshold detection to identify
        scene changes. Minimum scene length is set to 3.0 seconds.
        Output files are named with pattern: {video_name}_shot_{scene_number}.mp4
    """
    os.makedirs(output_dir, exist_ok=True)
    video = os.path.basename(video_input)[:-4]
    command = f"scenedetect --quiet -i {video_input} --min-scene-len 3.0s --drop-short-scenes detect-adaptive --threshold 2 split-video --high-quality --filename '{video}_shot_$SCENE_NUMBER' --output {output_dir}"
    # command = f"scenedetect --quiet -i {video_input} detect-adaptive --threshold 2 split-video --high-quality --filename '{video}_shot_$SCENE_NUMBER' --output {output_dir}"
    subprocess.run(command, shell=True)


def multi_run_wrapper(args):
    """
    Wrapper function for multiprocessing shot detection.

    Args:
        args (list): Arguments to pass to detect_shot function

    Returns:
        Result of detect_shot function call
    """
    return detect_shot(*args)


def detect_shot_multiprocessing(input_dir, output_dir, num_workers):
    """
    Detect shots in multiple videos using multiprocessing.

    Args:
        input_dir (str): Directory containing input videos
        output_dir (str): Directory where shot videos will be saved
        num_workers (int): Number of worker processes to use

    Note:
        This function processes all videos in the input directory in parallel,
        splitting each video into shots based on scene detection.
    """
    print(f"Recursively gathering video paths of {input_dir} ...")
    prepare_paths(input_dir, output_dir)

    print(f"Detecting shot of {input_dir} ...")
    with ProcessPoolExecutor(max_workers=num_workers) as executor:
        futures = list(executor.map(multi_run_wrapper, paths))
        for _ in tqdm.tqdm(futures, total=len(paths)):
            pass


if __name__ == "__main__":
    input_dir = "assets/inputs"
    output_dir = "demo/shots"
    num_workers = 4

    detect_shot_multiprocessing(input_dir, output_dir, num_workers)
