from pydub import AudioSegment
from pydub.silence import detect_silence
import cv2
import numpy as np
import os
import sys
from logging import getLogger
from insightface.app import FaceAnalysis
import argparse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import Dict, List, Tuple, Optional
import logging

import logging

# Clear all handlers
for handler in logging.root.handlers[:]:
    logging.root.removeHandler(handler)

# Create logger explicitly
logger = logging.getLogger("track_logger")
logger.setLevel(logging.INFO)

# Create file handler
file_handler = logging.FileHandler("track.log")
file_handler.setLevel(logging.INFO)

# Set formatter
formatter = logging.Formatter("[Line: %(lineno)d]: %(message)s")
file_handler.setFormatter(formatter)

# Attach handler to logger
logger.addHandler(file_handler)

# Optional: also log to console
# console_handler = logging.StreamHandler()
# console_handler.setFormatter(formatter)
# logger.addHandler(console_handler)

# Test log
logger.info("Track Logger is working correctly.")


class FaceTracker:
    """
    Advanced face tracking system with silence detection and bbox filtering.
    """

    # Configuration constants
    IoU_THRESHOLD = 0.2
    SIMILARITY_THRESHOLD = 0.5
    MAX_INACTIVE_FRAMES = 1
    BBOX_SIZE_THRESHOLD = 0.5  # 50% of largest bbox
    SILENCE_MIN_LENGTH = 5000  # ms
    SILENCE_THRESHOLD = -50  # dB

    def __init__(self, device: str = "cuda"):
        """Initialize face tracker with InsightFace model."""
        self.app = FaceAnalysis(
            name="buffalo_l",
            providers=["CUDAExecutionProvider", "CPUExecutionProvider"],
        )
        self.app.prepare(ctx_id=0, det_size=(640, 640))

        # Tracking state
        self.tracks: Dict = {}
        self.current_track_id: int = 0
        self.frame_size: Tuple[int, int] = (640, 640)
        self.total_frames: int = 0
        self.fps: Optional[float] = None
        self.video_duration_ms: Optional[int] = (
            None  # Store video duration in milliseconds
        )

    def process_video(self, video_path: str) -> None:
        """Main video processing pipeline."""
        print(f"🎬 Starting face tracking for: {video_path}")

        # Initialize video capture
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Cannot open video file: {video_path}")

        self._setup_video_properties(cap)

        # Process frames
        frame_idx = 0
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break

            self._process_frame(frame, frame_idx)
            frame_idx += 1

            if frame_idx % 100 == 0:
                print(f"Processed {frame_idx} frames...")

        cap.release()
        self.total_frames = frame_idx

        # Calculate video duration in milliseconds
        if self.fps and self.fps > 0:
            self.video_duration_ms = int((self.total_frames / self.fps) * 1000)

        # Post-processing with updated methods
        self._check_overlap_iou()  # IoU-based overlap removal
        self._filter_tracks_by_bbox_size()  # Frame-wise filtering
        self._add_silence_analysis(video_path)

        print(
            f"✅ Completed processing {frame_idx} frames, found {len(self.tracks)} valid tracks"
        )

    def _setup_video_properties(self, cap: cv2.VideoCapture) -> None:
        """Extract and store video properties."""
        self.fps = cap.get(cv2.CAP_PROP_FPS)
        self.frame_size = (
            int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
        )

    def _process_frame(self, frame: np.ndarray, frame_idx: int) -> None:
        """Process a single frame for face detection and tracking."""
        faces = self.app.get(frame)
        current_detections = self._extract_face_data(faces, frame_idx)
        self._update_tracking_state(current_detections, frame_idx)

    def _extract_face_data(self, faces: List, frame_idx: int) -> List[Dict]:
        """Convert InsightFace results to standardized detection format."""
        return [
            {
                "bbox": face.bbox.astype(int),
                "embedding": face.embedding,
                "frame_idx": frame_idx,
                "bbox_area": self._calculate_bbox_area(face.bbox),
            }
            for face in faces
        ]

    @staticmethod
    def _calculate_bbox_area(bbox: np.ndarray) -> float:
        """Calculate bounding box area."""
        return (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])

    def _update_tracking_state(
        self, current_detections: List[Dict], frame_idx: int
    ) -> None:
        """Update tracking state with new detections."""
        active_tracks = self._get_active_tracks(frame_idx)
        matches, unmatched_detections = self._match_detections_to_tracks(
            current_detections, active_tracks
        )

        # Update existing tracks
        for track_id, detection_idx in matches:
            self._update_existing_track(track_id, current_detections[detection_idx])

        # Create new tracks
        for detection_idx in unmatched_detections:
            self._create_new_track(current_detections[detection_idx])

    def _get_active_tracks(self, frame_idx: int) -> Dict:
        """Get tracks that are still considered active."""
        return {
            tid: track
            for tid, track in self.tracks.items()
            if (frame_idx - track["last_seen"]) <= self.MAX_INACTIVE_FRAMES
        }

    def _match_detections_to_tracks(
        self, detections: List[Dict], active_tracks: Dict
    ) -> Tuple[List[Tuple], List[int]]:
        """Match current detections to existing active tracks."""
        matches = []
        unmatched_detections = []
        available_track_ids = list(active_tracks.keys())

        for detection_idx, detection in enumerate(detections):
            best_match = self._find_best_track_match(
                detection, active_tracks, available_track_ids
            )

            if best_match:
                matches.append((best_match, detection_idx))
                available_track_ids.remove(best_match)
            else:
                unmatched_detections.append(detection_idx)

        return matches, unmatched_detections

    def _find_best_track_match(
        self, detection: Dict, active_tracks: Dict, available_track_ids: List[str]
    ) -> Optional[str]:
        """Find the best matching track for a detection."""
        best_similarity = -1
        best_track_id = None

        for track_id in available_track_ids:
            track = active_tracks[track_id]
            similarity = self._calculate_cosine_similarity(
                track["last_embedding"], detection["embedding"]
            )

            if similarity > best_similarity and similarity >= self.SIMILARITY_THRESHOLD:
                best_similarity = similarity
                best_track_id = track_id

        return best_track_id

    @staticmethod
    def _calculate_cosine_similarity(
        embedding1: np.ndarray, embedding2: np.ndarray
    ) -> float:
        """Calculate cosine similarity between two embeddings."""
        return np.dot(embedding1, embedding2) / (
            np.linalg.norm(embedding1) * np.linalg.norm(embedding2)
        )

    def _update_existing_track(self, track_id: str, detection: Dict) -> None:
        """Update an existing track with new detection data."""
        track = self.tracks[track_id]
        track.update(
            {
                "last_embedding": detection["embedding"],
                "last_seen": detection["frame_idx"],
                "frame_indices": np.append(
                    track["frame_indices"], detection["frame_idx"]
                ),
                "bboxes": np.vstack((track["bboxes"], detection["bbox"])),
                "bbox_areas": np.append(track["bbox_areas"], detection["bbox_area"]),
            }
        )

    def _create_new_track(self, detection: Dict) -> None:
        """Initialize a new track with the first detection."""
        self.current_track_id += 1
        track_id = str(self.current_track_id)

        self.tracks[track_id] = {
            "frame_indices": np.array([detection["frame_idx"]], dtype=np.int32),
            "bboxes": np.array([detection["bbox"]], dtype=np.int32),
            "bbox_areas": np.array([detection["bbox_area"]], dtype=np.float32),
            "last_embedding": detection["embedding"],
            "last_seen": detection["frame_idx"],
            "start_frame": detection["frame_idx"],
        }

    def _calculate_iou(self, bbox1: np.ndarray, bbox2: np.ndarray) -> float:
        """Calculate Intersection over Union (IoU) between two bounding boxes."""
        # bbox format: [x1, y1, x2, y2]
        x1_inter = max(bbox1[0], bbox2[0])
        y1_inter = max(bbox1[1], bbox2[1])
        x2_inter = min(bbox1[2], bbox2[2])
        y2_inter = min(bbox1[3], bbox2[3])

        # Check if there's an intersection
        if x1_inter >= x2_inter or y1_inter >= y2_inter:
            return 0.0

        # Calculate intersection area
        intersection = (x2_inter - x1_inter) * (y2_inter - y1_inter)

        # Calculate areas of both bounding boxes
        area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
        area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])

        # Calculate union area
        union = area1 + area2 - intersection

        # Avoid division by zero
        if union == 0:
            return 0.0

        return intersection / union

    def _check_overlap_iou(self) -> None:
        """Remove tracks that have more than 20% IoU overlap with other tracks in any frame."""
        tracks_to_remove = set()
        # Get all unique frame indices across all tracks
        all_frames = set()
        for track in self.tracks.values():
            all_frames.update(track["frame_indices"])

        # Check each frame for overlapping tracks
        for frame_idx in all_frames:
            # Get all tracks present in this frame
            tracks_in_frame = []
            for track_id, track in self.tracks.items():
                if track_id in tracks_to_remove:
                    continue

                # Find if this track has a detection in this frame
                frame_mask = track["frame_indices"] == frame_idx
                if np.any(frame_mask):
                    bbox_idx = np.where(frame_mask)[0][0]
                    bbox = track["bboxes"][bbox_idx]
                    bbox_area = track["bbox_areas"][bbox_idx]
                    tracks_in_frame.append((track_id, bbox, bbox_area))

            # Check for overlaps between all pairs of tracks in this frame
            for i in range(len(tracks_in_frame)):
                for j in range(i + 1, len(tracks_in_frame)):
                    track1_id, bbox1, area1 = tracks_in_frame[i]
                    track2_id, bbox2, area2 = tracks_in_frame[j]

                    # Calculate IoU
                    iou = self._calculate_iou(bbox1, bbox2)
                    # print(f'IOU for {track1_id},{track2_id}:',iou)

                    if float(iou) > 0.2:
                        tracks_to_remove.add(track1_id)
                        tracks_to_remove.add(track2_id)
                        logger.info(
                            f"Removing track {track1_id} &  {track2_id} (IoU: {iou:.3f}) - "
                            f"overlaps with track {track2_id} at frame {frame_idx}"
                        )

        # Remove overlapping tracks
        for track_id in tracks_to_remove:
            if track_id in self.tracks:
                del self.tracks[track_id]

        logger.info(f"🔄 Removed {len(tracks_to_remove)} tracks due to IoU overlap")

    def _filter_tracks_by_bbox_size(self) -> None:
        """Remove tracks with small bounding boxes relative to others in the same frame."""
        tracks_to_remove = set()

        # Get all unique frame indices across all tracks
        all_frames = set()
        for track in self.tracks.values():
            all_frames.update(track["frame_indices"])

        # Process each frame
        for frame_idx in all_frames:
            # Get all tracks and their bboxes for this frame
            tracks_in_frame = []
            for track_id, track in self.tracks.items():
                if track_id in tracks_to_remove:
                    continue

                # Find if this track has a detection in this frame
                frame_mask = track["frame_indices"] == frame_idx
                if np.any(frame_mask):
                    bbox_idx = np.where(frame_mask)[0][0]
                    bbox_area = track["bbox_areas"][bbox_idx]
                    tracks_in_frame.append((track_id, bbox_area))

            # Skip if only one or no tracks in this frame
            if len(tracks_in_frame) <= 1:
                continue

            # Find maximum bbox area in this frame
            max_area = max(area for _, area in tracks_in_frame)

            # Mark small tracks for removal
            for track_id, bbox_area in tracks_in_frame:
                if bbox_area < (max_area * self.BBOX_SIZE_THRESHOLD):
                    tracks_to_remove.add(track_id)
                    logger.info(
                        f"Removing track {track_id} (area: {bbox_area:.0f}) - "
                        f"too small compared to max area {max_area:.0f} at frame {frame_idx}"
                    )

        # Remove filtered tracks
        for track_id in tracks_to_remove:
            if track_id in self.tracks:
                del self.tracks[track_id]

        logger.info(f"🧹 Filtered out {len(tracks_to_remove)} small tracks")

    def _add_silence_analysis(self, video_path: str) -> None:
        """Add silence detection flags to all tracks."""
        try:
            audio = AudioSegment.from_file(video_path)
            tracks_to_remove = []

            for track_id, track in self.tracks.items():
                track["has_silence"] = self._detect_silence_in_track(audio, track)
                track["has_invalid_silence"] = self._detect_invalid_silence_in_track(
                    audio, track
                )

                if track["has_invalid_silence"]:
                    logger.info(
                        f"Track {video_path} has invalid silence, marking for removal"
                    )
                    tracks_to_remove.append(track_id)

            # Remove tracks with invalid silence
            for track_id in tracks_to_remove:
                del self.tracks[track_id]

            logger.info(
                f"🔇 Removed {len(tracks_to_remove)} tracks due to invalid silence"
            )

        except Exception as e:
            logger.error(f"Silence detection failed: {str(e)}")
            # Set default silence flags if audio processing fails
            for track in self.tracks.values():
                track["has_silence"] = False
                track["has_invalid_silence"] = False

    def _detect_silence_in_track(self, audio: AudioSegment, track: Dict) -> bool:
        """Check if track contains any silence periods."""
        # return self._check_silence_with_threshold(
        #     audio, track, self.SILENCE_MIN_LENGTH
        # )
        return True

    def _detect_invalid_silence_in_track(
        self, audio: AudioSegment, track: Dict
    ) -> bool:
        """Check if track contains invalid (too long) silence periods."""
        # Calculate dynamic threshold: half of video duration
        if self.video_duration_ms is None:
            logger.warning(
                "Video duration not available, using default invalid silence threshold"
            )
            invalid_silence_threshold = 3000  # fallback to 5 seconds
        else:
            invalid_silence_threshold = self.video_duration_ms // 2
            logger.info(
                f"Using dynamic invalid silence threshold: {invalid_silence_threshold} ms"
                f"(half of video duration: {self.video_duration_ms}ms)"
            )
        return self._check_silence_with_threshold(
            audio, track, invalid_silence_threshold
        )

    def _check_silence_with_threshold(
        self, audio: AudioSegment, track: Dict, min_silence_length: int
    ) -> bool:
        """Generic silence checking with configurable threshold."""
        segments = self._get_continuous_segments(track["frame_indices"])

        for start_frame, end_frame in segments:
            start_ms = int((start_frame / self.fps) * 1000)
            end_ms = int(((end_frame + 1) / self.fps) * 1000)

            # Skip if segment is beyond audio length
            if start_ms >= len(audio):
                continue

            end_ms = min(end_ms, len(audio))
            segment = audio[start_ms:end_ms]

            silence_ranges = detect_silence(
                segment,
                min_silence_len=min_silence_length,
                silence_thresh=self.SILENCE_THRESHOLD,
            )
            if silence_ranges:
                return True

        return False

    @staticmethod
    def _get_continuous_segments(frame_indices: np.ndarray) -> List[Tuple[int, int]]:
        """Convert frame indices to continuous segments."""
        if frame_indices.size == 0:
            return []

        sorted_frames = np.sort(frame_indices)
        segments = []
        current_start = current_end = sorted_frames[0]

        for frame_idx in sorted_frames[1:]:
            if frame_idx == current_end + 1:
                current_end = frame_idx
            else:
                segments.append((current_start, current_end))
                current_start = current_end = frame_idx

        segments.append((current_start, current_end))
        return segments

    def get_tracks_data(self) -> List[Dict]:
        """Export track data in standardized format."""
        tracks = []
        for track_id, data in self.tracks.items():
            track = {
                "track_id": track_id,
                "start_frame": int(data["frame_indices"][0]),
                "frame_indices": data["frame_indices"],
                "bboxes": data["bboxes"],
                "bbox_areas": data["bbox_areas"],
                "last_embedding": data["last_embedding"],
                "has_silence": data.get("has_silence", False),
                "has_invalid_silence": data.get("has_invalid_silence", False),
            }
            tracks.append(track)
        return tracks

    def get_summary_stats(self) -> Dict:
        """Get summary statistics about the tracking results."""
        if not self.tracks:
            return {"total_tracks": 0}

        track_lengths = [len(track["frame_indices"]) for track in self.tracks.values()]
        silence_tracks = sum(
            1 for track in self.tracks.values() if track.get("has_silence", False)
        )

        stats = {
            "total_tracks": len(self.tracks),
            "avg_track_length": np.mean(track_lengths),
            "max_track_length": np.max(track_lengths),
            "min_track_length": np.min(track_lengths),
            "tracks_with_silence": silence_tracks,
            "total_frames_processed": self.total_frames,
        }

        # Add video duration info if available
        if self.video_duration_ms:
            stats["video_duration_ms"] = self.video_duration_ms
            stats["invalid_silence_threshold_ms"] = self.video_duration_ms // 2

        return stats


def save_track_data(track: Dict, output_dir: str, video_name: str) -> str:
    """Save individual track data to NPZ file."""
    track_id = track["track_id"]
    output_path = os.path.join(output_dir, f"{video_name}_track{track_id}.npz")

    # Remove track_id from data before saving (redundant with filename)
    save_data = track
    np.savez(output_path, **save_data)

    return output_path


def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(
        description="Advanced Face Tracking with Silence Detection and Filtering"
    )
    parser.add_argument("--video", required=True, help="Input video file path")
    parser.add_argument(
        "--output-dir", required=True, help="Directory to save track NPZ files"
    )
    parser.add_argument(
        "--workers", type=int, default=4, help="Number of parallel workers (default: 4)"
    )

    args = parser.parse_args()

    # Validate inputs
    if not os.path.isfile(args.video) or not args.video.lower().endswith(".mp4"):
        print(f"❌ Invalid or unsupported video file: {args.video}")
        sys.exit(1)

    try:
        print(f"🚀 Starting face tracking pipeline...")
        os.makedirs(args.output_dir, exist_ok=True)

        # Process video
        tracker = FaceTracker()
        tracker.process_video(args.video)

        # Get results
        tracks_data = tracker.get_tracks_data()
        stats = tracker.get_summary_stats()
        video_name = os.path.splitext(os.path.basename(args.video))[0]

        # Print summary
        print(f"\n📊 Processing Summary:")
        for key, value in stats.items():
            print(f"   {key}: {value}")

        # Save tracks in parallel
        if tracks_data:
            print(f"\n💾 Saving {len(tracks_data)} tracks...")
            with ThreadPoolExecutor(max_workers=args.workers) as executor:
                futures = [
                    executor.submit(save_track_data, track, args.output_dir, video_name)
                    for track in tracks_data
                ]

                for future in as_completed(futures):
                    output_path = future.result()
                    print(f"✅ Saved: {os.path.basename(output_path)}")
        else:
            print("⚠️ No valid tracks found to save")

        print(f"\n🎉 Processing complete! Results saved to: {args.output_dir}")

    except Exception as e:
        logger.error(f"Processing failed: {str(e)}")
        print(f"❌ Error processing {args.video}: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
