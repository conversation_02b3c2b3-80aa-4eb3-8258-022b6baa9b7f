"""
Affine transformation module for video processing.

This module provides functionality to apply affine transformations to videos
using face detection and landmark processing. It supports multi-GPU processing
for efficient batch video transformation.
"""

import os
import shutil
import subprocess
from multiprocessing import Process

import torch
from omegaconf import OmegaConf

from src.modules.affine_transform.affine_processor import AffineProcessor
from src.utils.logger import configure_logger
from src.utils.video_utils import get_all_videos_durations, write_video_ffmpeg

# TODO: Config file path a configurable parameter
CONFIG = OmegaConf.load("src/modules/affine_transform/config.yaml")

paths = []


def gather_video_paths(input_dir, output_dir):
    """
    Recursively gather video paths from input directory and create corresponding output paths.

    Args:
        input_dir (str): Input directory containing videos
        output_dir (str): Output directory for processed videos

    Note:
        This function modifies the global 'paths' list by appending tuples of
        (input_path, output_path) for videos that don't already exist in output.
    """
    for video in sorted(os.listdir(input_dir)):
        if video.endswith(".mp4"):
            video_input = os.path.join(input_dir, video)
            video_output = os.path.join(output_dir, video)
            if os.path.isfile(video_output):
                continue
            paths.append((video_input, video_output))
        elif os.path.isdir(os.path.join(input_dir, video)):
            gather_video_paths(
                os.path.join(input_dir, video), os.path.join(output_dir, video)
            )


class FaceDetector:
    """
    Face detector class for applying affine transformations to videos.

    This class wraps the AffineProcessor to provide face detection and
    affine transformation capabilities for video processing.
    """

    def __init__(
        self,
        device: str = "cpu",
    ):
        """
        Initialize the FaceDetector with specified device.

        Args:
            device (str): Device to run face detection on ('cpu', 'cuda', 'cuda:0', etc.)
        """
        self.affine_processor = AffineProcessor(
            config=CONFIG,
            det_config=CONFIG.detector.det_config,
            use_onnx=CONFIG.detector.use_onnx,
            device=device,
            debug_mode=False,
        )

    def affine_transform_video(self, video_path):
        """
        Apply affine transformation to a video.

        Args:
            video_path (str): Path to the input video file

        Returns:
            tuple: (transformed_frames, affine_matrices) where transformed_frames
                   are the processed video frames and affine_matrices are the
                   transformation matrices used
        """
        results, M1 = self.affine_processor(video_path=video_path, return_boxes=False)
        return results, M1

    def close(self):
        """
        Clean up and reset the AffineProcessor state.

        This method should be called when done processing to free up resources.
        """
        print("Resetting AffineProcessor")
        self.affine_processor.reset_state()


def combine_video_audio(
    video_frames, video_input_path, video_output_path, process_temp_dir
):
    """
    Combine processed video frames with original audio.

    Args:
        video_frames: Processed video frames to write
        video_input_path (str): Path to original video (for audio extraction)
        video_output_path (str): Path where final video should be saved
        process_temp_dir (str): Temporary directory for intermediate files

    Note:
        This function creates temporary audio and video files during processing
        and cleans them up after combining.
    """
    video_name = os.path.basename(video_input_path)[:-4]
    audio_temp = os.path.join(process_temp_dir, f"{video_name}_temp.wav")
    video_temp = os.path.join(process_temp_dir, f"{video_name}_temp.mp4")

    write_video_ffmpeg(video_temp, video_frames, fps=25)

    command = (
        f"ffmpeg -y -loglevel error -i {video_input_path} -q:a 0 -map a {audio_temp}"
    )
    subprocess.run(command, shell=True)

    os.makedirs(os.path.dirname(video_output_path), exist_ok=True)
    command = f"ffmpeg -y -loglevel error -i {video_temp} -i {audio_temp} -c:v libx264 -c:a aac -map 0:v -map 1:a -q:v 0 -q:a 0 {video_output_path}"
    subprocess.run(command, shell=True)

    os.remove(audio_temp)
    os.remove(video_temp)


def func(paths, process_temp_dir, device_id, resolution):
    """
    Worker function for processing videos on a specific GPU device.

    Args:
        paths (list): List of (input_path, output_path) tuples to process
        process_temp_dir (str): Temporary directory for this process
        device_id (int): GPU device ID to use
        resolution (int): Target resolution for processing (currently unused)

    Note:
        This function processes each video in the paths list, applying affine
        transformation and saving the result with original audio.
    """
    logger = configure_logger(f"affine_worker_gpu_{device_id}")
    os.makedirs(process_temp_dir, exist_ok=True)
    face_detector = FaceDetector(device=f"cuda:{device_id}")

    for video_input, video_output in paths:
        if os.path.isfile(video_output):
            continue
        try:
            video_frames, M1 = face_detector.affine_transform_video(video_input)
            logger.info(
                f"video_frames type: {type(video_frames)}, length: {len(video_frames) if video_frames is not None and hasattr(video_frames, '__len__') else 'N/A'}"
            )
            if (
                video_frames is not None
                and hasattr(video_frames, "__len__")
                and len(video_frames) > 0
            ):
                logger.info(
                    f"First frame type: {type(video_frames[0])}, First frame shape: {video_frames[0].shape if hasattr(video_frames[0], 'shape') else 'N/A'}"
                )
            elif video_frames is not None:
                logger.info(f"video_frames is not None but not a sequence or is empty.")
            else:
                logger.info(f"video_frames is None.")
        except Exception as e:  # Handle the exception of face not detcted
            logger.exception(f"Error during affine transformation for {video_input}")
            continue

        os.makedirs(os.path.dirname(video_output), exist_ok=True)
        combine_video_audio(video_frames, video_input, video_output, process_temp_dir)
        logger.info(f"Saved: {video_output}")

        # Save the M1 matrix to a file
        M1_path = os.path.join(
            os.path.dirname(video_output),
            os.path.basename(video_output)[:-4] + "_M1.pt",
        )
        logger.info(f"Saving M1 matrix to: {M1_path}")
        torch.save(M1, M1_path)

        face_detector.close()


def split(a, n):
    """
    Split a list into n roughly equal parts.

    Args:
        a (list): List to split
        n (int): Number of parts to split into

    Returns:
        generator: Generator yielding n sublists
    """
    k, m = divmod(len(a), n)
    return (a[i * k + min(i, m) : (i + 1) * k + min(i + 1, m)] for i in range(n))


def affine_transform_multi_gpus(
    input_dir, output_dir, temp_dir, resolution, num_workers
):
    """
    Apply affine transformations to videos using multiple GPUs.

    Args:
        input_dir (str): Directory containing input videos
        output_dir (str): Directory to save processed videos
        temp_dir (str): Temporary directory for intermediate files
        resolution (int): Target resolution for processing
        num_workers (int): Number of worker processes per GPU

    Raises:
        RuntimeError: If no GPUs are found

    Note:
        This function coordinates multi-GPU processing by splitting the video
        paths among available GPUs and spawning worker processes for each.
    """
    logger = configure_logger("affine_coordinator")
    logger.info(f"Recursively gathering video paths of {input_dir} ...")
    gather_video_paths(input_dir, output_dir)
    num_devices = torch.cuda.device_count()
    if num_devices == 0:
        raise RuntimeError("No GPUs found")

    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    os.makedirs(temp_dir, exist_ok=True)

    split_paths = list(split(paths, num_workers * num_devices))

    processes = []

    for i in range(num_devices):
        for j in range(num_workers):
            process_index = i * num_workers + j
            process = Process(
                target=func,
                args=(
                    split_paths[process_index],
                    os.path.join(temp_dir, f"process_{i}"),
                    i,
                    resolution,
                ),
            )
            process.start()
            processes.append(process)

    for process in processes:
        process.join()


if __name__ == "__main__":
    input_dir = "data/6_VALID"
    output_dir = "data/7_AFFINE"
    temp_dir = "temp"
    resolution = 256
    num_workers = 10  # How many processes per device

    get_all_videos_durations(input_dir, delete=True, threshold=3, num_workers=4)

    affine_transform_multi_gpus(
        input_dir, output_dir, temp_dir, resolution, num_workers
    )
