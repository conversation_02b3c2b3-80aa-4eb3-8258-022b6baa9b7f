import os
import argparse
import numpy as np
import subprocess
import logging
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from src.warp import process_video, smoothen_and_pad

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class FaceSegmentProcessor:
    def __init__(self, video_path, output_dir, min_duration=3, max_workers=4):
        self.video_path = video_path
        self.output_dir = Path(output_dir)
        self.tracks_dir = self.output_dir / "tracks"
        self.min_duration = min_duration
        self.max_workers = max_workers

        # Create directory structure
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.tracks_dir.mkdir(exist_ok=True)
        self.video_name = Path(video_path).stem

        self._probe_video()
        self.tracks = self._load_tracking_data()

    def _load_tracking_data(self):
        """Load tracking data from NPZ files"""
        tracks = []
        for npz_file in self.tracks_dir.glob(f"{self.video_name}_track*.npz"):
            data = np.load(npz_file)
            tracks.append(
                {
                    "track_id": str(data["track_id"]),
                    "frame_indices": data["frame_indices"].astype(np.int32),
                    "bboxes": data["bboxes"].astype(float),
                }
            )
        logger.info(f"📊 Loaded {len(tracks)} tracking data files")
        return tracks

    def _probe_video(self):
        """Get video metadata using FFprobe"""
        logger.info(f"🔎 Probing video: {self.video_path}")

        def get_probe_value(arg):
            cmd = [
                "ffprobe",
                "-v",
                "error",
                "-select_streams",
                "v:0",
                "-show_entries",
                arg,
                "-of",
                "default=noprint_wrappers=1:nokey=1",
                self.video_path,
            ]
            try:
                result = subprocess.check_output(cmd).decode().strip()
                if "/" in result:
                    num, den = result.split("/")
                    return float(num) / float(den)
                return float(result)
            except subprocess.CalledProcessError as e:
                logger.error(f"FFprobe error: {e}")
                raise

        self.fps = get_probe_value("stream=r_frame_rate")
        self.duration = get_probe_value("stream=duration")
        logger.info(f"📹 Video specs: {self.fps}fps, {self.duration:.2f}s")

    def _create_segments(self):
        """Create segments from tracking data"""
        segments = []
        for track in self.tracks:
            frame_indices = track["frame_indices"]
            duration = (frame_indices[-1] - frame_indices[0]) / self.fps

            # Filter by minimum duration
            if duration >= self.min_duration:
                segments.append(
                    {
                        "track_id": track["track_id"],
                        "frame_indices": track[
                            "frame_indices"
                        ],  # Include frame indices
                        "bboxes": track["bboxes"],
                        "duration": duration,
                    }
                )

        logger.info(f"🔢 Found {len(segments)} valid segments")
        return segments

    def _process_segment(self, seg):
        """Process a single segment using the warp pipeline"""
        try:
            track_id = str(seg["track_id"])
            frame_indices = seg["frame_indices"]  # Get frame indices for this track
            bboxes = smoothen_and_pad(seg["bboxes"])

            # Output path
            final_path = self.output_dir / f"{self.video_name}_track{track_id}.mp4"

            # Skip if already exists
            if final_path.exists():
                logger.info(f"⏭️ Skipping existing segment: track{track_id}")
                return str(final_path)

            logger.info(
                f"⚙️ Processing segment: track{track_id} ({len(frame_indices)} frames)"
            )

            # Pass frame indices to process_video
            process_video(
                video=self.video_path,
                frame_indices=frame_indices,  # Pass specific frame indices
                bboxes=bboxes,
                batch_size=16,
                output_path=str(final_path),
            )

            logger.info(f"✅ Completed segment: track{track_id}")
            return str(final_path)

        except Exception as e:
            logger.error(f"Error processing segment {seg['track_id']}: {str(e)}")
            return None

    def run(self):
        """Process all segments"""
        logger.info(
            f"🚀 Starting face segment processor with {self.max_workers} workers"
        )

        segments = self._create_segments()
        if not segments:
            logger.warning("⚠️ No valid segments found to process")
            return []

        results = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [executor.submit(self._process_segment, seg) for seg in segments]
            for future in futures:
                try:
                    result = future.result()
                    if result:
                        results.append(result)
                except Exception as e:
                    logger.error(f"Thread execution error: {str(e)}")

        logger.info(f"🏁 Completed processing {len(results)} segments")
        return results


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Video Processing Pipeline")
    parser.add_argument("--video", required=True, help="Input video file path")
    parser.add_argument(
        "--output-dir", required=True, help="Output directory for all results"
    )
    parser.add_argument(
        "--min-dur", type=float, default=3.0, help="Minimum segment duration in seconds"
    )
    parser.add_argument(
        "--workers",
        type=int,
        default=2,
        help="Number of parallel processing threads (default: 2)",
    )
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    args = parser.parse_args()

    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    processor = FaceSegmentProcessor(
        video_path=args.video,
        output_dir=args.output_dir,
        min_duration=args.min_dur,
        max_workers=args.workers,
    )
    processor.run()
