"""
Visual quality filtering module for video processing.

This module filters out videos with low visual quality using a HyperIQA-based
neural network model. The quality assessment is performed on sample frames
from each video, and videos below a quality threshold are excluded.
"""

import os
import tqdm
import torch
import torchvision
import shutil
from multiprocessing import Process
import numpy as np
from decord import VideoReader
from einops import rearrange
from src.utils.logger import configure_logger
from .modules.hyper_iqa import HyperNet, TargetNet


paths = []

logger = configure_logger(__name__)

def gather_paths(input_dir, output_dir):
    """
    Recursively gather video paths from input directory for quality filtering.

    Args:
        input_dir (str): Directory containing input videos
        output_dir (str): Directory where filtered videos will be saved

    Note:
        This function modifies the global 'paths' list by appending tuples of
        (input_path, output_path) for videos that don't already exist in output.
    """
    # os.makedirs(output_dir, exist_ok=True)

    for video in tqdm.tqdm(sorted(os.listdir(input_dir))):
        if video.endswith(".mp4"):
            video_input = os.path.join(input_dir, video)
            video_output = os.path.join(output_dir, video)
            if os.path.isfile(video_output):
                continue
            paths.append((video_input, video_output))
        elif os.path.isdir(os.path.join(input_dir, video)):
            gather_paths(
                os.path.join(input_dir, video), os.path.join(output_dir, video)
            )


def read_video(video_path: str):
    """
    Read sample frames from a video for quality assessment.

    Args:
        video_path (str): Path to the video file

    Returns:
        torch.Tensor: Tensor containing first, middle, and last frames
                     normalized to [0, 1] range with shape (3, C, H, W)

    Note:
        This function samples three representative frames (first, middle, last)
        from the video to assess overall visual quality.
    """
    vr = VideoReader(video_path)
    first_frame = vr[0].asnumpy()
    middle_frame = vr[len(vr) // 2].asnumpy()
    last_frame = vr[-1].asnumpy()
    vr.seek(0)
    video_frames = np.stack([first_frame, middle_frame, last_frame], axis=0)
    video_frames = torch.from_numpy(rearrange(video_frames, "b h w c -> b c h w"))
    video_frames = video_frames / 255.0
    return video_frames


def func(paths, device_id):
    """
    Worker function for processing videos on a specific GPU device.

    Args:
        paths (list): List of (input_path, output_path) tuples to process
        device_id (int): GPU device ID to use for processing

    Note:
        This function loads the HyperIQA model and processes each video in the
        paths list. Videos with quality scores >= 40 are copied to output directory.
        Quality scores range from 0-100, with higher scores indicating better quality.
    """
    device = f"cuda:{device_id}"

    model_hyper = HyperNet(16, 112, 224, 112, 56, 28, 14, 7).to(device)
    model_hyper.train(False)

    # load the pre-trained model on the koniq-10k dataset
    model_hyper.load_state_dict(
        (
            torch.load(
                "src/models/pretrained/koniq_pretrained.pkl",
                map_location=device,
                weights_only=True,
            )
        )
    )

    transforms = torchvision.transforms.Compose(
        [
            torchvision.transforms.CenterCrop(size=224),
            torchvision.transforms.Normalize(
                mean=(0.485, 0.456, 0.406), std=(0.229, 0.224, 0.225)
            ),
        ]
    )

    for video_input, video_output in paths:
        try:
            video_frames = read_video(video_input)
            video_frames = transforms(video_frames)
            video_frames = video_frames.clone().detach().to(device)
            paras = model_hyper(
                video_frames
            )  # 'paras' contains the network weights conveyed to target network

            # Building target network
            model_target = TargetNet(paras).to(device)
            for param in model_target.parameters():
                param.requires_grad = False

            # Quality prediction
            pred = model_target(
                paras["target_in_vec"]
            )  # 'paras['target_in_vec']' is the input to target net

            # quality score ranges from 0-100, a higher score indicates a better quality
            quality_score = pred.mean().item()
            logger.info(
                f"Input video: {video_input}\nVisual quality score: {quality_score:.2f}"
            )
            if quality_score >= 40:
                os.makedirs(os.path.dirname(video_output), exist_ok=True)
                shutil.copy(video_input, video_output)
        except Exception as e:
            print(e)


def split(a, n):
    """
    Split a list into n roughly equal parts.

    Args:
        a (list): List to split
        n (int): Number of parts to split into

    Returns:
        generator: Generator yielding n sublists
    """
    k, m = divmod(len(a), n)
    return (a[i * k + min(i, m) : (i + 1) * k + min(i + 1, m)] for i in range(n))


def filter_visual_quality_multi_gpus(input_dir, output_dir, num_workers):
    """
    Filter videos by visual quality using multiple GPUs.

    Args:
        input_dir (str): Directory containing input videos
        output_dir (str): Directory to save high-quality videos
        num_workers (int): Number of worker processes per GPU

    Raises:
        RuntimeError: If no GPUs are found

    Note:
        This function coordinates multi-GPU processing by splitting video paths
        among available GPUs and spawning worker processes for each. Only videos
        with quality scores >= 40 are kept.
    """
    gather_paths(input_dir, output_dir)
    num_devices = torch.cuda.device_count()
    if num_devices == 0:
        raise RuntimeError("No GPUs found")
    split_paths = list(split(paths, num_workers * num_devices))
    processes = []

    for i in range(num_devices):
        for j in range(num_workers):
            process_index = i * num_workers + j
            process = Process(target=func, args=(split_paths[process_index], i))
            process.start()
            processes.append(process)

    for process in processes:
        process.join()


if __name__ == "__main__":
    input_dir = "data/7_AFFINE"
    output_dir = "data/8_FINAL"
    num_workers = 20  # How many processes per device

    filter_visual_quality_multi_gpus(input_dir, output_dir, num_workers)
