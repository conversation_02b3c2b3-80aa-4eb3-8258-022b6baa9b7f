import os
from concurrent.futures import <PERSON><PERSON><PERSON><PERSON>xecutor, TimeoutError, as_completed
from typing import Dict, <PERSON>, Tuple, Union

import tqdm

from src.utils.logger import configure_logger
from src.utils.video_utils import collect_video_paths_recursively, resample_video_fps

# Configure logger
logger = configure_logger(__name__)


def collect_input_output_paths(
    input_dir: str, output_dir: str
) -> List[Tuple[str, str]]:
    """
    Collect video paths and generate corresponding output paths.

    Args:
        input_dir (str): Input directory containing videos
        output_dir (str): Output directory for processed videos

    Returns:
        List[Tuple[str, str]]: List of (input_path, output_path) tuples
    """
    # Get all input video paths
    input_paths = collect_video_paths_recursively(input_dir)
    path_pairs = []

    # Generate output paths while preserving directory structure
    for input_path in input_paths:
        rel_path = os.path.relpath(input_path, input_dir)
        output_path = os.path.join(output_dir, rel_path)

        # Skip if output already exists
        if not os.path.isfile(output_path):
            path_pairs.append((input_path, output_path))

    return path_pairs


def resample_fps_hz(video_input: str, video_output: str) -> None:
    """
    Resample video FPS to 25 and audio sample rate to 16000 Hz.

    Args:
        video_input (str): Input video path
        video_output (str): Output video path
    """
    try:
        os.makedirs(os.path.dirname(video_output), exist_ok=True)
        resample_video_fps(
            input_video_path=video_input,
            target_fps=25,
            audio_sample_rate=16000,
            output_path=video_output,
        )
    except Exception as e:
        logger.error(f"Error processing {video_input}: {e}")
        raise


def process_video_pair(paths: Tuple[str, str]) -> Tuple[str, Union[None, str]]:
    """Process a single video input/output pair"""
    try:
        resample_fps_hz(*paths)
        return paths[0], None  # Success case
    except Exception as e:
        return paths[0], str(e)  # Error case


def resample_fps_hz_multiprocessing(
    input_dir: str, output_dir: str, num_workers: int = None
) -> Dict[str, Union[None, str]]:
    """
    Process multiple videos in parallel, resampling FPS and audio sample rate.

    Args:
        input_dir (str): Input directory containing videos
        output_dir (str): Output directory for processed videos
        num_workers (int, optional): Number of parallel workers. If None, uses CPU count.

    Returns:
        Dict[str, Union[None, str]]: Mapping of input paths to either None (success) or error message
    """
    # Calculate optimal number of workers if not provided
    if num_workers is None:
        num_workers = max(1, os.cpu_count() // 2)
        logger.info(f"Automatically set num_workers to {num_workers}")

    logger.info(f"Collecting video paths from {input_dir} ...")
    path_pairs = collect_input_output_paths(input_dir, output_dir)

    if not path_pairs:
        logger.warning("No videos found to process.")
        return {}

    results = {}
    logger.info(f"Processing {len(path_pairs)} videos with {num_workers} workers ...")

    try:
        with ProcessPoolExecutor(max_workers=num_workers) as executor:
            # Submit all tasks
            future_to_path = {
                executor.submit(process_video_pair, pair): pair[0]
                for pair in path_pairs
            }

            # Process completed tasks with progress bar
            with tqdm.tqdm(total=len(path_pairs), desc="Processing videos") as pbar:
                for future in as_completed(
                    future_to_path, timeout=300
                ):  # 5 minute timeout per video
                    input_path = future_to_path[future]
                    try:
                        _, error = future.result()
                        results[input_path] = error
                    except TimeoutError:
                        results[input_path] = "Processing timed out"
                        logger.error(f"Processing timed out for {input_path}")
                    except Exception as e:
                        results[input_path] = str(e)
                        logger.error(f"Error processing {input_path}: {e}")
                    pbar.update(1)

        # Log summary
        success_count = sum(1 for v in results.values() if v is None)
        logger.info(f"Successfully processed {success_count}/{len(path_pairs)} videos")

        return results

    except Exception as e:
        logger.exception(f"Error in parallel video processing: {e}")
        raise


if __name__ == "__main__":
    input_dir = "/home/<USER>/HotDub-Preprocess/assets/inputs"
    output_dir = "/home/<USER>/HotDub-Preprocess/assets/resampled"
    num_workers = 8

    try:
        results = resample_fps_hz_multiprocessing(input_dir, output_dir, num_workers)
        # Log errors if any
        errors = {k: v for k, v in results.items() if v is not None}
        if errors:
            logger.error("Failed videos:")
            for path, error in errors.items():
                logger.error(f"{path}: {error}")
    except Exception as e:
        logger.exception(f"Processing failed: {e}")
