# TalkNet Wrapper Plan

## Objective
Create a wrapper for `models/talknet/demoTalkNet.py` that efficiently provides active speaker detection scores for a video when called from another module, optimizing for faster processing.

## Requirements
- **Input**: Video file path (e.g., `/path/to/video.mp4`).
- **Output**: List of scores from `evaluate_network` (per-track active speaker scores).
- **Efficiency**: Faster processing by reusing cached preprocessing data (`scores.pckl`, `tracks.pckl`).

## Implementation Details
- **File**: `models/talknet/talknet_wrapper.py`
- **Function**: `get_video_scores(video_path)`
  - Initializes `Config` with `video_name` derived from `video_path`.
  - Sets up processing paths (e.g., `pyworkPath`, `pycropPath`) like `process_video`.
  - Checks for cached data:
    - If `scores.pckl` exists in `pyworkPath`, loads and returns scores.
    - If not, checks for `tracks.pckl`:
      - If exists, loads tracks.
      - If not, runs preprocessing: `scene_detect`, `inference_video`, `track_shot`, `crop_video`.
    - Runs `evaluate_network` on cropped clips.
    - Saves scores to `scores.pckl` for future reuse.
  - Returns scores without visualization or Columbia evaluation.
- **Imports**: Only necessary components from `demoTalkNet.py` (e.g., `Config`, `evaluate_network`, etc.).
- **Optimization**: Skips redundant steps if cached data is valid, leverages existing multi-threading (`n_data_loader_thread`).

## Workflow
```mermaid
graph TD
    A[Call get_video_scores(video_path)] --> B{Check scores.pckl exists?}
    B -->|Yes| C[Load scores from scores.pckl]
    B -->|No| D{Check tracks.pckl exists?}
    D -->|Yes| E[Load tracks from tracks.pckl]
    D -->|No| F[Run preprocessing: scene_detect, inference_video, track_shot, crop_video]
    E --> G[Run evaluate_network with tracks]
    F --> G
    C --> H[Return scores]
    G --> I[Save scores to scores.pckl]
    I --> H
```

## Next Steps
- Switch to Code mode to implement `talknet_wrapper.py`.
- Test the wrapper with a sample video to verify scores are returned efficiently.