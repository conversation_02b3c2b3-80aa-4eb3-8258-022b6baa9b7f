# Pythonic Structure Migration Plan

## Current Structural Issues
```mermaid
graph TD
    A[Root Directory Clutter] --> B[Test files in root]
    A --> C[Mixed content types]
    D[Missing Packaging] --> E[No setup.py/pyproject.toml]
    D --> F[No requirements.txt]
    G[Test Organization] --> H[No dedicated tests/ directory]
```

## Phase 1: Directory Restructuring
```mermaid
flowchart LR
    subgraph Root["Project Root (Clean)"]
        direction TB
        R1[src/] --> R2[models/]
        R1 --> R3[utils/]
        R4[tests/] --> R5[test_imports.py]
        R4 --> R6[test_logging.py]
        R7[notebooks/] --> R8[playground.ipynb]
        R9[docs/]
        R10[configs/]
        R11[assets/]
    end
```

### Step-by-Step Implementation

1. **Create New Directories**
```bash
mkdir -p tests/unit notebooks/ data/
```

2. **Move Existing Files**
```bash
# Move test files
mv test_*.py tests/unit/

# Move notebook
mv playground.ipynb notebooks/

# Create __init__.py files for test packages
touch tests/__init__.py tests/unit/__init__.py
```

3. **Update Import Paths**  
   Required changes in test files:
```diff
# Before
- from src.utils import logger
# After
+ from ..src.utils import logger
```

## Phase 2: Python Packaging Setup

### Package Configuration
```python
# setup.py
from setuptools import setup, find_packages

setup(
    name="hotdub",
    version="0.1.0",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    install_requires=[
        'numpy>=1.21',
        'opencv-python',
        'librosa',
        'pytest'
    ]
)
```

### Requirements File
```bash
# Generate from environment
pip freeze > requirements.txt
```

## Phase 3: Test Structure Standardization

### Proposed Test Layout
```
tests/
├── __init__.py
├── unit/
│   ├── __init__.py
│   ├── test_imports.py
│   └── test_logging.py
└── integration/
    └── test_pipelines.py
```

## Migration Checklist
- [ ] Create new directory structure
- [ ] Move existing test files
- [ ] Update import references
- [ ] Add packaging configuration
- [ ] Verify pytest discovery
- [ ] Update documentation paths

## Post-Migration Validation
```bash
# Run tests
python -m pytest tests/

# Build package
python setup.py sdist bdist_wheel

# Verify imports
python -c "from hotdub.models import talknet"