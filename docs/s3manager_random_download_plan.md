# Plan to Add --random Key to s3manager download

The goal is to enable downloading a specified number of random files from an S3 directory using the `s3manager` tool.

**Inspired by:** `scripts/download_random.py`

**Affected Files:**

1.  `src/s3manager/cli.py`: To add the new command-line option.
2.  `src/s3manager/handler.py`: To implement the random selection and download logic.

**Detailed Steps:**

**1. Modify `src/s3manager/cli.py`**

*   **Add a new Click option to the `download` command:**
    *   Locate the `download` command definition (around line 178).
    *   Add a `@click.option` decorator for `--random`:
        ```python
        @click.option('--random', 'random_count', type=int, default=None, help="Download N random files from the remote_path (if it's a directory/prefix).", show_default=False)
        ```
        *   `'random_count'` will be the parameter name in the function.
        *   `type=int` ensures the input is an integer.
        *   `default=None` means the option is not active unless specified.
        *   `show_default=False` avoids showing `None` in help text.
*   **Update the `download` function signature:**
    *   Add `random_count` to the function parameters:
        ```python
        def download(ctx, alias, remote_path, local_path, random_count):
        ```
*   **Pass `random_count` to the handler:**
    *   When calling `s3_handler.download_directory()`, pass the `random_count` value. The call (around line 212 or 238) will look something like:
        ```python
        downloaded_count, total_files = s3_handler.download_directory(
            client, bucket_name, remote_path, local_path, random_count=random_count
        )
        ```
    *   The `--random` option should primarily apply when `remote_path` is treated as a directory/prefix. If `random_count` is specified for a single file download, it should be ignored or raise a warning.

**2. Modify `src/s3manager/handler.py`**

*   **Import `random` module:**
    *   Add `import random` at the top of the file.
*   **Update `download_directory` function signature:**
    *   Modify the function definition (around line 72) to accept the new optional parameter:
        ```python
        def download_directory(
            client: Minio,
            bucket_name: str,
            s3_prefix: str,
            local_directory_path: str,
            random_count: Optional[int] = None  # New parameter
        ) -> Tuple[int, int]:
        ```
*   **Implement random selection logic within `download_directory`:**
    *   After listing objects and before the download loop (after `objects_to_download` is populated, around line 97):
        ```python
        # ... existing code to populate objects_to_download ...
        total_objects_found_in_prefix = len(objects_to_download) # Store total before sampling

        if random_count is not None and random_count > 0:
            if not objects_to_download:
                logger.warning(f"No files found under prefix '{s3_prefix}' to select randomly from.")
                return 0, 0 

            if random_count >= total_objects_found_in_prefix:
                logger.info(f"--random count ({random_count}) is >= total files ({total_objects_found_in_prefix}). Downloading all files from prefix '{s3_prefix}'.")
            else:
                logger.info(f"Randomly selecting {random_count} files out of {total_objects_found_in_prefix} from prefix '{s3_prefix}'.")
                objects_to_download = random.sample(objects_to_download, random_count)
            
            # This reflects the number to be downloaded
            total_objects_for_download_attempt = len(objects_to_download) 
        else:
            total_objects_for_download_attempt = total_objects_found_in_prefix

        # ... rest of the download loop using the (potentially sampled) objects_to_download list ...
        # The tuple returned (downloaded_count, total_files_attempted)
        # should reflect (downloaded_count, total_objects_for_download_attempt)
        ```
*   **Adjust Logging and Return Values:**
    *   The log message at the end of `download_directory` and the return tuple `(downloaded_count, total_objects_for_download_attempt)` should accurately reflect whether all files under the prefix were considered or a random subset was attempted.
    *   The CLI output in `src/s3manager/cli.py` will use this `total_objects_for_download_attempt` for its status message.

**Mermaid Diagram of the Download Flow with `--random`:**

```mermaid
graph TD
    A[s3manager download --random N alias remote/path/ local/path/] --> B{Parse Args};
    B --> C{Is --random N specified?};
    C -- Yes --> D{Call handler.download_directory(..., random_count=N)};
    C -- No --> E{Call handler.download_directory(..., random_count=None)};
    D --> F[handler.download_directory];
    E --> F;
    F --> G[List all objects under remote/path/];
    G --> H{Filter for files (obj.is_dir == False)};
    H --> I{Is random_count valid ( > 0 )?};
    I -- Yes --> J{Sample N files from list (or all if N >= total)};
    J --> K[Set objects_for_download = sampled_files];
    I -- No --> L[Set objects_for_download = all_files];
    K --> M[Loop: Download each file in objects_for_download];
    L --> M;
    M --> N[Return (downloaded_count, selected_count_or_total_found)];
    N --> O[CLI: Display result];
```

**Considerations:**

*   **Error Handling:** If `random_count` is specified but `remote_path` is a single file (not a prefix/directory), the `cli.py` logic should ideally ignore `--random` or print a warning.
*   **Clarity of Output:** The user should clearly understand if all files or a random subset were downloaded, and how many were selected versus how many were available in the prefix.