# Video Utilities Refactoring Plan

This document outlines the key changes needed for refactoring `video_utils.py` into a functional approach. This serves as a guide for implementation in Code mode.

## Current State

- `video_utils.py` contains only placeholder functions (no implementations)
- Functions are organized in `Utils` and `Processor` classes
- Many video functions exist in `util.py` that should be moved to `video_utils.py`

## Refactoring Goals

1. Convert to a functional approach (standalone functions instead of classes)
2. Implement all placeholder functions based on their docstring comments
3. Move relevant video functions from `util.py` to `video_utils.py`
4. Maintain backward compatibility
5. Add proper error handling and logging

## Functions to Include

### From existing `video_utils.py` (currently placeholders)

| Current | Refactored |
|---------|------------|
| `Utils.is_corrupt()` | `is_corrupt()` |
| `Utils.read_video()` | `read_video()` |
| `Utils.merge_audio()` | `merge_audio()` |
| `Processor.resample()` | `resample()` |

### From `util.py` to move

| Current | Refactored |
|---------|------------|
| `read_video()` | (merge with the placeholder) |
| `read_video_decord()` | `read_video_decord()` |
| `read_video_cv2()` | `read_video_cv2()` |
| `write_video()` | `write_video()` |
| `check_video_fps()` | `check_video_fps()` |
| `count_video_time()` | `count_video_time()` |
| `gather_video_paths()` | `gather_video_paths()` |
| `gather_video_paths_recursively()` | `gather_video_paths_recursively()` |
| `save_videos_grid()` | `save_videos_grid()` |
| `check_ffmpeg_installed()` | `check_ffmpeg_installed()` |

## Function Specifications

### 1. `is_corrupt(input_video_path: str) -> bool`

- **Purpose**: Check if a video file is corrupted or unreadable
- **Implementation**:
  - Use FFprobe to check video integrity
  - Also try to open with OpenCV as a fallback check
  - Return boolean indicating if file is corrupt
  - Add proper error handling and logging

### 2. `read_video(input_video_path: str) -> Union[np.ndarray, str]`

- **Purpose**: Read video frames and validate duration and FPS
- **Implementation**:
  - Check video duration (minimum 5 seconds)
  - Check FPS (minimum 20 FPS)
  - Return appropriate error string when conditions not met
  - Return numpy array of frames when valid
  - Add proper error handling and logging

### 3. `merge_audio(input_video_path: str, input_audio_path: str, output_path: Optional[str] = None) -> str`

- **Purpose**: Merge audio and video using FFMPEG
- **Implementation**:
  - Generate output path based on inputs if not provided
  - Use FFMPEG to combine video and audio
  - Add proper error handling and logging
  - Return path to merged video

### 4. `resample(input_video_path: str, target_fps: int = 25, output_path: Optional[str] = None) -> str`

- **Purpose**: Resample video to target FPS (default 25) if needed
- **Implementation**:
  - Check current FPS
  - Skip resampling if FPS already matches target
  - Use FFMPEG for resampling if needed
  - Add proper error handling and logging
  - Return path to resampled video

### 5. `read_video_decord(video_path: str, return_fps: bool = False) -> Union[np.ndarray, Tuple[np.ndarray, float]]`

- **Purpose**: Read video frames using Decord library
- **Implementation**:
  - Copy existing implementation from util.py
  - Add proper error handling and logging
  - Return frames or (frames, fps) based on return_fps parameter

### 6. `read_video_cv2(video_path: str, return_fps: bool = False) -> Union[np.ndarray, Tuple[np.ndarray, float]]`

- **Purpose**: Read video frames using OpenCV
- **Implementation**:
  - Copy existing implementation from util.py
  - Add proper error handling and logging
  - Return frames or (frames, fps) based on return_fps parameter

### 7. `write_video(video_output_path: str, video_frames: np.ndarray, fps: int) -> str`

- **Purpose**: Write frames to a video file
- **Implementation**:
  - Copy existing implementation from util.py
  - Add proper error handling and logging
  - Return path to saved video

### 8. `check_video_fps(video_path: str) -> float`

- **Purpose**: Check if video FPS is 25
- **Implementation**:
  - Copy existing implementation from util.py
  - Add proper error handling and logging
  - Return FPS or raise error if not 25

### 9. `count_video_time(video_path: str) -> float`

- **Purpose**: Calculate the duration of a video in seconds
- **Implementation**:
  - Copy existing implementation from util.py
  - Add proper error handling and logging
  - Return duration in seconds

### 10. `gather_video_paths(input_dir: str, paths: List[str]) -> None`

- **Purpose**: Recursively gather video paths from directory
- **Implementation**:
  - Copy existing implementation from util.py
  - Add proper error handling and logging
  - Modify paths list in-place

### 11. `gather_video_paths_recursively(input_dir: str) -> List[str]`

- **Purpose**: Recursively find all video paths from directory
- **Implementation**:
  - Copy existing implementation from util.py
  - Add proper error handling and logging
  - Return list of video paths

### 12. `save_videos_grid(videos: torch.Tensor, path: str, rescale: bool = False, n_rows: int = 6, fps: int = 8) -> str`

- **Purpose**: Save a grid of videos as an animated GIF
- **Implementation**:
  - Copy existing implementation from util.py
  - Add proper error handling and logging
  - Return path to saved GIF

### 13. `check_ffmpeg_installed() -> bool`

- **Purpose**: Verify FFMPEG is installed and available
- **Implementation**:
  - Copy existing implementation from util.py
  - Add proper error handling and logging
  - Return boolean indicating if FFMPEG is available

## Required Imports

```python
import os
import cv2
import shutil
import numpy as np
import subprocess
import tempfile
import torch
import imageio
from typing import Union, List, Tuple, Optional
from decord import VideoReader
from einops import rearrange
import torchvision
from src.utils.logger import configure_logger
```

## Key Implementation Considerations

1. **Error Handling**:
   - Use try/except blocks for all external operations
   - Properly handle subprocess errors with FFMPEG
   - Use logger for all error messages

2. **FFMPEG Operations**:
   - Check for FFMPEG installation before operations
   - Handle command-line errors properly
   - Use appropriate FFMPEG parameters for quality

3. **Backward Compatibility**:
   - Maintain function signatures where possible
   - Follow existing behavior for current functions

## Next Steps for Code Mode

1. Implement the refactored `video_utils.py` based on this plan
2. Test each function independently
3. Update any imports in files that use these utilities