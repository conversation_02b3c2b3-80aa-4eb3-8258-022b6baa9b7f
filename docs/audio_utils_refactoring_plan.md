# Audio Utilities Refactoring Plan

This document outlines the key changes needed for refactoring `audio_utils.py` into a functional approach. This serves as a guide for implementation in Code mode.

## Current State

- `audio_utils.py` contains a mix of implemented and placeholder functions
- Functions are organized in `Utils` and `Processor` classes
- Some audio functions exist in `util.py` that should be moved to `audio_utils.py`

## Refactoring Goals

1. Convert to a functional approach (standalone functions instead of classes)
2. Implement the placeholder function (extract_audio)
3. Move relevant audio functions from `util.py` to `audio_utils.py`
4. Maintain backward compatibility
5. Add proper error handling and logging

## Functions to Include

### From existing `audio_utils.py`

| Current | Refactored |
|---------|------------|
| `Utils.extract_audio()` | `extract_audio()` |
| `Processor.speaker_count()` | `speaker_count()` |
| `Processor.enhance()` | `enhance()` |

### From `util.py` to move

| Current | Refactored |
|---------|------------|
| `read_audio()` | `read_audio()` |
| `make_audio_window()` | `make_audio_window()` |

### New utility functions

| Function | Purpose |
|----------|---------|
| `check_ffmpeg_installed()` | Verify FFMPEG is available |

## Function Specifications

### 1. `extract_audio(input_video_path: str, output_path: Optional[str] = None, format: str = "wav") -> str`

- **Purpose**: Extract audio from a video file using FFMPEG
- **Implementation**:
  - Use FFMPEG to extract audio track from video
  - Default to WAV format if not specified
  - Generate output path based on input if not provided
  - Add proper error handling and logging

### 2. `speaker_count(audio_file_path: str, device: Optional[str] = None) -> int`

- **Purpose**: Count unique speakers in audio file using pyannote.audio
- **Implementation**: 
  - Keep existing implementation with minor improvements
  - Add better error handling and logging

### 3. `enhance(audio_file_path: str, model_name: Optional[str] = None) -> List[str]`

- **Purpose**: Enhance audio quality using audio-separator
- **Implementation**: 
  - Keep existing implementation with minor improvements
  - Add better error handling and logging

### 4. `read_audio(audio_path: str, audio_sample_rate: int = 16000) -> torch.Tensor`

- **Purpose**: Read audio file and return tensor of samples
- **Implementation**: 
  - Copy existing implementation from util.py
  - Add proper error handling and logging

### 5. `make_audio_window(audio_embeddings: torch.Tensor, window_size: int) -> torch.Tensor`

- **Purpose**: Create sliding windows from audio embeddings
- **Implementation**: 
  - Copy existing implementation from util.py
  - Maintain same behavior for backward compatibility

### 6. `check_ffmpeg_installed() -> bool`

- **Purpose**: Verify FFMPEG is installed and available
- **Implementation**: 
  - Copy existing implementation from util.py
  - Return boolean indicating if FFMPEG is available

## Required Imports

```python
import os
import subprocess
import torch
from typing import Union, List, Tuple, Optional
from dotenv import load_dotenv
from audio_separator.separator import Separator
from pyannote.audio import Pipeline
from decord import AudioReader
from src.utils.logger import configure_logger
```

## Key Implementation Considerations

1. **Error Handling**:
   - Use try/except blocks for all external operations
   - Properly handle subprocess errors with FFMPEG
   - Use logger for all error messages

2. **Dependencies**:
   - Ensure proper error messages when dependencies are missing
   - Handle HuggingFace token error gracefully

3. **Backward Compatibility**:
   - Maintain function signatures where possible
   - Follow existing behavior for current functions

## Next Steps for Code Mode

1. Implement the refactored `audio_utils.py` based on this plan
2. Test each function independently
3. Update any imports in files that use these utilities