# Plan for preprocess.py Enhancements

This plan incorporates user-suggested improvements, items from `docs/improvements_scope.md`, and additional suggestions for enhancing the `preprocess.py` script.

**I. Command-Line Interface (CLI) and User Experience (UX)**

1.  **Transient CUI for Stage Selection (from `docs/improvements_scope.md` & User Request):**
    *   **Goal:** Allow users to interactively select pipeline start and end stages.
    *   **Tasks:**
        *   Integrate `rich` library.
        *   Define pipeline stages (potentially read from the new configuration file - see item II.1).
        *   Implement `rich.prompt.Prompt` or `rich.panel.Panel` for stage selection in `main()` function of `preprocess.py`.
        *   Add validation (end stage >= start stage).
        *   Pass selected stages to `pipeline.run_pipeline()`.
    *   **Files Affected:** `preprocess.py`

2.  **Input Directory Handling (from `docs/improvements_scope.md` & User Request):**
    *   **Goal:** Allow users to specify an input directory for raw videos.
    *   **Tasks:**
        *   Add `--input-dir` / `-i` argument using `argparse` in `main()`.
        *   In `main()` or early in `PreprocessPipeline.__init__()`:
            *   Create `data/0_RAW_VIDEOS` if it doesn't exist.
            *   Copy video files from the provided `--input-dir` to `data/0_RAW_VIDEOS` using `shutil.copy2`.
            *   Implement error handling (source/target directory permissions, existence).
    *   **Files Affected:** `preprocess.py`

3.  **Argument Shorthands (from `docs/improvements_scope.md` & User Request):**
    *   **Goal:** Add short aliases for existing CLI arguments.
    *   **Tasks:**
        *   Review existing `argparse` definitions in `main()`.
        *   Add short versions (e.g., `-b` for `--base-dir`, `-w` for `--workers`, `-s` for `--start-stage`, `-e` for `--end-stage`) where appropriate and non-conflicting.
    *   **Files Affected:** `preprocess.py`

4.  **Enhanced Progress Indication (New Suggestion):**
    *   **Goal:** Provide visual feedback for long-running operations.
    *   **Tasks:**
        *   Integrate `rich.progress.Progress` within relevant pipeline methods in `PreprocessPipeline` class (e.g., `denoise_videos()`, `run_asd()`, `run_affine()`, `visual_quality_check()`).
        *   Update loops processing multiple files/items to use the progress bar.
    *   **Files Affected:** `preprocess.py`

**II. Configuration and Robustness**

1.  **Externalized Configuration (New Suggestion, builds on `docs/improvements_scope.md`):**
    *   **Goal:** Move hardcoded parameters to a YAML configuration file for flexibility.
    *   **Tasks:**
        *   Create/extend a YAML configuration file (e.g., `src/configs/pipeline_config.yaml`).
        *   Identify hardcoded parameters in `preprocess.py` (e.g., `PROCESS_KILL_TIMEOUT`, `FFMPEG_TIMEOUT`, `CUDA_RETRY_COUNT`, `confidence` in `filter_single_face()`, `min_duration` in `delete_short_videos()`, `resolution` in `run_affine()`, etc.).
        *   Move these parameters to the YAML file.
        *   Load this configuration in `PreprocessPipeline.__init__()` or `main()` and use the loaded values.
        *   Consider structuring the YAML by stage or component.
    *   **Files Affected:** `preprocess.py`, new YAML file in `src/configs/`.

2.  **Pretrained Checkpoint Management (from `docs/improvements_scope.md`):**
    *   **Goal:** Automatically download missing model checkpoints.
    *   **Tasks:**
        *   Create/update `models.yaml` (as detailed in `docs/improvements_scope.md`) with URLs, checksums, and local paths for all required models (S3FD, TalkNet, Face Mesh, etc.).
        *   Implement a utility function (e.g., in `src/utils/model_utils.py`) to:
            *   Parse `models.yaml`.
            *   Check for file existence and checksum.
            *   Download using `requests` if missing/corrupt (with `tqdm` for progress).
            *   Verify checksum after download.
        *   Call this utility function before model loading in relevant parts of the pipeline (e.g., within specific stage methods or model loading wrappers).
    *   **Files Affected:** New `src/utils/model_utils.py`, `models.yaml`, potentially `preprocess.py` and model-specific scripts in `src/`.

3.  **Credential Checks (from `docs/improvements_scope.md`):**
    *   **Goal:** Handle optional credentials gracefully.
    *   **Tasks:**
        *   Add optional `argparse` arguments for credentials in `main()`.
        *   Implement logic to check if credentials (e.g., `SLACK_WEBHOOK_URL` from `os.getenv()`) are provided/valid.
        *   If optional credentials (like Slack webhook) are missing, issue a warning but allow the pipeline to proceed.
        *   Consider if any credentials should be *required* for certain operations and handle accordingly.
    *   **Files Affected:** `preprocess.py`

4.  **Structured Logging for Key Events (New Suggestion):**
    *   **Goal:** Improve log parsing and analysis.
    *   **Tasks:**
        *   Review current logging in `preprocess.py` (using `logger = configure_logger(__name__)`).
        *   Identify key events (e.g., stage start/end, significant errors, summary statistics per stage).
        *   Modify logging calls for these events to output in a structured format (e.g., JSON). This might involve custom `logging.Formatter` or using a library like `python-json-logger`.
        *   Ensure `src/utils/logger.py` can support or be adapted for this.
    *   **Files Affected:** `preprocess.py`, `src/utils/logger.py`.

**III. Core Pipeline Logic Enhancements (from `docs/improvements_scope.md`)**

1.  **Scenedetect for Dark Scenes:**
    *   **Goal:** Improve shot generation accuracy in dark video segments.
    *   **Tasks:** As detailed in `docs/improvements_scope.md` (lines 5-13), involving `PySceneDetect` experimentation and potential pre-processing.
    *   **Files Affected:** `src/generate_shots.py` (and potentially new utility scripts for pre-processing).

2.  **Smart Active Speaker Detection (replacing Single Face Filtering):**
    *   **Goal:** Filter videos based on active speaker presence rather than just single faces.
    *   **Tasks:** As detailed in `docs/improvements_scope.md` (lines 15-25), involving integration of ASD output with face detection.
    *   **Files Affected:** `src/filter_single_face.py` (major rewrite), `preprocess.py` (stage name/logic update), potentially ASD-related scripts like `src/asd.py` or `src/models/talknet/talknet_wrapper.py`.

3.  **Robust Affine Transform with Dense Landmarks (MediaPipe):**
    *   **Goal:** Improve the accuracy and robustness of the affine transformation.
    *   **Tasks:** As detailed in `docs/improvements_scope.md` (lines 27-33), involving `mediapipe` Face Mesh integration.
    *   **Files Affected:** `src/affine.py`, `src/modules/affine_transform/`.

**IV. Documentation and Memory Bank**

1.  **Update Memory Bank:**
    *   **Goal:** Keep project documentation current.
    *   **Tasks:**
        *   Update `memory-bank/projectBrief.md`, `memory-bank/activeContext.md`, `memory-bank/productContext.md`, `memory-bank/progress.md`, `memory-bank/decisionLog.md`, and `memory-bank/systemPatterns.md` to reflect these planned changes once approved and as they are implemented.
        *   Specifically, new decisions (e.g., choice of `rich` for CUI, configuration file format) should be logged in `decisionLog.md`.
        *   New patterns (e.g., structured logging, model auto-download) should be added to `systemPatterns.md`.
    *   **Files Affected:** All files in `memory-bank/`.

2.  **Update `docs/improvements_scope.md`:**
    *   **Goal:** Consolidate all agreed-upon improvements.
    *   **Tasks:** Add the new suggestions (Externalized Config, Enhanced Progress, Structured Logging) to this document with appropriate implementation details.
    *   **Files Affected:** `docs/improvements_scope.md`

## Visual Plan (Mermaid Diagram)

```mermaid
gantt
    dateFormat  YYYY-MM-DD
    title preprocess.py Enhancement Plan

    section CLI & UX Enhancements
    Transient CUI for Stages    :crit, ux_cui, 2025-05-10, 3d
    Input Directory Handling    :crit, ux_input, after ux_cui, 2d
    Argument Shorthands         :ux_shorthands, after ux_input, 1d
    Enhanced Progress Indication:ux_progress, after ux_shorthands, 3d

    section Configuration & Robustness
    Externalized Configuration  :crit, conf_yaml, 2025-05-10, 4d
    Checkpoint Management       :conf_models, after conf_yaml, 3d
    Credential Checks           :conf_creds, after conf_models, 1d
    Structured Logging          :conf_logging, after conf_creds, 2d

    section Core Pipeline Logic (from docs/improvements_scope.md)
    Scenedetect Dark Scenes     :core_scenes, 2025-05-15, 5d
    Smart ASD Filter            :crit, core_asd, after core_scenes, 7d
    Robust Affine Transform     :core_affine, after core_asd, 6d

    section Documentation
    Update Memory Bank          :doc_mem, 2025-05-10, 15d
    Update improvements_scope.md:doc_scope, after ux_progress, 1d