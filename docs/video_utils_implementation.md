# Video Utilities Implementation Guide

This document provides detailed implementation specifications for the refactored `video_utils.py` file. This is intended for implementation in Code mode.

## Implementation Overview

The refactored `video_utils.py` will use a functional approach (no classes) while maintaining backward compatibility with existing code. All functions will include proper error handling and comprehensive documentation.

## Required Imports

```python
import os
import cv2
import shutil
import numpy as np
import subprocess
import tempfile
import torch
import imageio
from typing import Union, List, Tuple, Optional
from decord import VideoReader
from einops import rearrange
import torchvision

# Import logger
from src.utils.logger import configure_logger

# Configure logger
logger = configure_logger(__name__)
```

## Function Implementations

### 1. Core Video Functions

#### `is_corrupt(input_video_path: str) -> bool`

```python
def is_corrupt(input_video_path: str) -> bool:
    """
    Check if a video file is corrupted or unreadable.
    
    Args:
        input_video_path (str): Path to the video file to check
        
    Returns:
        bool: True if the video is corrupt, False otherwise
    """
    if not os.path.exists(input_video_path):
        logger.error(f"Video file not found: {input_video_path}")
        return True
        
    try:
        # Use FFprobe to check video integrity
        command = [
            'ffprobe', 
            '-v', 'error',
            '-select_streams', 'v:0', 
            '-show_entries', 'stream=codec_type', 
            '-of', 'default=noprint_wrappers=1:nokey=1',
            input_video_path
        ]
        
        result = subprocess.run(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=False  # Don't raise exception on non-zero return code
        )
        
        # Check if ffprobe found a valid video stream
        if result.returncode != 0 or result.stdout.strip() != 'video':
            logger.error(f"Video file is corrupt: {input_video_path}")
            return True
            
        # Also try to open with OpenCV as a second check
        cap = cv2.VideoCapture(input_video_path)
        if not cap.isOpened():
            logger.error(f"OpenCV cannot open the video file: {input_video_path}")
            cap.release()
            return True
            
        # Read a frame to ensure video is readable
        ret, _ = cap.read()
        cap.release()
        
        if not ret:
            logger.error(f"Cannot read frames from video: {input_video_path}")
            return True
            
        return False
        
    except Exception as e:
        logger.exception(f"Error checking video corruption: {e}")
        return True
```

#### `read_video(input_video_path: str) -> Union[np.ndarray, str]`

```python
def read_video(input_video_path: str) -> Union[np.ndarray, str]:
    """
    Read video frames and validate duration and FPS.
    
    Args:
        input_video_path (str): Path to the video file
        
    Returns:
        Union[np.ndarray, str]: 
            - numpy array of frames if valid
            - "duration_invalid" if duration < 5s
            - "fps_invalid" if fps <= 20
    """
    if not os.path.exists(input_video_path):
        logger.error(f"Video file not found: {input_video_path}")
        raise FileNotFoundError(f"Video file not found: {input_video_path}")
        
    try:
        # Check if file is corrupt
        if is_corrupt(input_video_path):
            logger.error(f"Cannot process corrupt video: {input_video_path}")
            raise ValueError(f"Cannot process corrupt video: {input_video_path}")
            
        # Check duration
        cap = cv2.VideoCapture(input_video_path)
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / fps if fps > 0 else 0
        cap.release()
        
        if duration < 5:
            logger.warning(f"Video duration too short ({duration:.2f}s < 5s): {input_video_path}")
            return "duration_invalid"
            
        if fps <= 20:
            logger.warning(f"Video FPS too low ({fps:.2f} <= 20): {input_video_path}")
            return "fps_invalid"
            
        # Read frames using existing function from util.py
        # We'll use the decord-based implementation
        video_frames = read_video_decord(input_video_path)
        return video_frames
        
    except Exception as e:
        logger.exception(f"Error reading video: {e}")
        raise
```

#### `read_video_decord(video_path: str, return_fps: bool = False) -> Union[np.ndarray, Tuple[np.ndarray, float]]`

```python
def read_video_decord(video_path: str, return_fps: bool = False) -> Union[np.ndarray, Tuple[np.ndarray, float]]:
    """
    Read video frames using Decord library.
    
    Args:
        video_path (str): Path to the video file
        return_fps (bool, optional): Whether to return the FPS. Defaults to False.
        
    Returns:
        Union[np.ndarray, Tuple[np.ndarray, float]]: 
            - numpy array of frames if return_fps is False
            - tuple of (frames, fps) if return_fps is True
    """
    try:
        vr = VideoReader(video_path)
        fps = vr.get_avg_fps()
        video_frames = vr[:].asnumpy()
        vr.seek(0)
        
        if return_fps:
            return video_frames, fps
        else:
            return video_frames
            
    except Exception as e:
        logger.exception(f"Error reading video with Decord: {e}")
        # Fall back to OpenCV if Decord fails
        logger.info(f"Falling back to OpenCV for video reading")
        return read_video_cv2(video_path, return_fps)
```

#### `read_video_cv2(video_path: str, return_fps: bool = False) -> Union[np.ndarray, Tuple[np.ndarray, float]]`

```python
def read_video_cv2(video_path: str, return_fps: bool = False) -> Union[np.ndarray, Tuple[np.ndarray, float]]:
    """
    Read video frames using OpenCV.
    
    Args:
        video_path (str): Path to the video file
        return_fps (bool, optional): Whether to return the FPS. Defaults to False.
        
    Returns:
        Union[np.ndarray, Tuple[np.ndarray, float]]: 
            - numpy array of frames if return_fps is False
            - tuple of (frames, fps) if return_fps is True
    """
    # Open the video file
    cap = cv2.VideoCapture(video_path)
    fps = cap.get(cv2.CAP_PROP_FPS)

    # Check if the video was opened successfully
    if not cap.isOpened():
        logger.error(f"Error: Could not open video: {video_path}")
        return np.array([])

    frames = []

    while True:
        # Read a frame
        ret, frame = cap.read()

        # If frame is read correctly ret is True
        if not ret:
            break

        # Convert BGR to RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        frames.append(frame_rgb)

    # Release the video capture object
    cap.release()

    if return_fps:
        return np.array(frames), fps
    else:
        return np.array(frames)
```

#### `write_video(video_output_path: str, video_frames: np.ndarray, fps: int) -> str`

```python
def write_video(video_output_path: str, video_frames: np.ndarray, fps: int) -> str:
    """
    Write video frames to a file.
    
    Args:
        video_output_path (str): Path where the video should be saved
        video_frames (np.ndarray): Array of frames to write
        fps (int): Frames per second for the output video
        
    Returns:
        str: Path to the saved video file
    """
    try:
        # Ensure the output directory exists
        os.makedirs(os.path.dirname(os.path.abspath(video_output_path)), exist_ok=True)
        
        # Get dimensions from the first frame
        height, width = video_frames[0].shape[:2]
        
        # Create VideoWriter object
        out = cv2.VideoWriter(
            video_output_path, 
            cv2.VideoWriter_fourcc(*"mp4v"), 
            fps, 
            (width, height)
        )
        
        # Write frames
        for frame in video_frames:
            # Convert RGB to BGR for OpenCV
            frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            out.write(frame)
            
        # Release the VideoWriter
        out.release()
        
        logger.info(f"Video successfully written to {video_output_path}")
        return video_output_path
        
    except Exception as e:
        logger.exception(f"Error writing video: {e}")
        raise
```

### 2. Video Processing Functions

#### `merge_audio(input_video_path: str, input_audio_path: str, output_path: Optional[str] = None) -> str`

```python
def merge_audio(input_video_path: str, input_audio_path: str, output_path: Optional[str] = None) -> str:
    """
    Merge audio and video using FFMPEG.
    
    Args:
        input_video_path (str): Path to the input video file
        input_audio_path (str): Path to the input audio file
        output_path (str, optional): Path for the output video file. 
            If None, a path will be generated based on the input paths.
            
    Returns:
        str: Path to the merged video file
    """
    # Check if files exist
    if not os.path.exists(input_video_path):
        raise FileNotFoundError(f"Video file not found: {input_video_path}")
    if not os.path.exists(input_audio_path):
        raise FileNotFoundError(f"Audio file not found: {input_audio_path}")
        
    # Generate output path if not provided
    if output_path is None:
        video_dir = os.path.dirname(input_video_path)
        video_name = os.path.splitext(os.path.basename(input_video_path))[0]
        audio_name = os.path.splitext(os.path.basename(input_audio_path))[0]
        output_path = os.path.join(video_dir, f"{video_name}_merged_with_{audio_name}.mp4")
        
    # Ensure output directory exists
    os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
    
    try:
        # Check if ffmpeg is installed
        check_ffmpeg_installed()
        
        # Merge audio and video
        command = [
            'ffmpeg',
            '-y',  # Overwrite output file if it exists
            '-loglevel', 'error',
            '-i', input_video_path,  # Video input
            '-i', input_audio_path,  # Audio input
            '-c:v', 'copy',          # Copy video codec
            '-c:a', 'aac',           # AAC audio codec
            '-map', '0:v',           # Use video from first input
            '-map', '1:a',           # Use audio from second input
            '-shortest',             # Finish encoding when the shortest input stream ends
            output_path
        ]
        
        subprocess.run(command, check=True)
        logger.info(f"Successfully merged video and audio: {output_path}")
        return output_path
        
    except subprocess.CalledProcessError as e:
        logger.error(f"FFMPEG error merging audio and video: {e}")
        raise RuntimeError(f"Failed to merge audio and video: {e}")
    except Exception as e:
        logger.exception(f"Error merging audio and video: {e}")
        raise
```

#### `resample(input_video_path: str, target_fps: int = 25, output_path: Optional[str] = None) -> str`

```python
def resample(input_video_path: str, target_fps: int = 25, output_path: Optional[str] = None) -> str:
    """
    Resample video to target FPS if needed.
    
    Args:
        input_video_path (str): Path to the input video file
        target_fps (int, optional): Target frame rate. Defaults to 25.
        output_path (str, optional): Path for the output video file.
            If None, a path will be generated based on the input path.
            
    Returns:
        str: Path to the resampled video file, or the original path if no resampling was needed
    """
    # Check if input file exists
    if not os.path.exists(input_video_path):
        raise FileNotFoundError(f"Video file not found: {input_video_path}")
        
    # Check current FPS
    cap = cv2.VideoCapture(input_video_path)
    current_fps = cap.get(cv2.CAP_PROP_FPS)
    cap.release()
    
    # If current FPS is already the target, return the input path
    if abs(current_fps - target_fps) < 0.01:  # Small tolerance for floating point comparison
        logger.info(f"Video already at target FPS ({target_fps}): {input_video_path}")
        return input_video_path
        
    # Generate output path if not provided
    if output_path is None:
        video_dir = os.path.dirname(input_video_path)
        video_name = os.path.splitext(os.path.basename(input_video_path))[0]
        video_ext = os.path.splitext(input_video_path)[1]
        output_path = os.path.join(video_dir, f"{video_name}_fps{target_fps}{video_ext}")
        
    # Ensure output directory exists
    os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
    
    try:
        # Check if ffmpeg is installed
        check_ffmpeg_installed()
        
        # Resample video
        command = [
            'ffmpeg',
            '-y',  # Overwrite output file if it exists
            '-loglevel', 'error',
            '-i', input_video_path,
            '-r', str(target_fps),   # Set frame rate
            '-c:v', 'libx264',       # Use H.264 codec
            '-preset', 'medium',     # Encoding preset (speed/quality tradeoff)
            '-crf', '18',            # Constant Rate Factor (quality)
            '-c:a', 'copy',          # Copy audio codec
            output_path
        ]
        
        subprocess.run(command, check=True)
        logger.info(f"Successfully resampled video to {target_fps} FPS: {output_path}")
        return output_path
        
    except subprocess.CalledProcessError as e:
        logger.error(f"FFMPEG error resampling video: {e}")
        raise RuntimeError(f"Failed to resample video: {e}")
    except Exception as e:
        logger.exception(f"Error resampling video: {e}")
        raise
```

### 3. Utility Functions

#### `check_video_fps(video_path: str) -> float`

```python
def check_video_fps(video_path: str) -> float:
    """
    Check the FPS of a video file.
    
    Args:
        video_path (str): Path to the video file
        
    Returns:
        float: The frame rate of the video
        
    Raises:
        ValueError: If the video FPS is not 25
    """
    # Check if file exists
    if not os.path.exists(video_path):
        raise FileNotFoundError(f"Video file not found: {video_path}")
        
    # Open the video and get FPS
    cam = cv2.VideoCapture(video_path)
    fps = cam.get(cv2.CAP_PROP_FPS)
    cam.release()
    
    # Check if FPS is 25
    if fps != 25:
        raise ValueError(f"Video FPS is not 25, it is {fps}. Please convert the video to 25 FPS.")
        
    return fps
```

#### `count_video_time(video_path: str) -> float`

```python
def count_video_time(video_path: str) -> float:
    """
    Calculate the duration of a video in seconds.
    
    Args:
        video_path (str): Path to the video file
        
    Returns:
        float: Duration of the video in seconds
    """
    # Check if file exists
    if not os.path.exists(video_path):
        raise FileNotFoundError(f"Video file not found: {video_path}")
        
    # Open the video and get frame count and FPS
    video = cv2.VideoCapture(video_path)
    frame_count = video.get(cv2.CAP_PROP_FRAME_COUNT)
    fps = video.get(cv2.CAP_PROP_FPS)
    video.release()
    
    # Calculate duration
    duration = frame_count / fps if fps > 0 else 0
    return duration
```

#### `gather_video_paths(input_dir: str, paths: List[str]) -> None`

```python
def gather_video_paths(input_dir: str, paths: List[str]) -> None:
    """
    Recursively gather video paths from input directory.
    
    Args:
        input_dir (str): Directory containing input videos
        paths (List[str]): List to store discovered video paths
    """
    # Check if directory exists
    if not os.path.isdir(input_dir):
        logger.error(f"Directory not found: {input_dir}")
        return
        
    # Iterate through files and subdirectories
    for file in sorted(os.listdir(input_dir)):
        file_path = os.path.join(input_dir, file)
        
        # If file is a video, add to paths
        if file.endswith(".mp4"):
            paths.append(file_path)
            
        # If file is a directory, recursively search it
        elif os.path.isdir(file_path):
            gather_video_paths(file_path, paths)
```

#### `gather_video_paths_recursively(input_dir: str) -> List[str]`

```python
def gather_video_paths_recursively(input_dir: str) -> List[str]:
    """
    Recursively gather all video paths from an input directory.
    
    Args:
        input_dir (str): Directory containing input videos
        
    Returns:
        List[str]: List of discovered video paths
    """
    logger.info(f"Recursively gathering video paths of {input_dir} ...")
    paths = []
    gather_video_paths(input_dir, paths)
    return paths
```

#### `save_videos_grid(videos: torch.Tensor, path: str, rescale: bool = False, n_rows: int = 6, fps: int = 8) -> str`

```python
def save_videos_grid(videos: torch.Tensor, path: str, rescale: bool = False, n_rows: int = 6, fps: int = 8) -> str:
    """
    Save a grid of videos as an animated GIF.
    
    Args:
        videos (torch.Tensor): Videos tensor with shape [batch, channels, frames, height, width]
        path (str): Path to save the output GIF
        rescale (bool, optional): Whether to rescale pixel values from [-1,1] to [0,1]. Defaults to False.
        n_rows (int, optional): Number of rows in the grid. Defaults to 6.
        fps (int, optional): Frames per second for the output GIF. Defaults to 8.
        
    Returns:
        str: Path to the saved GIF file
    """
    # Rearrange videos to put frames first
    videos = rearrange(videos, "b c f h w -> f b c h w")
    outputs = []
    
    # Process each frame
    for x in videos:
        # Create a grid for this frame
        x = torchvision.utils.make_grid(x, nrow=n_rows)
        x = x.transpose(0, 1).transpose(1, 2).squeeze(-1)
        
        # Rescale if requested
        if rescale:
            x = (x + 1.0) / 2.0  # -1,1 -> 0,1
            
        # Convert to uint8
        x = (x * 255).numpy().astype(np.uint8)
        outputs.append(x)

    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(os.path.abspath(path)), exist_ok=True)
    
    # Save as GIF
    imageio.mimsave(path, outputs, fps=fps)
    logger.info(f"Saved video grid to {path}")
    
    return path
```

#### `check_ffmpeg_installed() -> bool`

```python
def check_ffmpeg_installed() -> bool:
    """
    Check if FFmpeg is installed and available in the system path.
    
    Returns:
        bool: True if FFmpeg is installed, False otherwise
        
    Raises:
        FileNotFoundError: If FFmpeg is not installed
    """
    try:
        # Run the ffmpeg command with the -version argument
        result = subprocess.run(
            "ffmpeg -version", 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE, 
            shell=True
        )
        
        if result.returncode != 0:
            raise FileNotFoundError("ffmpeg not found, please install it by:\n    $ conda install -c conda-forge ffmpeg")
            
        return True
        
    except Exception as e:
        logger.error(f"Error checking FFmpeg installation: {e}")
        raise FileNotFoundError("ffmpeg not found, please install it by:\n    $ conda install -c conda-forge ffmpeg")
```

## Usage Examples

```python
# Check if a video is corrupt
is_corrupt_result = is_corrupt("path/to/video.mp4")
print(f"Video is corrupt: {is_corrupt_result}")

# Read video frames
frames = read_video("path/to/video.mp4")
print(f"Number of frames: {len(frames)}")

# Merge audio and video
merged_path = merge_audio("path/to/video.mp4", "path/to/audio.wav")
print(f"Merged video saved to: {merged_path}")

# Resample video to 25 FPS
resampled_path = resample("path/to/video.mp4")
print(f"Resampled video saved to: {resampled_path}")

# Count video duration
duration = count_video_time("path/to/video.mp4")
print(f"Video duration: {duration} seconds")

# Gather all video paths in a directory
video_paths = gather_video_paths_recursively("path/to/directory")
print(f"Found {len(video_paths)} videos")
```

## Implementation Notes

1. Error handling is implemented for all functions
2. All functions include proper logging
3. Function signatures maintain backward compatibility
4. FFMPEG operations handle command-line errors properly
5. All functions include type hints and documentation