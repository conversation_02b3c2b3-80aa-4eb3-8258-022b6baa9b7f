# Refactoring Plan for `affine_processor.py`

This document outlines the analysis of recent changes to `src/modules/affine_transform/affine_processor.py` and proposes a plan for further systematic refactoring.

## 1. Analysis of Provided `git diff`

The primary change observed in the provided `git diff` involves refactoring the `__call__` method of the `AffineProcessor` class.

**Key Changes:**

*   **Separation of Concerns:**
    *   The original `__call__` method handled logic for two distinct scenarios:
        1.  Processing a video from a file path (`video_path`).
        2.  Processing pre-loaded video frames (`video_frames` as a NumPy array).
    *   This logic has been extracted into two new private methods:
        *   `_process_for_inference(self, video_frames, return_boxes)`: Handles processing of pre-loaded frames.
        *   `_process_for_preprocessing(self, video_path)`: Handles processing from a video file path.

*   **Simplified `__call__` Method:**
    *   The `__call__` method now acts as a dispatcher, deciding which private method to call based on the provided arguments (`video_path` vs. `video_frames`).
    *   The `read_video: bool` parameter was removed from `__call__`, as its purpose is now implicitly handled by whether `video_path` or `video_frames` is supplied.

*   **Code Structure:**
    *   The core processing steps (detecting facial landmarks, applying Savitzky-Golay smoothing, and the affine transformation loop) are now present within each of the new private methods, tailored to their specific input sources.

**Benefits of this Refactoring:**

*   **Improved Clarity:** The `__call__` method is now simpler and easier to understand.
*   **Better Organization:** Separating logic for file-based and frame-based processing improves code structure.
*   **Easier Maintenance (for specific paths):** Changes to one processing path are less likely to affect the other.

## 2. Towards a More Systematic and Efficient Refactoring Approach

While the current refactoring is beneficial, further improvements can be made by adhering more strictly to the DRY (Don't Repeat Yourself) principle.

**Guiding Principle: DRY (Don't Repeat Yourself)**
Significant portions of the processing logic (smoothing, affine transformation loop) are duplicated in `_process_for_inference` and `_process_for_preprocessing`.

**Proposed Further Refinement: Extracting Common Core Logic**

Introduce a new private helper method to encapsulate the common processing pipeline once `frames` and `rect_points` are available.

**Conceptual Steps:**

*   **Step A: Identify the Common Workflow:**
    The common workflow is: `Initial Frames/Rect Points -> Smoothing -> Affine Transformation Loop -> Collect Results`.

*   **Step B: Create a Core Processing Method:**
    Define `_apply_smoothing_and_transforms(self, frames_iterable, rect_points_iterable, return_boxes_config)`:
    1.  Takes an iterable of frames (expected in BGR format from landmark detection) and an iterable of rectangle points.
    2.  Performs Savitzky-Golay smoothing on `rect_points_iterable`.
    3.  Loops through `frames_iterable` and `smoothed_rect_points`.
    4.  Calls `self._affine_transform` for each frame-point pair (which returns BGR images).
    5.  **Converts each transformed BGR frame to RGB format.**
    6.  Collects and returns transformed **RGB frames**, affine matrices, and (if `return_boxes_config` is true) boxes.

*   **Step C: Adapt Existing Methods:**
    *   `_process_for_preprocessing(self, video_path)`:
        1.  Calls `_detect_facial_landmarks_video()` to get `frames` (BGR) and `rect_points`.
        2.  Calls `_apply_smoothing_and_transforms(frames, rect_points, return_boxes_config=False)` which returns **RGB frames**.
        3.  Returns the list of **RGB frames** and affine matrices directly (assuming `write_video_ffmpeg` expects RGB).
    *   `_process_for_inference(self, video_frames, return_boxes)`:
        1.  Calls `_detect_facial_landmarks_frames()` to get `frames` (BGR) and `rect_points`.
        2.  Calls `_apply_smoothing_and_transforms(frames, rect_points, return_boxes_config=return_boxes)` which returns **RGB frames**.
        3.  Performs inference-specific output formatting (e.g., ToTensor) on the received **RGB frames**. The explicit BGR to RGB conversion step within this method is removed.

*   **Step D: Refine `__call__` Dispatcher:**
    Ensure robust error handling for invalid argument combinations (e.g., raise `ValueError`).

**Importance of Testing:**
Thorough unit and integration tests are crucial for validating these refactoring changes.

**Documentation:**
Update docstrings for all modified and new methods.

## 3. Visualizing the Proposed Refinement

```mermaid
graph TD
    subgraph EntryPoint
        P_call["__call__(video_path, video_frames, return_boxes)"]
    end

    subgraph ProcessingPaths
        direction LR
        P_pre["_process_for_preprocessing(video_path)"]
        P_inf["_process_for_inference(video_frames, return_boxes)"]
    end

    subgraph LandmarkDetection
        direction LR
        P_pre_detect["_detect_facial_landmarks_video()"]
        P_inf_detect["_detect_facial_landmarks_frames()"]
    end

    subgraph CoreLogic
        P_common_smooth["_apply_smoothing_and_transforms(frames, rect_points, return_boxes_config)"]
    end

    subgraph OutputFormatting
        direction LR
        P_pre_format["Preprocessing-Specific Output Formatting"]
        P_inf_format["Inference-Specific Output Formatting (e.g., ToTensor, RGB)"]
    end

    P_call -->|video_path provided| P_pre
    P_call -->|video_frames provided| P_inf

    P_pre --> P_pre_detect
    P_inf --> P_inf_detect

    P_pre_detect -->|frames, rect_points| P_common_smooth
    P_inf_detect -->|frames, rect_points| P_common_smooth

    P_common_smooth -->|raw_transformed_frames, matrices, boxes| P_pre_format
    P_common_smooth -->|raw_transformed_frames, matrices, boxes| P_inf_format

    P_pre_format --> P_pre
    P_inf_format --> P_inf