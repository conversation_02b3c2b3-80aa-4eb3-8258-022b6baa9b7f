# Plan: S3-Compatible Bucket Management CLI

**Version:** 1.0
**Date:** 2025-05-30

## 1. Objective

Create a Python-based Command Line Interface (CLI) tool named `s3manager` to manage credentials for multiple S3-compatible storage buckets and perform upload/download operations for files and directories. The tool will prioritize credentials from environment variables, then a user-level configuration file (default or user-specified). All core logic will reside within a `src/s3manager/` directory within the existing project structure.

## 2. Core Modules & Structure

*   **Project Root:** `/home/<USER>/HotDub-Preprocess/` (assumed)
*   **Module Directory:** `src/s3manager/`
    *   `__init__.py`
    *   `cli.py`: Main CLI entry point using the `click` library. Defines command groups and commands.
    *   `handler.py`: S3 interaction logic (e.g., `upload_file`, `download_directory`, `get_s3_client`). Will adapt relevant logic from the existing `scripts/minio_downloader.py`.
    *   `config.py`: Manages bucket configurations, credential resolution, and configuration file I/O.
    *   `utils.py` (Optional): For common helper functions, constants, or custom exceptions.

## 3. CLI Commands

All commands will be accessible via the `s3manager` entry point.

### 3.1. Global Options

*   `--config-file <PATH>`: An optional global option applicable to most commands. If provided, all operations that read from or write to a configuration file will use this specified `<PATH>`. If omitted, the default configuration file path (`~/.config/s3manager/credentials.json`) will be used.

### 3.2. Configuration Management Commands

*   **`s3manager configure`** (alias: `s3manager add-bucket`)
    *   **Description:** Adds a new bucket configuration or updates an existing one.
    *   **Options:** Accepts the global `--config-file <PATH>`.
    *   **Prompts:** Interactively for:
        *   `alias` (TEXT, required): A unique nickname for the bucket.
        *   `endpoint_url` (TEXT, required): The S3 endpoint URL.
        *   `access_key` (TEXT, required, hidden input): The S3 access key.
        *   `secret_key` (TEXT, required, hidden input): The S3 secret key.
        *   `region` (TEXT, optional): The S3 bucket region.
    *   **Action:** Saves or updates the bucket configuration in the specified (or default) configuration file using `config.save_bucket_config()`.

*   **`s3manager list-buckets`**
    *   **Description:** Lists all configured bucket aliases and their non-sensitive details.
    *   **Options:** Accepts the global `--config-file <PATH>`.
    *   **Action:** Loads configurations using `config.load_all_bucket_configs()` from the specified (or default) file and prints a formatted table (Alias, Endpoint, Region).

*   **`s3manager remove-bucket <ALIAS>`**
    *   **Description:** Removes a bucket configuration.
    *   **Arguments:** `<ALIAS>` (TEXT, required): The alias of the bucket to remove.
    *   **Options:** Accepts the global `--config-file <PATH>`.
    *   **Action:** Calls `config.remove_bucket_config(ALIAS)` for the specified (or default) file. Prompts for confirmation before deletion.

*   **`s3manager export-config <EXPORT_FILE_PATH>`**
    *   **Description:** Exports the current bucket configurations to a new file.
    *   **Arguments:** `<EXPORT_FILE_PATH>` (TEXT, required): The path where the configuration will be saved.
    *   **Options:** Accepts the global `--config-file <PATH>` (to specify the *source* configuration if not using the default).
    *   **Action:** Loads configurations from the source (specified or default) file and writes them to `<EXPORT_FILE_PATH>`.

*   **`s3manager import-config <IMPORT_FILE_PATH>`**
    *   **Description:** Imports bucket configurations from a file into the active configuration.
    *   **Arguments:** `<IMPORT_FILE_PATH>` (TEXT, required): The path to the configuration file to import.
    *   **Options:**
        *   Accepts the global `--config-file <PATH>` (to specify the *destination* configuration if not using the default).
        *   `--merge / --overwrite` (FLAG, default: `--merge`):
            *   `--merge`: If an alias from `<IMPORT_FILE_PATH>` already exists in the destination, it is skipped (or updated, TBD based on desired merge strategy - simplest is skip).
            *   `--overwrite`: If an alias from `<IMPORT_FILE_PATH>` already exists, it is overwritten in the destination.
    *   **Action:** Loads configurations from `<IMPORT_FILE_PATH>` and saves them to the destination (specified or default) configuration file, handling conflicts as per the flag.

### 3.3. File Operation Commands

*   **`s3manager upload <ALIAS> <LOCAL_PATH> <REMOTE_PATH>`**
    *   **Description:** Uploads a local file or directory to the specified S3 bucket.
    *   **Arguments:**
        *   `<ALIAS>` (TEXT, required): The alias of the configured bucket.
        *   `<LOCAL_PATH>` (Path, required): Path to the local file or directory to upload.
        *   `<REMOTE_PATH>` (TEXT, required): The S3 object key (for files) or prefix (for directories).
    *   **Options:** Accepts the global `--config-file <PATH>`.
    *   **Action:**
        1.  Resolves credentials for `<ALIAS>` using `config.get_resolved_credentials(ALIAS, config_file_path=...)`.
        2.  If credentials are not resolved, an error is displayed.
        3.  Initializes an S3 client using `handler.get_s3_client()`.
        4.  If `<LOCAL_PATH>` is a directory, calls `handler.upload_directory()`.
        5.  Else (if a file), calls `handler.upload_file()`.
        6.  Provides progress feedback during upload.

*   **`s3manager download <ALIAS> <REMOTE_PATH> <LOCAL_PATH>`**
    *   **Description:** Downloads an S3 object or "directory" (prefix) to the local filesystem.
    *   **Arguments:**
        *   `<ALIAS>` (TEXT, required): The alias of the configured bucket.
        *   `<REMOTE_PATH>` (TEXT, required): The S3 object key (for files) or prefix (for "directories").
        *   `<LOCAL_PATH>` (Path, required): The local destination path for the download.
    *   **Options:** Accepts the global `--config-file <PATH>`.
    *   **Action:**
        1.  Resolves credentials for `<ALIAS>` using `config.get_resolved_credentials(ALIAS, config_file_path=...)`.
        2.  If credentials are not resolved, an error is displayed.
        3.  Initializes an S3 client.
        4.  Determines if `<REMOTE_PATH>` refers to a single object or a prefix.
        5.  If a prefix, calls `handler.download_directory()`.
        6.  Else (if a single object), calls `handler.download_file()`.
        7.  Provides progress feedback during download.

## 4. Credential Handling Priority

The `config.get_resolved_credentials(alias, config_file_path=None)` function will resolve credentials in the following order:

1.  **Environment Variables:**
    *   Checks for environment variables with a specific naming convention:
        *   `S3MANAGER_ENDPOINT_<ALIAS_UPPERCASE>`
        *   `S3MANAGER_ACCESS_KEY_<ALIAS_UPPERCASE>`
        *   `S3MANAGER_SECRET_KEY_<ALIAS_UPPERCASE>`
        *   `S3MANAGER_REGION_<ALIAS_UPPERCASE>` (optional)
    *   If all required variables for the given `alias` are found, these are used.

2.  **Configuration File:**
    *   If environment variables are not fully set for the `alias`, the system attempts to load the configuration for `alias` from the configuration file.
    *   The path used is `config_file_path` if provided to the function, otherwise the default path (`~/.config/s3manager/credentials.json`).

3.  **Failure:**
    *   If credentials cannot be resolved for the `alias` through either method, the function returns `None` or raises a specific error (e.g., `CredentialsNotFoundError`).
    *   CLI commands (like `upload`/`download`) will handle this by informing the user that the alias is not configured or credentials are not available, and suggest using `s3manager configure` or setting environment variables. Interactive prompting for credentials will *not* occur for these operations.

## 5. Configuration File Management

*   **Default Location:** `~/.config/s3manager/credentials.json`. The `config.py` module will provide a function like `get_default_config_path()`.
*   **User-Specified Location:** Via the global `--config-file <PATH>` option.
*   **Format:** JSON. A dictionary where keys are bucket aliases. Each value is an object containing:
    ```json
    {
      "my_bucket_alias": {
        "endpoint_url": "s3.example.com",
        "access_key": "ACCESSKEYID",
        "secret_key": "SECRETACCESSKEY",
        "region": "us-east-1"
      }
      // ... more aliases
    }
    ```
*   **Permissions:** The `config.py` module will attempt to ensure the configuration file directory exists and the file itself has restricted permissions (e.g., `0600`) upon creation/modification.
*   **`.gitignore`:** The default configuration path (`~/.config/s3manager/`) is user-specific and should *not* be added to the project's `.gitignore`. Users are responsible for not committing this file if they manually copy it into a project.

## 6. `pyproject.toml` Setup

The project's `pyproject.toml` file will be updated to include:
*   **Dependencies:** `click` (for CLI framework) and `minio` (for S3 interactions).
    ```toml
    [project]
    # ... other project metadata ...
    dependencies = [
        "click>=8.0",
        "minio>=7.0",
        # ... other project dependencies ...
    ]
    ```
*   **Script Entry Point:** To make `s3manager` available as a command-line tool after package installation (`pip install .` or `pip install -e .`).
    ```toml
    [project.scripts]
    s3manager = "s3manager.cli:main_cli_group"
    ```
    (Note: The exact string `s3manager.cli:main_cli_group` assumes that the `src` directory is structured such that `s3manager` is an importable package and `main_cli_group` is the top-level `click.Group` instance in `src/s3manager/cli.py`. Adjustments may be needed based on the project's specific packaging setup and how the `src` layout is handled.)

This setup will not interfere with the execution of other Python scripts in the project like `preprocess.py`.

## 7. Conceptual Flow Diagram (Mermaid)

```mermaid
graph TD
    UserShell[User Shell] -- "s3manager [--config-file path] command ..." --> S3ManagerCLI["s3manager CLI (src/s3manager/cli.py)"];

    S3ManagerCLI -- "configure" --> ConfigOps["config.py: save_bucket_config()"];
    S3ManagerCLI -- "list-buckets" --> ConfigOps;
    S3ManagerCLI -- "remove-bucket ALIAS" --> ConfigOps;
    S3ManagerCLI -- "export-config EXPORT_PATH" --> ConfigOps;
    S3ManagerCLI -- "import-config IMPORT_PATH" --> ConfigOps;
    
    ConfigOps --> ActiveConfigFile["User-specified config.json OR ~/.config/s3manager/credentials.json"];

    S3ManagerCLI -- "upload/download ALIAS ..." --> ResolveCreds["config.py: get_resolved_credentials(ALIAS, file_path)"];
    
    subgraph "Credential Resolution for ALIAS"
        direction LR
        ResolveCreds --> CheckEnvVars{"1. Env Vars (S3MANAGER_ALIAS_*)"};
        CheckEnvVars -- Found --> UseEnvCreds["Use Env Credentials"];
        CheckEnvVars -- Not Found --> CheckConfigFile{"2. Config File"};
        CheckConfigFile --> ActiveConfigFile;
        CheckConfigFile -- Found in File --> UseFileCreds["Use File Credentials"];
        CheckConfigFile -- Not Found --> NoCreds["No Credentials Found"];
    end

    UseEnvCreds --> S3Ops["handler.py: S3 Operations"];
    UseFileCreds --> S3Ops;
    NoCreds --> ErrorMsg["Display Error: Alias/Credentials not found"];

    S3Ops --> S3Service["S3 Compatible Service"];
```

## 8. Future Considerations (Optional)

*   Encryption for credentials at rest in the config file (e.g., using `cryptography`).
*   More sophisticated merge strategies for `import-config`.
*   Support for session tokens/temporary credentials.
*   Plugin system for different storage backends (beyond S3-compatible).
*   Interactive selection for `upload`/`download` if multiple files/objects match a partial name.