# ELI5: How We Make Faces Look "Upright" in Pictures

Imagine you're looking at a photo of your friend, and their head is a little tilted. We want to write a computer program that can automatically "untilt" their head in the picture so it looks straight and their eyes are level.

**The Tricky Part: What if they're not looking straight at the camera?**

If your friend is looking right at the camera, it's kind of easy to see how to tilt their head back. But what if they're looking far off to the side (we call this an "extreme yaw")? The old way our program tried to fix the tilt would get confused. It was like trying to level a picture on the wall while looking at it from a weird angle – hard to get right!

**The Old Way (and why it sometimes messed up):**

Our program used to look at something called "3D roll." Think of it like this:
*   Imagine your friend's head is a toy car.
*   "Roll" is like when you tilt the toy car onto its side.
*   If the car (head) was facing straight towards you, figuring out its "roll" and un-rolling it worked pretty well to make the headlights (eyes) level.
*   But, if the car (head) was turned to look far to the side, just using that "roll" information didn't always make the headlights (eyes) look level *from where you were standing*. The tilt looked wrong in the flat picture.

**The New, Smarter Way (How we do it now):**

Our new method is a bit like having a super-smart understanding of how the head is actually positioned in 3D space, and then figuring out how to make it look upright in the 2D photo.

Here are the steps:

1.  **Know "Up" for a Head:**
    *   First, we imagine that every head has an invisible arrow sticking out of the top, pointing straight up from the neck towards the sky. This is the head's personal "up" direction.

2.  **See How the Head is Posed in 3D:**
    *   Our program gets very detailed information about how your friend's head is actually oriented in 3D space. This info comes from some clever math (using something called a "3D rotation matrix," let's call it `R`).
    *   This `R` is like a super-detailed instruction manual that tells us exactly how the head is tilted forward/backward (pitch), turned left/right (yaw), and twisted (roll) all at once in its 3D environment.

3.  **Find the Head's "Up" Arrow in the 3D Scene:**
    *   Using the pose instructions (`R`), we figure out exactly where that personal "up" arrow (from Step 1) is pointing in the 3D scene of the photo. Is it pointing straight up in the scene, or is it leaning because the head is tilted?

4.  **See How That "Up" Arrow Looks on the Flat 2D Photo:**
    *   Now, we take that 3D "up" arrow (which we found in Step 3) and look at how it would appear on the flat, 2D surface of the photograph.
    *   If the head was perfectly upright and facing the camera, its "up" arrow would point straight up in the photo too.
    *   But if the head is tilted or turned, that "up" arrow might look like it's leaning to the left or right on the flat photo.

5.  **Measure the Lean on the Photo:**
    *   We calculate the exact angle of this "leaning" arrow on the 2D photo. This angle tells us precisely how much the head *appears* to be tilted *in the flat picture itself*.

6.  **Untilt the Picture!**
    *   Finally, we tell the computer to rotate the picture (or sometimes just the part with the face) by the *opposite* of the angle we just measured in Step 5.
    *   If the arrow was leaning 10 degrees to the right in the photo, we rotate the photo 10 degrees to the left.
    *   And... voila! The head's personal "up" arrow now points straight up in the photo. This makes the face look upright, and the eyes should be nice and level.

**Why is this New Way Better?**

*   It doesn't get easily confused, even if the head is turned far to the side (extreme yaw).
*   It always focuses on the head's *true* "up" direction in 3D and then figures out how to make *that specific direction* appear vertical in our 2D view of the photo.
*   This means we get more consistently "straightened" faces, which is what we want!

Think of it like trying to hang a picture frame straight. Instead of just guessing, you use a spirit level. Our new method is like a very smart spirit level for faces in photos!