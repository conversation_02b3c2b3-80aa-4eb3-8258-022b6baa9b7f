# Video Preprocessing Pipeline Failure Analysis

## Summary
Attempted to implement AV1 video validation using decord's AVReader but encountered GPU-related errors during initial pipeline run. All 100 test videos were marked as corrupted due to missing CUDA dependencies.

## Key Changes Made
```python
# Updated check_video_corruption in src/utils/video_utils.py
def check_video_corruption(input_video_path: str) -> bool:
    # Check CUDA availability
    from decord import gpu, cpu
    ctx = gpu(0) if torch.cuda.is_available() else cpu(0)
    
    # Initialize AVReader with mixed context
    reader = AVReader(
        uri=input_video_path,
        ctx=ctx,
        sample_rate=16000,
        mono=True,
        num_threads=4
    )
```

## Trial Results (2025-03-20)
```text
ERROR LOGS:
[21:22:34] /github/workspace/src/runtime/c_runtime_api.cc:69: 
Check failed: allow_missing Device API gpu is not enabled.

STATS:
- Total videos processed: 100
- Valid videos: 0
- Corrupted videos: 100
- Failed stages: Corruption check
```

## Identified Issues
1. **Decord GPU Support**: 
   - Decord not built with GPU support
   - Missing CUDA 11.3+ and cuDNN dependencies
2. **Context Handling**:
   - Mixed usage of PyTorch and decord device contexts
3. **Error Propagation**:
   - No fallback to CPU decoding when GPU unavailable
4. **Dependency Management**:
   - Missing build requirements in pyproject.toml

## Proposed Solutions
1. **Decord Reinstallation**:
   ```bash
   pip uninstall decord
   pip install decord --no-binary decord --pre
   ```
2. **CUDA Toolkit Installation**:
   ```bash
   conda install -c nvidia cuda-toolkit=11.8
   ```
3. **Fallback Mechanism**:
   ```python
   try:
       # GPU attempt
   except DecordError:
       # Fallback to CPU
   ```

## Recommendations
1. **Immediate**:
   - Run pipeline in CPU-only mode with `DECORD_USE_CUDA=0`
2. **Short-term**:
   - Add dependency checks to preprocess.py startup
3. **Long-term**:
   - Create Docker image with prebuilt GPU dependencies

## Next Steps
1. Fix decord installation with GPU support
2. Update preprocessing pipeline error handling
3. Add dependency verification stage
4. Implement comprehensive logging for hardware failures

```mermaid
graph TD
    A[Failed GPU Check] --> B{Has CUDA?}
    B -->|Yes| C[Reinstall decord with GPU]
    B -->|No| D[Use CPU Context]
    C --> E[Verify Installation]
    D --> F[Continue Pipeline]
    E --> F