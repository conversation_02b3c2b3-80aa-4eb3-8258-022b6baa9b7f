# Refactoring Plan: Optional Blending in `restore_img`

The goal is to refactor the `restore_img` function in `src/modules/affine_transform/infer_utils.py` to make the blending process optional. This will allow the generation of both blended and unblended (raw pasted face) frames.

## 1. Analyze Current Blending Logic:
The blending process in the current `restore_img` function involves these key steps after the `pasted_face` (which is the transformed face applied with an initial hard mask) is computed:
    *   Calculating `w_edge` and `erosion_radius`.
    *   Further eroding `inv_mask_erosion` (on CPU) to get `inv_mask_center`.
    *   Applying Gaussian blur to `inv_mask_center` to create a `inv_soft_mask`.
    *   Using this `inv_soft_mask` to blend the `pasted_face` with the original `input_img` (tensor version).

## 2. Create a New Private Blending Method:
Extract the blending logic into a new private helper method: `_apply_blending`.

    *   **Signature:**
        `_apply_blending(self, pasted_face_chw: torch.Tensor, original_input_img_chw: torch.Tensor, inv_mask_erosion_gpu: torch.Tensor, w_edge: int) -> torch.Tensor:`
    *   **Inputs:**
        *   `pasted_face_chw`: The CHW tensor of the face pasted with the hard erosion mask.
        *   `original_input_img_chw`: The original input image, also as a CHW tensor, on the correct device.
        *   `inv_mask_erosion_gpu`: The eroded mask tensor (before CPU conversion for center finding).
        *   `w_edge`: Integer value used to determine erosion radius and blur parameters.
    *   **Operations within `_apply_blending`:**
        1.  Calculate `erosion_radius = w_edge * 2`.
        2.  Convert `inv_mask_erosion_gpu` to CPU, perform `cv2.erode` to get `inv_mask_center_cpu`.
        3.  Convert `inv_mask_center_cpu` back to a GPU tensor: `inv_mask_center_gpu`.
        4.  Calculate `blur_size` and `sigma` based on `w_edge`.
        5.  Apply `kornia.filters.gaussian_blur2d` to `inv_mask_center_gpu` to get `inv_soft_mask`.
        6.  Expand `inv_soft_mask` to match the channel dimension of `pasted_face_chw` (e.g., `inv_soft_mask_3d = inv_soft_mask.expand_as(pasted_face_chw)`).
        7.  Perform the blending: `blended_img_chw = inv_soft_mask_3d * pasted_face_chw + (1 - inv_soft_mask_3d) * original_input_img_chw`.
        8.  Return `blended_img_chw`.

## 3. Modify the `restore_img` Method:

    *   **New Signature:**
        `restore_img(self, input_img_np: np.ndarray, face: torch.Tensor, affine_matrix: torch.Tensor, apply_blending: bool = True) -> np.ndarray:`
    *   **Logic Flow:**
        1.  Perform initial steps as before: get `h, w`, process `affine_matrix`, calculate `inv_affine_matrix`, `inv_face`.
        2.  Convert `input_img_np` to `input_img_chw` tensor (moved to device, dtype set, rearranged).
        3.  Calculate `inv_mask` and then `inv_mask_erosion_gpu`.
        4.  Calculate `pasted_face_chw` (e.g., `inv_mask_erosion_t * inv_face`).
        5.  Calculate `w_edge`.
        6.  **Conditional Blending:**
            *   If `apply_blending` is `True`:
                `final_img_chw = self._apply_blending(pasted_face_chw, input_img_chw, inv_mask_erosion_gpu, w_edge)`
            *   Else (`apply_blending` is `False`):
                `final_img_chw = pasted_face_chw`
        7.  Perform final post-processing on `final_img_chw`:
            *   `rearrange` from CHW to HWC.
            *   Convert to `dtype=torch.uint8`.
            *   Move to CPU (`.cpu()`).
            *   Convert to NumPy array (`.numpy()`).
        8.  Return the final NumPy array.

## 4. Ensure Imports:
Make sure `np` (for `np.ndarray`), `torch` (for `torch.Tensor`), and any other necessary types are imported for type hinting if not already present at the top of the file.

## Mermaid Diagram of the Proposed Flow:

```mermaid
graph TD
    A[restore_img starts] --> B[Input: input_img_np, face, affine_matrix, apply_blending];
    B --> C[Preprocessing: affine_matrix, inv_affine_matrix, inv_face];
    C --> D[Convert input_img_np to input_img_chw tensor];
    D --> E[Warp mask to get inv_mask];
    E --> F[Erode inv_mask to get inv_mask_erosion_gpu];
    F --> G[Calculate pasted_face_chw = inv_mask_erosion_t * inv_face];
    G --> H[Calculate w_edge];
    H --> I{apply_blending?};
    I -- Yes --> J[Call _apply_blending(pasted_face_chw, input_img_chw, inv_mask_erosion_gpu, w_edge)];
    J --> K[Output: blended_img_chw];
    I -- No --> L[unblended_img_chw = pasted_face_chw];
    K --> M[final_img_chw];
    L --> M;
    M --> N[Postprocessing: rearrange to HWC, to_uint8, to_cpu, to_numpy];
    N --> O[Return final_img_hwc_numpy];

    subgraph _apply_blending
        P[Input: pasted_face_chw, input_img_chw, inv_mask_erosion_gpu, w_edge] --> Q[Calculate erosion_radius];
        Q --> R[CPU Erode inv_mask_erosion_gpu to get inv_mask_center_cpu];
        R --> S[Convert inv_mask_center_cpu to inv_mask_center_gpu tensor];
        S --> T[Calculate blur_size, sigma];
        T --> U[Gaussian Blur inv_mask_center_gpu to get inv_soft_mask];
        U --> V[Expand inv_soft_mask to inv_soft_mask_3d];
        V --> W[Blend: inv_soft_mask_3d * pasted_face_chw + (1-inv_soft_mask_3d) * input_img_chw];
        W --> X[Return blended_img_chw];
    end