# Improved Scope with Implementation Details

## Core Pipeline Enhancements

-   **Original:** Test scenedetect on multiple videos and try to find a solution for dark scenes.
    -   **Implementation Details:**
        -   **Library:** Utilize `PySceneDetect` (`scenedetect` CLI or Python library).
        -   **Methods:** Experiment with different detection algorithms (`detect-content`, `detect-threshold`, `detect-adaptive`).
        -   **Dark Scenes:**
            -   Tune the `threshold` parameter for `detect-threshold` or the `adaptive_threshold` for `detect-adaptive`. Lower thresholds might be needed.
            -   Consider pre-processing dark video segments (e.g., using `ffmpeg-python` or `OpenCV` for brightness/contrast adjustment) before scene detection, potentially as a separate, conditional step.
            -   Evaluate the `luma_only` option in adaptive detection.
        -   **Testing:** Create a diverse test set including videos with known dark scenes, fast cuts, and gradual transitions. Log detection results and parameters used.

-   **Original:** Remove Single face filtering, replace it with a smart active speaker detection based approach.
    -   **Implementation Details:**
        -   **Core Logic:** Leverage the output of the existing Active Speaker Detection (ASD) model (likely `talknet` based on project structure). The ASD should provide timestamps or frame ranges where speech is detected and associated with a face.
        -   **Integration:** Modify the pipeline stage currently performing single-face filtering (`src/filter_single_face.py` seems relevant). Instead of counting faces per frame, correlate the ASD output with detected face tracks.
        -   **Algorithm:**
            1.  Run face detection (e.g., S3FD) to get face bounding boxes per frame.
            2.  Run ASD (`talknet`) to get active speaker segments (timestamps + potentially face association if the model supports it).
            3.  For each frame, check if any detected face significantly overlaps with the face region associated with an *active* speaker segment from ASD at that timestamp.
            4.  Filter out frames/segments where no active speaker's face is detected.
        -   **Libraries:** `numpy` for timestamp/frame calculations, potentially `pandas` for managing time-series data if complex correlation is needed. Reuse existing face detection and ASD wrappers.
        -   **Challenges:** Accurate synchronization between video frames and audio for ASD, handling multiple speakers, defining "significant overlap", potential need for face tracking across frames if ASD output isn't frame-specific.

-   **Original:** Make Affine transform more robust by introducing a new dense landmark detector.
    -   **Implementation Details:**
        -   **Library:** Integrate `mediapipe`'s Face Mesh solution. It provides 468+ 3D face landmarks.
        -   **Integration:** Update `src/affine.py` and potentially `src/modules/affine_transform/`. Replace the current landmark detection method with calls to the `mediapipe.solutions.face_mesh` API.
        -   **Affine Calculation:** Use a selected subset of the dense landmarks (e.g., stable points around eyes, nose, mouth corners) as source points for calculating the affine transformation matrix using `cv2.getAffineTransform` or `cv2.estimateRigidTransform` / `cv2.estimateAffinePartial2D` for more robustness (using RANSAC).
        -   **Dependencies:** Add `mediapipe` to `pyproject.toml` or environment definition.
        -   **Challenges:** Performance impact of running Face Mesh on every frame (consider running at a lower frame rate if feasible), ensuring consistent landmark indices, handling occlusions or partial faces where `mediapipe` might struggle. Download and manage the `.task` model file for Face Mesh (similar to the existing `face_landmarker.task`).

## CLI Changes

-   **Original:** Transient CUI for selecting start and end stage
    -   **Implementation Details:**
        -   **Library:** Use `rich` for creating a visually appealing and interactive CUI. Alternatives include `prompt_toolkit` or standard `curses` (more complex).
        -   **Interface:**
            -   Define pipeline stages clearly (perhaps reading from a config file or hardcoded list).
            -   Present stages using `rich.prompt.Prompt` with choices or a `rich.panel.Panel` with numbered options.
            -   Prompt the user to select a start stage and then an end stage.
            -   Add validation to ensure the end stage doesn't precede the start stage.
        -   **Integration:** Modify the main script (`preprocess.py`?) entry point to display this CUI before processing starts. Pass the selected stages to the core processing logic.

-   **Original:** Take input directory as input, after creating data directory, copy the videos in input directory to 0_RAW_VIDEOS
    -   **Implementation Details:**
        -   **Library:** Use `argparse` to add a required argument (e.g., `--input-dir` or `-i`) for the source video directory.
        -   **Logic:**
            1.  Parse arguments using `argparse`.
            2.  Define the target raw video directory path (e.g., `data/0_RAW_VIDEOS`).
            3.  Use `os.makedirs(target_dir, exist_ok=True)` to create the target directory.
            4.  Use `pathlib.Path` to iterate through video files (e.g., `.mp4`, `.mov`) in the source directory.
            5.  Use `shutil.copy2(source_file, target_dir)` to copy each video file, preserving metadata.
        -   **Error Handling:** Add checks for source directory existence and read permissions, target directory write permissions.

-   **Original:** Add shorthands for arguments.
    -   **Implementation Details:**
        -   **Library:** Use `argparse`.
        -   **Method:** When defining arguments with `parser.add_argument()`, provide both the long name (e.g., `--input-dir`) and a short name (e.g., `-i`). Example: `parser.add_argument('-i', '--input-dir', required=True, help='Directory containing input videos')`.
        -   **Review:** Check existing arguments in `preprocess.py` (or relevant script) and add short versions where appropriate and non-conflicting.

-   **Original:** Add checks for credentials (also provide an option to proceed without optional credentials)
    -   **Implementation Details:**
        -   **Library:** Use `argparse` to define optional arguments for credentials (e.g., `--api-key`, `--username`). Do *not* set `required=True`.
        -   **Checking:**
            -   After parsing args, check if the optional credential arguments were provided (i.e., are not `None`).
            -   If provided, perform basic validation (e.g., check if a credential file exists, check if an API key has a plausible format - length, characters).
            -   If *required* credentials (if any) are missing, exit gracefully with an informative error message.
            -   If *optional* credentials are missing, print a warning message indicating reduced functionality and continue execution.
        -   **Storage:** Avoid passing credentials directly on the CLI if possible. Prefer environment variables (`os.getenv`), a configuration file (`configparser`, `PyYAML`), or Python's `getpass` for interactive input.

-   **Original:** Add checks for pretrained checkpoints, download them if not found. Maintain a yaml file with models and their download links
    -   **Implementation Details:**
        -   **Config File:** Create a YAML file (e.g., `models.yaml` or add to `configs/paths.yaml`) structured like:
            ```yaml
            models:
              s3fd:
                url: "http://example.com/path/to/s3fd_checkpoint.pth"
                checksum: "sha256:abcdef123..." # Optional but recommended
                path: "src/models/faceDetector/s3fd/s3fd_convert.pth" # Relative to project root
              talknet:
                url: "http://example.com/path/to/talknet.pt"
                checksum: "sha256:fedcba987..."
                path: "src/models/talknet/pretrained/talkNet.pt"
              face_mesh:
                 url: "http://example.com/path/to/face_landmarker.task"
                 checksum: "sha256:..."
                 path: "src/models/pretrained/face_landmarker.task"
              # Add other models...
            ```
        -   **Libraries:** `PyYAML` to parse the config, `requests` to download files, `hashlib` to verify checksums, `os` and `pathlib` for file path manipulation, `tqdm` (optional) for download progress bars.
        -   **Logic:**
            1.  Before initializing models that require checkpoints, parse the `models.yaml` file.
            2.  For each required model:
                a.  Construct the full expected path using the project root and the relative `path` from the YAML.
                b.  Check if the file exists using `os.path.exists()`.
                c.  If it exists and a `checksum` is provided, calculate the checksum of the local file and compare it.
                d.  If the file doesn't exist or the checksum mismatches:
                    i.  Print a message indicating download/update.
                    ii. Use `requests.get(url, stream=True)` to download the file from the `url`.
                    iii. Save the downloaded content to the specified `path`. Ensure parent directories are created (`os.makedirs(os.path.dirname(full_path), exist_ok=True)`).
                    iv. If a checksum is provided, verify the downloaded file. If verification fails, raise an error or attempt redownload.
        -   **Integration:** Call this check/download function early in the script execution or just before a specific model is loaded.