# Plan for Affine Test Script

**Goal:** Create a Python script (`tests/test_affine_workflow.py`) that processes an input video (or videos in a directory) by:
1.  Performing an affine transformation on detected faces (similar to `src/affine.py`).
2.  Applying a configurable mask to the transformed face (similar to `tests/apply_mask.py`).
3.  Performing an inverse affine transformation.
4.  Pasting the processed face back onto the original video frame.

The script will not modify existing library code but will use the components as intended.

**Detailed Steps:**

1.  **Script Setup (`tests/test_affine_workflow.py`):**
    *   **Imports:**
        *   `os`, `argparse`, `logging`
        *   `cv2` (for video I/O, image manipulation)
        *   `numpy` (for array manipulations)
        *   `torch` (potentially, if `AffineProcessor` or `InferenceUtils` require/return tensors, though they primarily use NumPy)
        *   `OmegaConf` (from `omegaconf`) to load affine configuration.
        *   `AffineProcessor` from `src.modules.affine_transform.affine_processor`
        *   `InferenceUtils` from `src.modules.affine_transform.infer_utils`
        *   `write_video_ffmpeg` from `src.utils.video_utils` (and potentially `read_video` if preferred over direct `cv2` for robustness).
        *   `glob` for finding video files.
    *   **Argument Parsing (`argparse`):**
        *   `--input_path` (str, required): Path to the input video file or a directory containing video files (e.g., `.mp4`).
        *   `--output_dir` (str, required): Directory where processed videos will be saved.
        *   `--mask_path` (str, default: `src/modules/hotdub/utils/mask.png`): Path to the mask image.
        *   `--transparency` (float, default: 0.5, range: 0.0-1.0): Transparency level for the mask.
        *   `--invert_mask_behavior` (bool, `action='store_true'`, default: False): If set, inverts the mask behavior (white areas opaque).
        *   `--affine_config_path` (str, default: `src/modules/affine_transform/config.yaml`): Path to the `config.yaml` for `AffineProcessor`.
        *   `--device` (str, default: "cpu"): Device for `AffineProcessor` (e.g., "cpu", "cuda:0").
        *   `--target_resolution` (int, default: 256): The resolution (height and width) for the affine-transformed face crop.
    *   **Logging:** Configure basic logging (e.g., `logging.basicConfig`).

2.  **Video Discovery and Iteration:**
    *   Create a main function that orchestrates the process.
    *   If `input_path` is a directory, use `glob` to find all video files (e.g., `*.mp4`).
    *   If `input_path` is a file, use it directly.
    *   Loop through each identified video file.

3.  **Per-Video Processing Loop:**
    For each `video_file_path`:
    *   **A. Initialization:**
        *   Load `AffineProcessor` config: `config = OmegaConf.load(args.affine_config_path)`.
        *   Instantiate `affine_processor = AffineProcessor(config=config, det_config=config.detector.det_config, use_onnx=config.detector.use_onnx, device=args.device)`.
        *   Instantiate `inference_utils = InferenceUtils()`.
        *   Load and preprocess the mask: Call a helper function `load_and_preprocess_mask_image(args.mask_path, args.target_resolution, args.invert_mask_behavior)`. This function will return a dictionary like `{"resized_mask_bgr": ..., "alpha_map_base": ...}`.
    *   **B. Video Reading:**
        *   Read all frames from `video_file_path`. Using `cv2.VideoCapture` frame by frame is suitable.
            ```python
            cap = cv2.VideoCapture(video_file_path)
            original_fps = cap.get(cv2.CAP_PROP_FPS)
            original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            processed_frames_list = []
            frame_idx = 0
            ```
    *   **C. Per-Frame Processing Sub-Loop:**
        While `cap.isOpened()`:
        `ret, original_frame = cap.read()`
        If not `ret`, break.

        1.  **Forward Affine Transformation:**
            *   Call `transformed_output = affine_processor.process_frame(original_frame, frame_idx, return_boxes=True, output_resolution=args.target_resolution)`.
            *   This is expected to return `(transformed_face_crop, affine_matrix_M1, bounding_box_coords)` or `None` if detection fails. `transformed_face_crop` will be a NumPy array of shape `(target_resolution, target_resolution, 3)`. `bounding_box_coords` will be `[x1, y1, x2, y2]`.
        2.  **Handle No-Face Detection:**
            *   If `transformed_output` is `None` or `bounding_box_coords` is invalid:
                *   `logging.warning(f"No face detected in frame {frame_idx} of {video_file_path}.")`
                *   `processed_frames_list.append(original_frame.copy())`
                *   `frame_idx += 1`
                *   Continue to the next frame.
            *   Else: `transformed_face_crop, affine_matrix_M1, bounding_box_coords = transformed_output`
        3.  **Apply Mask:**
            *   Call a helper function `masked_transformed_crop = apply_overlay_mask(transformed_face_crop, mask_data, args.transparency)`.
        4.  **Prepare for Inverse Affine (Resize Processed Crop):**
            *   `x1, y1, x2, y2 = bounding_box_coords`
            *   `original_face_width = int(x2 - x1)`
            *   `original_face_height = int(y2 - y1)`
            *   If `original_face_width <= 0` or `original_face_height <= 0`:
                *   `logging.warning(f"Invalid bounding box for frame {frame_idx}. Skipping paste-back.")`
                *   `processed_frames_list.append(original_frame.copy())`
            *   Else:
                *   `resized_masked_crop = cv2.resize(masked_transformed_crop, (original_face_width, original_face_height), interpolation=cv2.INTER_LANCZOS4)` (or `cv2.INTER_AREA`).
        5.  **Inverse Affine & Paste Back:**
            *   `final_frame = inference_utils.restore_img(original_frame.copy(), resized_masked_crop, affine_matrix_M1)`
            *   `processed_frames_list.append(final_frame)`
        `frame_idx += 1`
    *   **D. Video Writing:**
        *   `cap.release()`
        *   If `processed_frames_list` is not empty:
            *   Construct output path: `base_name = os.path.splitext(os.path.basename(video_file_path))[0]`
            *   `output_video_path = os.path.join(args.output_dir, f"{base_name}_affine_test.mp4")`
            *   Ensure `args.output_dir` exists: `os.makedirs(args.output_dir, exist_ok=True)`.
            *   Import `write_video_ffmpeg` from `src.utils.video_utils`.
            *   Convert `processed_frames_list` (which contains OpenCV BGR frames) to a NumPy array of frames if it isn't already, ensuring correct format for `write_video_ffmpeg`.
            *   Call `write_video_ffmpeg(output_video_path, np.array(processed_frames_list), fps=original_fps)`.
            *   `logging.info(f"Processed video saved to: {output_video_path}")`
    *   **E. Cleanup (Optional but good practice):**
        *   `affine_processor.reset_state()` (if such a method exists and is needed).

4.  **Helper Functions:**
    *   **`load_and_preprocess_mask_image(mask_path, target_resolution_tuple, invert_alpha_logic)`:**
        *   Adapts logic from `load_and_preprocess_mask` in `tests/apply_mask.py`.
        *   Input: mask file path, target resolution (e.g., `(256, 256)`), boolean for inversion.
        *   Output: Dictionary `{"resized_mask_bgr": ..., "alpha_map_base": ...}`.
    *   **`apply_overlay_mask(frame_to_mask, mask_data, transparency)`:**
        *   Adapts blending logic from `process_single_video` in `tests/apply_mask.py`.
        *   Input: NumPy array `frame_to_mask` (e.g., the `transformed_face_crop`), `mask_data` (output from previous helper), transparency value.
        *   Output: NumPy array `blended_frame`.

**Workflow Diagram (Mermaid):**

```mermaid
graph TD
    A[Start Script] --> B(Parse Arguments);
    B --> C{Input is Dir?};
    C -- Yes --> D[Discover Video Files in Dir];
    C -- No --> E[Use Single Video File Path];
    D --> F[For Each Video File];
    E --> F;

    subgraph Process Single Video
        direction TB
        F_Start(( )) --> G[Init AffineProcessor, InferenceUtils];
        G --> H[Load & Preprocess Mask Image];
        H --> I[Open Video (cv2.VideoCapture)];
        I --> J[For Each Frame in Video];

        subgraph Process Frame
            direction TB
            J_FrameStart(( )) --> K[Read Original Frame];
            K --> L[AffineProcessor.process_frame(original_frame, target_res, return_boxes=True)];
            L --> M{Face Detected? (transformed_crop, M1, box)};
            M -- No --> N[Log Warning, Use Original Frame];
            M -- Yes --> O[Apply Overlay Mask to transformed_crop];
            O --> P[Resize masked_crop to original face size (using box)];
            P --> Q[InferenceUtils.restore_img(original_frame, resized_masked_crop, M1)];
            Q --> R[Store Final Frame];
            N --> R;
        end
        J --> J_FrameEnd(( ));
        J_FrameEnd --> S{All Frames Done?};
        S -- No --> J;
        S -- Yes --> T[Release Video Capture];
        T --> U[Write Processed Frames to Output Video File using write_video_ffmpeg];
        U --> F_End(( ));
    end
    F --> V{All Videos Done?};
    V -- Yes --> W[End Script];
    V -- No --> F;