- Modify def verify_video_fps_25(video_path: str) to take in fps as an input, instead of just verifying for 25 FPS but keep 25 as default value of FPS
- In load_and_validate_video use def get_video_duration(video_path: str, delete: bool = False) to check duration. use verify_video_fps_25 to check for fps
- In resample_video_fps use verify_video_fps_25 to check for fps
- In save_video_frames use ffmpeg instead of opencv with highest quality settings with most comaptible encoding