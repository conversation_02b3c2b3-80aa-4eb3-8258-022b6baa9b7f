# Audio and Video Utilities Refactoring Plan

## 1. Current State Analysis

```mermaid
graph TD
    A[src/utils/video_utils.py] -->|Placeholder functions| B[Utils Class]
    A -->|Placeholder functions| C[Processor Class]
    D[src/utils/audio_utils.py] -->|Mix of implemented and<br>placeholder functions| E[Utils Class]
    D -->|Implemented functions| F[Processor Class]
    G[src/utils/util.py] -->|Fully implemented<br>audio/video functions| H[Duplicates functionality<br>in other utils files]
    I[src/af.py] -->|Imports| G
```

### Issues Identified:
1. **Function Duplication**: Similar functions exist across multiple files
2. **Inconsistent Implementation**: Mix of placeholder and implemented functions
3. **Class Structure**: Current utils use classes unnecessarily
4. **Missing Error Handling**: Incomplete error handling in some functions

## 2. Refactoring Approach

### 2.1 Key Principles
- Convert to functional approach (standalone functions)
- Maintain existing function signatures for backward compatibility
- Implement placeholder functions based on docstring comments
- Move functions from util.py to appropriate utility files
- Add proper error handling and logging

### 2.2 File Restructuring

```mermaid
graph LR
    A[video_utils.py] -->|Contains| B[All video-related functions]
    C[audio_utils.py] -->|Contains| D[All audio-related functions]
    E[util.py] -->|Keeps only| F[Non-audio/video utilities]
```

## 3. Implementation Plan

### 3.1 `video_utils.py` Refactoring

```python
# Functions to implement:
def is_corrupt(input_video_path: str) -> bool:
    """Check if video file is corrupted"""
    
def read_video(input_video_path: str):
    """Read video frames, validate duration and FPS"""
    
def merge_audio(input_video_path: str, input_audio_path: str) -> str:
    """Merge audio and video using FFMPEG"""
    
def resample(input_video_path: str) -> str:
    """Resample video to 25fps if needed"""

# Functions to migrate from util.py:
def read_video_decord(video_path: str, return_fps=False)
def read_video_cv2(video_path: str, return_fps=False)
def write_video(video_output_path: str, video_frames: np.ndarray, fps: int)
def check_video_fps(video_path: str)
def count_video_time(video_path)
def gather_video_paths(input_dir, paths)
def gather_video_paths_recursively(input_dir)
def save_videos_grid(videos, path, rescale, n_rows, fps)
def check_ffmpeg_installed()
```

### 3.2 `audio_utils.py` Refactoring

```python
# Functions to implement:
def extract_audio(input_video_path: str) -> str:
    """Extract audio from video in .wav format using FFMPEG"""

# Keep existing implementations:
def speaker_count(audio_file_path: str, device=None) -> int:
    """Count unique speakers in audio file"""
    
def enhance(audio_file_path: str) -> List[str]:
    """Enhance audio quality using separator"""
    
# Functions to migrate from util.py:
def read_audio(audio_path: str, audio_sample_rate: int = 16000)
def make_audio_window(audio_embeddings: torch.Tensor, window_size: int)
```

## 4. Migration Strategy

```mermaid
graph TD
    A[Stage 1: Implementation] --> B[Implement all functions<br>in video_utils.py and audio_utils.py]
    B --> C[Add proper error handling<br>and logging]
    C --> D[Add comprehensive docstrings]
    
    D --> E[Stage 2: Integration]
    E --> F[Identify all imports<br>from util.py]
    F --> G[Update imports to use<br>new locations]
    G --> H[Test to ensure<br>no functionality breaks]
    
    H --> I[Stage 3: Cleanup]
    I --> J[Remove duplicated functions<br>from util.py]
    J --> K[Update all code that uses<br>the old functions]
```

## 5. Implementation Details

### 5.1 Error Handling
- Use logger from `src.utils.logger` for consistent error reporting
- Consistent approach to error types and messages
- Proper validation of input parameters

### 5.2 FFMPEG Integration
- Check for FFMPEG installation
- Handle FFMPEG errors gracefully
- Use subprocess with proper error capture

### 5.3 Performance Considerations
- Efficient handling of large video files
- Proper memory management
- Temporary file cleanup

## 6. Implementation Specifications

### 6.1 `video_utils.py` Implementation

#### `is_corrupt(input_video_path)`
- Use FFprobe to check if the video file can be properly read
- Check for basic metadata (streams, duration, etc.)
- Return boolean indicating if the file is corrupt

#### `read_video(input_video_path)`
- Validate the video duration (minimum 5 seconds)
- Validate FPS (minimum 20 FPS)
- Return appropriate error strings when conditions aren't met
- Return numpy array of frames when valid

#### `merge_audio(input_video_path, input_audio_path)`
- Create output filename based on input paths
- Use FFMPEG to merge video and audio tracks
- Handle error cases gracefully
- Return path to merged video file

#### `resample(input_video_path)`
- Check current FPS
- If not 25 FPS, use FFMPEG to resample
- Create appropriate output filename
- Return path to resampled video

### 6.2 `audio_utils.py` Implementation

#### `extract_audio(input_video_path)`
- Generate appropriate output filename
- Use FFMPEG to extract audio in WAV format
- Handle error cases gracefully
- Return path to extracted audio file

## 7. Recommendations

1. **Phased Implementation**: Implement one file at a time to limit potential issues
2. **Testing**: Test each function independently before integrating
3. **Documentation**: Add comprehensive docstrings for all functions
4. **Version Control**: Consider creating a branch for this refactoring
5. **Backward Compatibility**: Maintain function signatures to avoid breaking existing code

## 8. Next Steps

1. Implement the refactored `video_utils.py`
2. Implement the refactored `audio_utils.py` 
3. Update imports in dependent files
4. Test thoroughly
5. Clean up duplicated functionality in `util.py`