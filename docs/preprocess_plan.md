# Video Preprocessing Pipeline Design - Revised

## Pipeline Overview
```mermaid
graph TD
    A[RAW_VIDEOS] --> B[Check Corruption]
    B --> C{Good?}
    C -->|Yes| D[Generate Shots]
    C -->|No| X[Reject]
    D --> E[SHOTS]
    E --> F[Filter Single Face]
    F --> G[SINGLE_FACE]
    G --> H[Resample FPS/Hz]
    H --> I[RESAMPLED]
    I --> J[<PERSON><PERSON>]
    J --> K[DENOISED]
    K --> L[ASD Processing]
    L --> M[VALID]

    classDef dir fill:#e6f3ff,stroke:#004c99;
    class A,B,C,D,E,F,G,H,I,J,K,L,M dir
```

## Revised Directory Structure
```bash
HotDub-Preprocess/
└── data/
    ├── RAW_VIDEOS/      # Initial input videos
    ├── GOOD/            # Passed corruption check
    ├── SHOTS/           # Video scene segments  
    ├── SINGLE_FACE/     # Videos with single face
    ├── RESAMPLED/       # Uniform FPS/Hz videos
    ├── DENOISED/        # Denoised videos
    └── VALID/           # Final validated videos
```

## Updated Implementation Notes:
1. All pipeline directories will be created under `data/`
2. Path handling in code will use `os.path.join('data', 'RAW_VIDEOS')` etc.
3. Existing module paths will be updated to match new structure
4. File movement operations will preserve relative paths within `data/`

Should I proceed with creating the markdown file with these updates and then transition to Code mode?