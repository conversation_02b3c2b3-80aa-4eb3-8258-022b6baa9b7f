#!/usr/bin/env python3
import hashlib
import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import List, Optional

import requests
from dotenv import load_dotenv
from minio import Minio
from minio.error import S3Error

# Configure logging
load_dotenv(override=True)

logging.basicConfig(
    level=os.getenv("LOG_LEVEL", "INFO"),
    format=f"NODE {os.getenv('NODE_INDEX', '?')} | %(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


def send_slack_notification(
    webhook_url: str, success: bool = True, message: Optional[str] = None
):
    """
    Send training completion notification via Slack

    Args:
        webhook_url: Slack webhook URL for posting messages
        success: Whether training completed successfully
        message: Optional message to include
    """
    if not webhook_url:
        print("Slack webhook URL not provided")
        return

    status = "✅ completed successfully" if success else "❌ failed"
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    payload = {"text": f"Preprocessing {status} at {timestamp}"}

    if message:
        payload["text"] += f"\nMessage: {message}"

    try:
        response = requests.post(webhook_url, json=payload)
        response.raise_for_status()
        print("Slack notification sent successfully")
    except Exception as e:
        print(f"Error sending Slack notification: {str(e)}")


class NodeWorker:
    """Handles video processing for a single node's assigned range"""

    def __init__(self):
        self.node_index = int(os.getenv("NODE_INDEX", 0))
        self.total_nodes = int(os.getenv("TOTAL_NODES", 1))
        self.minio_client = self._init_minio_client()
        self.bucket_name = "d-gpu-5a8f596d-2951-4ad7-bd6c-ee25be0fc91a"
        self.video_prefix = "Stage-6/"

    def _init_minio_client(self):
        """Initialize MinIO client with credentials"""
        return Minio(
            "objectstore.e2enetworks.net",
            access_key=os.getenv("E2E_ACCESS_KEY"),
            secret_key=os.getenv("E2E_SECRET_KEY"),
            secure=True,
        )

    def get_assigned_videos(self) -> List[str]:
        """Get sorted list of all videos and return assigned subset"""
        try:
            # Get all objects and sort for consistent ordering
            objects = list(
                self.minio_client.list_objects(
                    self.bucket_name, prefix=self.video_prefix, recursive=True
                )
            )
            objects.sort(key=lambda x: x.object_name)

            # Calculate assigned range
            total_videos = len(objects)

            print(f"Total videos: {total_videos}")
            print(f"Node index: {self.node_index}")
            print(f"Total nodes: {self.total_nodes}")

            per_node = total_videos // self.total_nodes
            remainder = total_videos % self.total_nodes

            start_idx = self.node_index * per_node + min(self.node_index, remainder)
            end_idx = start_idx + per_node + (1 if self.node_index < remainder else 0)

            logger.info(f"Assigned videos {start_idx}-{end_idx} of {total_videos}")
            return [obj.object_name for obj in objects[start_idx:end_idx]]

        except S3Error as e:
            logger.error(f"MinIO error: {e}")
            raise
        except Exception as e:
            logger.error(f"Failed to list objects: {e}")
            raise

    def download_videos(self, video_paths: List[str]) -> int:
        """Download assigned videos with checksum verification"""
        os.makedirs("data/6_VALID", exist_ok=True)
        success_count = 0

        for video_path in video_paths:
            local_path = f"data/6_VALID/{os.path.basename(video_path)}"
            temp_path = f"{local_path}.tmp"

            try:
                # Download with temporary file
                self.minio_client.fget_object(self.bucket_name, video_path, temp_path)

                # Verify download
                if self._verify_download(video_path, temp_path):
                    os.rename(temp_path, local_path)
                    success_count += 1
                    logger.info(f"Verified download: {video_path}")
                else:
                    logger.warning(f"Checksum mismatch: {video_path}")
                    os.remove(temp_path)

            except Exception as e:
                logger.error(f"Failed to download {video_path}: {e}")
                if os.path.exists(temp_path):
                    os.remove(temp_path)

        return success_count

    def _verify_download(self, remote_path: str, local_path: str) -> bool:
        """Compare MinIO ETag with local file checksum"""
        # try:
        #     # Get remote checksum (MinIO uses MD5 as ETag)
        #     obj_info = self.minio_client.stat_object(self.bucket_name, remote_path)
        #     remote_checksum = obj_info.etag.strip('"')

        #     # Calculate local checksum
        #     with open(local_path, "rb") as f:
        #         local_checksum = hashlib.md5(f.read()).hexdigest()

        #     return remote_checksum == local_checksum

        # except Exception as e:
        #     logger.error(f"Verification failed for {remote_path}: {e}")
        #     return False
        return True

    def run_processing_pipeline(self):
        """Execute the video processing pipeline"""
        from preprocess import PreprocessPipeline

        try:
            pipeline = PreprocessPipeline(base_dir="data")
            pipeline.run_pipeline(
                start_stage="affine",
                end_stage="quality",
                num_workers=os.cpu_count() // 2,
            )
            return True
        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            return False

    def upload_results(self):
        """Upload processed videos back to storage"""
        # Implementation would go here
        pass

    def run(self):
        """Main execution flow for the node"""
        logger.info(f"Starting node worker {self.node_index}/{self.total_nodes}")

        try:
            # 1. Get assigned work
            video_paths = self.get_assigned_videos()
            if not video_paths:
                logger.error("No videos assigned to this node")
                return False

            # 2. Download videos
            download_count = self.download_videos(video_paths)
            logger.info(f"Downloaded {download_count}/{len(video_paths)} videos")

            # 3. Process pipeline
            if not self.run_processing_pipeline():
                message = f"Node {self.node_index} failed to process videos"
                send_slack_notification(
                    webhook_url=os.getenv("SLACK_WEBHOOK_URL"),
                    success=False,
                    message=message,
                )
                return False

            message = (
                f"Node {self.node_index} completed processing {download_count} videos"
            )

            send_slack_notification(
                webhook_url=os.getenv("SLACK_WEBHOOK_URL"),
                success=True,
                message=message,
            )

            # 4. Upload results
            self.upload_results()

            return True

        except Exception as e:
            logger.error(f"Node worker failed: {e}")
            return False


if __name__ == "__main__":
    worker = NodeWorker()
    success = worker.run()
    sys.exit(0 if success else 1)
