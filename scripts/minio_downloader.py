import os
import logging
import argparse
from minio import Minio
from minio.error import S3Error
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()],
)


def download_object(client, bucket_name, object_name, local_file_path):
    """
    Downloads a single object from MinIO to a local file path.
    Ensures the local directory structure exists.
    """
    try:
        # Ensure the local directory for the file exists
        local_dir = os.path.dirname(local_file_path)
        if local_dir:  # Create directory only if it's not the root
            os.makedirs(local_dir, exist_ok=True)

        client.fget_object(bucket_name, object_name, local_file_path)
        logging.info(f"Successfully downloaded: {object_name} to {local_file_path}")
        return True
    except S3Error as s3_err:
        logging.error(f"S3 Error downloading {object_name}: {s3_err}")
    except Exception as e:
        logging.error(
            f"Failed to download {object_name} to {local_file_path}: {str(e)}"
        )
    return False


def main():
    # Load environment variables from .env file
    load_dotenv()

    parser = argparse.ArgumentParser(
        description="Download files or folders from MinIO S3 bucket."
    )
    parser.add_argument(
        "s3_path", help="The S3 path (object key or prefix) to the file or folder."
    )
    parser.add_argument(
        "output_dir", help="The local directory where files will be downloaded."
    )

    args = parser.parse_args()

    # Get MinIO credentials and bucket name from environment variables
    access_key = os.getenv("E2E_ACCESS_KEY")
    secret_key = os.getenv("E2E_SECRET_KEY")
    bucket_name = os.getenv("E2E_BUCKET_NAME")
    minio_endpoint = "objectstore.e2enetworks.net"  # As per previous script

    if not access_key or not secret_key:
        logging.error(
            "E2E_ACCESS_KEY or E2E_SECRET_KEY not found in .env file or environment."
        )
        return
    if not bucket_name:
        logging.error("E2E_BUCKET_NAME not found in .env file or environment.")
        return

    # Initialize MinIO client
    try:
        client = Minio(
            minio_endpoint,
            access_key=access_key,
            secret_key=secret_key,
            secure=True,  # Assuming secure connection as in download_random.py
        )
    except Exception as e:
        logging.error(f"Failed to initialize MinIO client: {e}")
        return

    # Ensure output directory exists
    try:
        os.makedirs(args.output_dir, exist_ok=True)
    except PermissionError:
        logging.error(f"Permission denied creating output directory: {args.output_dir}")
        return
    except Exception as e:
        logging.error(f"Error creating output directory {args.output_dir}: {e}")
        return

    s3_path_to_check = args.s3_path
    is_definitely_folder = s3_path_to_check.endswith("/")

    objects_at_path = []
    try:
        # Try to list the specific object or objects with the prefix
        # If s3_path is "folder/file.txt", list_objects with prefix "folder/file.txt" will return that object if it exists.
        # If s3_path is "folder/", list_objects with prefix "folder/" will return objects in that folder.
        listed_objects = list(
            client.list_objects(bucket_name, prefix=s3_path_to_check, recursive=False)
        )

        # Filter out the potential "directory" object itself if the path was a prefix ending in /
        # For example, if s3_path is "foo/", list_objects might return an object "foo/" which is_dir.
        # We are interested in actual files or contents *within* "foo/".
        if is_definitely_folder:
            objects_at_path = [
                obj
                for obj in listed_objects
                if obj.object_name != s3_path_to_check or not obj.is_dir
            ]
        else:  # If not ending with /, it could be a file or a prefix without a trailing slash
            objects_at_path = listed_objects

    except S3Error as e:
        logging.error(
            f"S3 error when trying to determine path type for '{s3_path_to_check}': {e}"
        )
        return
    except Exception as e:
        logging.error(
            f"Unexpected error when trying to determine path type for '{s3_path_to_check}': {e}"
        )
        return

    download_count = 0
    total_files_to_download = 0

    # Scenario 1: s3_path is a specific file
    # Check if exactly one object matches the s3_path and it's not a directory marker
    if (
        not is_definitely_folder
        and len(objects_at_path) == 1
        and objects_at_path[0].object_name == s3_path_to_check
        and not objects_at_path[0].is_dir
    ):
        obj_to_download = objects_at_path[0]
        logging.info(f"'{s3_path_to_check}' identified as a single file.")
        # For a single file, download it directly into output_dir, not a subfolder based on its S3 path.
        # Or, if we want to preserve the last part of the path:
        file_name = os.path.basename(obj_to_download.object_name)
        local_file_path = os.path.join(args.output_dir, file_name)
        total_files_to_download = 1
        if download_object(
            client, bucket_name, obj_to_download.object_name, local_file_path
        ):
            download_count += 1
    else:
        # Scenario 2: s3_path is a folder/prefix (or a file that wasn't matched above, treat as prefix)
        logging.info(
            f"'{s3_path_to_check}' identified as a folder/prefix. Downloading contents recursively."
        )

        # Ensure the prefix for stripping ends with a slash for correct relative path calculation
        prefix_to_strip = s3_path_to_check
        if not prefix_to_strip.endswith("/"):
            # If s3_path was "foo/bar" (a file) but we are treating it as a prefix "foo/bar/"
            # or if it was "foo" (a prefix) we want "foo/"
            # We need to be careful not to strip "foo/bar" from "foo/bar/baz.txt" if s3_path was "foo/bar" (file)
            # The logic here is: if it's not definitely a folder, we assume it's a prefix.
            # If the original s3_path was "a/b/c.txt" and it wasn't caught as a single file,
            # then listing with prefix "a/b/c.txt" recursively might find "a/b/c.txt/d.txt" (unlikely but possible).
            # A more robust way for folder download is to always ensure the prefix for listing ends with '/' if it's meant to be a folder.
            # For this script, if it's not a single file, we treat s3_path as a prefix.
            if (
                not is_definitely_folder
                and len(objects_at_path) > 0
                and any(
                    obj.object_name.startswith(s3_path_to_check + "/")
                    for obj in client.list_objects(
                        bucket_name, prefix=s3_path_to_check + "/", recursive=False
                    )
                )
            ):
                prefix_to_strip += "/"  # It's a prefix that needs a slash
            elif not prefix_to_strip.endswith("/"):
                prefix_to_strip += "/"

        all_objects_in_prefix = []
        try:
            # List all objects recursively under the determined prefix
            # If s3_path was "folder/file.txt" and it's treated as a prefix, this will list objects starting with "folder/file.txt"
            # If s3_path was "folder/", this will list objects in "folder/"
            iterable_objects = client.list_objects(
                bucket_name, prefix=prefix_to_strip, recursive=True
            )
            all_objects_in_prefix = [
                obj for obj in iterable_objects if not obj.is_dir
            ]  # Filter out directory markers
        except S3Error as e:
            logging.error(
                f"S3 error listing objects for prefix '{prefix_to_strip}': {e}"
            )
            return
        except Exception as e:
            logging.error(
                f"Unexpected error listing objects for prefix '{prefix_to_strip}': {e}"
            )
            return

        if not all_objects_in_prefix:
            logging.warning(
                f"No downloadable files found under prefix '{prefix_to_strip}' in bucket '{bucket_name}'."
            )

        total_files_to_download = len(all_objects_in_prefix)
        for obj in all_objects_in_prefix:
            # Calculate relative path to maintain folder structure
            # Example: s3_path="foo/", obj.object_name="foo/bar/baz.txt" -> relative_path="bar/baz.txt"
            # Example: s3_path="foo/bar.txt" (treated as prefix "foo/bar.txt/"), obj.object_name="foo/bar.txt/qux.dat" -> relative_path="qux.dat"

            relative_path = obj.object_name
            if obj.object_name.startswith(prefix_to_strip):
                relative_path = obj.object_name[len(prefix_to_strip) :]

            relative_path = relative_path.lstrip(
                "/"
            )  # Ensure no leading slash if prefix_to_strip was empty or root

            if not relative_path:  # Skip if it's the prefix itself (e.g. a directory marker that wasn't filtered)
                continue
            local_file_path = os.path.join(args.output_dir, relative_path)
            if download_object(client, bucket_name, obj.object_name, local_file_path):
                download_count += 1

    # This logging should be outside the loop but inside the main function, after all processing.
    if total_files_to_download > 0:
        logging.info(
            f"Download process finished. Successfully downloaded {download_count}/{total_files_to_download} files."
        )
    # Avoid logging "No files targeted" if it was a single file that was successfully downloaded or failed.
    # This condition checks if we weren't in the single-file download scenario OR if we were but it didn't result in total_files_to_download > 0
    elif not (
        not is_definitely_folder
        and len(objects_at_path) == 1
        and objects_at_path[0].object_name == s3_path_to_check
        and not objects_at_path[0].is_dir
        and total_files_to_download > 0
    ):
        if not (
            total_files_to_download > 0
        ):  # Only log if no files were actually processed for download
            logging.info(
                "No files were targeted for download based on the provided S3 path, or no files found at the prefix."
            )


if __name__ == "__main__":
    main()
