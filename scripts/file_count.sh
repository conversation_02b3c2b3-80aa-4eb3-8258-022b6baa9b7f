#!/bin/bash

# Script to count number of .mp4 files in each subdirectory of data/

# Function to print formatted header
print_header() {
    printf "%-30s | %s\n" "Directory" "MP4 Count"
    printf "%s\n" "----------------------------------------"
}

# Get the absolute path to the data directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DATA_DIR="$(cd "$SCRIPT_DIR/../data" && pwd)"

# Check if data directory exists
if [ ! -d "$DATA_DIR" ]; then
    echo "Error: data directory not found"
    exit 1
fi

# Print header
print_header

# Process each non-hidden directory in data/
for dir in "$DATA_DIR"/*/; do
    # Skip if not a directory or is hidden
    if [ ! -d "$dir" ] || [[ $(basename "$dir") == .* ]]; then
        continue
    fi
    
    # Count .mp4 files in directory (including symbolic links)
    count=$(find -L "$dir" -type f -name "*.mp4" | wc -l)
    
    # Format directory name for display
    dir_name=$(basename "$dir")
    
    # Print result in table format
    printf "%-30s | %d\n" "$dir_name" "$count"
done