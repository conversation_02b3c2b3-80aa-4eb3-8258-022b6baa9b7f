import csv
import subprocess
import os
import sys
import shutil
from pathlib import Path
import random
import argparse
from multiprocessing import Pool, cpu_count
import time
import signal
import psutil
from contextlib import contextmanager


@contextmanager
def timeout_handler(seconds):
    """Context manager for handling timeouts"""

    def timeout_signal_handler(signum, frame):
        raise TimeoutError(f"Operation timed out after {seconds} seconds")

    # Set the signal handler
    old_handler = signal.signal(signal.SIGALRM, timeout_signal_handler)
    signal.alarm(seconds)

    try:
        yield
    finally:
        # Restore the old signal handler
        signal.signal(signal.SIGALRM, old_handler)
        signal.alarm(0)


def kill_process_tree(pid):
    """Kill a process and all its children"""
    try:
        parent = psutil.Process(pid)
        children = parent.children(recursive=True)
        for child in children:
            try:
                child.kill()
            except psutil.NoSuchProcess:
                pass
        try:
            parent.kill()
        except psutil.NoSuchProcess:
            pass
    except psutil.NoSuchProcess:
        pass


def download_and_process_videos(
    csv_file, output_dir="output", temp_dir="temp", n_samples=None, max_retries=2
):
    """
    Download YouTube videos and extract time segments with improved error handling

    Args:
        csv_file: Path to CSV file with format: YouTube ID, start, end
        output_dir: Directory to save final video segments
        temp_dir: Directory for temporary full video downloads
        n_samples: Number of random samples to download (None for all)
        max_retries: Maximum number of retry attempts per video
    """
    # Create directories if they don't exist
    Path(output_dir).mkdir(exist_ok=True, parents=True)
    Path(temp_dir).mkdir(exist_ok=True, parents=True)

    try:
        with open(csv_file, "r") as file:
            reader = csv.reader(file)
            rows = list(reader)
            if rows and rows[0][0].lower() == "youtube_id":
                header = rows.pop(0)
            else:
                header = []

            total_rows = len(rows)
            print(f"Total rows in CSV (excluding header): {total_rows}")

            # Apply random sampling if requested
            if n_samples is not None and n_samples > 0:
                n_samples = min(n_samples, total_rows)
                print(f"Randomly selecting {n_samples} samples")
                rows = random.sample(rows, n_samples)
            else:
                print("Processing all samples")

            # Prepare arguments for parallel processing
            tasks = []
            for row_idx, row in enumerate(rows):
                if len(row) < 3:
                    continue
                youtube_id, start_time, end_time = row[:3]
                tasks.append(
                    (
                        youtube_id,
                        start_time,
                        end_time,
                        output_dir,
                        temp_dir,
                        row_idx,
                        max_retries,
                    )
                )

            # Use fewer processes to avoid overwhelming the system
            num_processes = max(1, min(2, int(cpu_count() * 0.5)))
            print(f"Starting parallel processing with {num_processes} workers")

            with Pool(processes=num_processes) as pool:
                results = pool.starmap(process_single_video, tasks)

                success_count = sum(results)
                print(f"\nProcessing complete! Success: {success_count}/{len(tasks)}")

    except FileNotFoundError:
        print(f"Error: CSV file '{csv_file}' not found")
    except Exception as e:
        print(f"Error processing CSV: {e}")


def process_single_video(
    youtube_id, start_time, end_time, output_dir, temp_dir, row_idx, max_retries=2
):
    """Process a single video with retry logic"""
    for attempt in range(max_retries + 1):
        try:
            # Clean and validate inputs
            youtube_id = youtube_id.strip()
            try:
                start_time = float(start_time)
                end_time = float(end_time)
            except ValueError:
                print(f"Row {row_idx + 1}: Invalid numeric values, skipping...")
                return False

            # Validate time range
            if start_time >= end_time or start_time < 0:
                print(
                    f"Row {row_idx + 1}: Invalid time range ({start_time}-{end_time}), skipping..."
                )
                return False

            # Check if output already exists
            output_file = f"{output_dir}/{youtube_id}_{start_time}_{end_time}.mp4"
            if (
                os.path.exists(output_file) and os.path.getsize(output_file) > 1000
            ):  # At least 1KB
                print(f"Row {row_idx + 1}: Already exists, skipping {youtube_id}")
                return True

            attempt_suffix = (
                f" (attempt {attempt + 1}/{max_retries + 1})" if attempt > 0 else ""
            )
            print(
                f"Processing: {youtube_id} ({start_time}s-{end_time}s) [Row {row_idx + 1}]{attempt_suffix}"
            )

            # Download video with timeout
            video_path = download_video_with_timeout(
                youtube_id, temp_dir, timeout=600
            )  # 10 minutes max
            if not video_path:
                if attempt < max_retries:
                    print(f"Download failed, retrying in 5 seconds...")
                    time.sleep(5)
                    continue
                else:
                    print(
                        f"Download failed after {max_retries + 1} attempts: {youtube_id}"
                    )
                    return False

            # Validate downloaded video
            if not validate_video_file(video_path):
                print(f"Downloaded video is corrupted: {youtube_id}")
                cleanup_temp_file(video_path)
                if attempt < max_retries:
                    time.sleep(5)
                    continue
                else:
                    return False

            # Extract segment with timeout
            success = extract_segment_with_timeout(
                video_path,
                output_file,
                start_time,
                end_time,
                timeout=300,  # 5 minutes max
            )

            # Clean up temp file
            cleanup_temp_file(video_path)

            if success:
                # Validate output file
                if validate_video_file(output_file):
                    print(f"✓ Successfully processed: {youtube_id}")
                    return True
                else:
                    print(f"Output file is corrupted: {youtube_id}")
                    cleanup_temp_file(output_file)
                    if attempt < max_retries:
                        time.sleep(5)
                        continue
                    else:
                        return False
            else:
                if attempt < max_retries:
                    print(f"Segment extraction failed, retrying...")
                    time.sleep(5)
                    continue
                else:
                    print(
                        f"Segment extraction failed after {max_retries + 1} attempts: {youtube_id}"
                    )
                    return False

        except Exception as e:
            print(f"Error processing {youtube_id} (attempt {attempt + 1}): {e}")
            if attempt < max_retries:
                time.sleep(5)
                continue
            else:
                return False

    return False


def download_video_with_timeout(youtube_id, temp_dir, timeout=600):
    """Download video with timeout handling"""
    url = f"https://www.youtube.com/watch?v={youtube_id}"
    output_path = f"{temp_dir}/{youtube_id}.%(ext)s"

    # More conservative format selection to avoid issues
    cmd = [
        "yt-dlp",
        "-f",
        "best[height<=720][ext=mp4]/best[height<=1080][ext=mp4]/best[ext=mp4]/best",
        "--output",
        output_path,
        "--no-playlist",
        "--no-warnings",
        "--merge-output-format",
        "mp4",
        "--quiet",
        "--socket-timeout",
        "30",
        "--retries",
        "3",
        "--fragment-retries",
        "3",
        "--abort-on-error",
        "--cookies",
        "cookies.txt",
        url,
    ]

    process = None
    try:
        process = subprocess.Popen(
            cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
        )

        # Wait for process with timeout
        try:
            stdout, stderr = process.communicate(timeout=timeout)
            if process.returncode == 0:
                # Find the actual downloaded file
                for ext in ["mp4", "webm", "mkv", "avi", "mov"]:
                    potential_path = f"{temp_dir}/{youtube_id}.{ext}"
                    if os.path.exists(potential_path):
                        return potential_path
                print(f"Download completed but file not found: {youtube_id}")
            else:
                error_msg = stderr.strip() if stderr else "Unknown error"
                print(f"Download failed for {youtube_id}: {error_msg}")
        except subprocess.TimeoutExpired:
            print(f"Download timeout for {youtube_id}")
            if process:
                kill_process_tree(process.pid)
                process.kill()
                process.wait()
    except Exception as e:
        print(f"Error downloading {youtube_id}: {e}")
        if process:
            try:
                kill_process_tree(process.pid)
                process.kill()
            except:
                pass

    return None


def extract_segment_with_timeout(
    input_path, output_path, start_time, end_time, timeout=300
):
    """Extract segment with timeout and better error handling"""
    duration = end_time - start_time

    # More robust ffmpeg command
    cmd = [
        "ffmpeg",
        "-i",
        input_path,
        "-ss",
        str(start_time),
        "-t",
        str(duration),
        "-c:v",
        "libx264",  # Re-encode to ensure compatibility
        "-c:a",
        "aac",  # Re-encode audio to ensure compatibility
        "-preset",
        "fast",  # Faster encoding
        "-crf",
        "23",  # Good quality
        "-movflags",
        "+faststart",  # Optimize for streaming
        "-avoid_negative_ts",
        "make_zero",
        "-y",
        "-loglevel",
        "error",
        "-hide_banner",
        output_path,
    ]

    process = None
    try:
        process = subprocess.Popen(
            cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
        )

        # Wait for process with timeout
        try:
            stdout, stderr = process.communicate(timeout=timeout)
            if process.returncode == 0:
                return (
                    os.path.exists(output_path) and os.path.getsize(output_path) > 1000
                )
            else:
                error_msg = stderr.strip() if stderr else "Unknown error"
                print(f"FFmpeg error for {os.path.basename(input_path)}: {error_msg}")
        except subprocess.TimeoutExpired:
            print(f"FFmpeg timeout for {os.path.basename(input_path)}")
            if process:
                kill_process_tree(process.pid)
                process.kill()
                process.wait()
    except Exception as e:
        print(f"FFmpeg exception for {os.path.basename(input_path)}: {e}")
        if process:
            try:
                kill_process_tree(process.pid)
                process.kill()
            except:
                pass

    return False


def validate_video_file(file_path):
    """Validate that a video file is not corrupted"""
    if not os.path.exists(file_path):
        return False

    if os.path.getsize(file_path) < 1000:  # Less than 1KB is probably corrupted
        return False

    # Quick validation using ffprobe
    cmd = [
        "ffprobe",
        "-v",
        "quiet",
        "-select_streams",
        "v:0",
        "-show_entries",
        "stream=duration",
        "-of",
        "csv=p=0",
        file_path,
    ]

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0 and result.stdout.strip():
            try:
                duration = float(result.stdout.strip())
                return duration > 0
            except ValueError:
                return False
    except (subprocess.TimeoutExpired, Exception):
        pass

    return False


def cleanup_temp_file(file_path):
    """Safely remove temporary files"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
    except Exception as e:
        print(f"Warning: Could not remove temp file {file_path}: {e}")


def check_dependencies():
    """Check if required tools are installed"""
    tools = {
        "yt-dlp": "YouTube downloader",
        "ffmpeg": "Video processing",
        "ffprobe": "Video validation",
    }

    missing = []
    for tool, description in tools.items():
        if not shutil.which(tool):
            print(f"✗ {tool} not found in PATH ({description})")
            missing.append(tool)
        else:
            print(f"✓ {tool} found")

    if missing:
        print(f"\nMissing dependencies: {', '.join(missing)}")
        print("Please install them before running this script.")
        return False

    return True


def main():
    parser = argparse.ArgumentParser(
        description="YouTube Video Segment Downloader with Improved Error Handling"
    )
    parser.add_argument("csv_file", help="Path to CSV file")
    parser.add_argument(
        "--output_dir", default="output", help="Output directory (default: output)"
    )
    parser.add_argument(
        "--temp_dir", default="temp", help="Temporary directory (default: temp)"
    )
    parser.add_argument(
        "--random",
        type=int,
        default=None,
        help="Number of random samples to download (default: all)",
    )
    parser.add_argument(
        "--max_retries",
        type=int,
        default=2,
        help="Maximum retry attempts per video (default: 2)",
    )

    args = parser.parse_args()

    print("Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)

    if not os.path.exists(args.csv_file):
        print(f"Error: CSV file '{args.csv_file}' not found")
        sys.exit(1)

    print(f"\nStarting download process...")
    print(f"Output directory: {args.output_dir}")
    print(f"Temp directory: {args.temp_dir}")
    print(f"Max retries per video: {args.max_retries}")

    download_and_process_videos(
        csv_file=args.csv_file,
        output_dir=args.output_dir,
        temp_dir=args.temp_dir,
        n_samples=args.random,
        max_retries=args.max_retries,
    )


if __name__ == "__main__":
    main()
