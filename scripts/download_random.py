import os
import random
import logging
from minio import Minio
from minio.error import S3Error
from dotenv import load_dotenv

# Configure logging
load_dotenv()


logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()],
)


def safe_download(client, bucket_name, obj, output_dir):
    """Safe download implementation with enhanced error handling"""
    local_file = os.path.join(output_dir, os.path.basename(obj.object_name))
    try:
        client.fget_object(bucket_name, obj.object_name, local_file)
        logging.info(f"Downloaded: {obj.object_name} to {local_file}")
        return True
    except Exception as e:
        logging.error(f"Failed to download {obj.object_name}: {str(e)}")
        return False


def main(count=100):
    """Download random video samples from MinIO"""
    client = Minio(
        "objectstore.e2enetworks.net",
        access_key=os.getenv("E2E_ACCESS_KEY"),
        secret_key=os.getenv("E2E_SECRET_KEY"),
        secure=True,
    )

    bucket_name = "d-gpu-5a8f596d-2951-4ad7-bd6c-ee25be0fc91a"
    video_prefix = "NEW_FD_FULL/Videos/"
    output_dir = "data/0_RAW_VIDEOS"

    try:
        os.makedirs(output_dir, exist_ok=True)
    except PermissionError:
        logging.error(f"Permission denied creating {output_dir}")
        return

    try:
        # List objects (read-only)
        objects = client.list_objects(bucket_name, prefix=video_prefix, recursive=True)
        all_videos = [obj for obj in objects if not obj.is_dir]

        if not all_videos:
            logging.warning(f"No videos found in {bucket_name}/{video_prefix}")
            return

        sample_size = min(count, len(all_videos))
        selected_videos = random.sample(all_videos, sample_size)
        logging.info(f"Selected {sample_size} videos for download")

        success_count = 0
        for video in selected_videos:
            if safe_download(client, bucket_name, video, output_dir):
                success_count += 1

        logging.info(f"Download complete. Success: {success_count}/{sample_size}")

    except S3Error as e:
        logging.error(f"S3 Error: {e.message}", exc_info=True)
    except Exception as e:
        logging.error(f"Unexpected error: {str(e)}", exc_info=True)


if __name__ == "__main__":
    count = 100
    main(count)
