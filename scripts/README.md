# Distributed Video Processing System

## Architecture Overview
```mermaid
graph TD
    O[Orchestrator] -->|Task Assignment| N[Node Cluster]
    N --> HC[Health Check API]
    HC -->|Metrics| O
    O -->|Recovery| N
    N --> M[MinIO Storage]
    D[Dashboard] --> O
    D --> N
```

## Components
1. `orchestrator.py` - Main control plane
2. `node_worker.py` - Processing node implementation 
3. `health_check.py` - Node monitoring service
4. `distributed_download.py` - Deterministic downloader
5. `dashboard.py` - Monitoring dashboard

## Setup Instructions

### 1. Prerequisites
```bash
pip install minio python-dotenv requests fastapi uvicorn psutil chart.js
```

### 2. Configuration
Create `.env` file with MinIO credentials:
```ini
E2E_ACCESS_KEY=your_access_key
E2E_SECRET_KEY=your_secret_key
DASHBOARD_PORT=8000
```

### 3. Running the System

#### Start Orchestrator (Control Node):
```bash
python scripts/orchestrator.py
```

#### Start Worker Nodes (Repeat for each node):
```bash
export NODE_INDEX=0  # 0-11 for 12 nodes
export TOTAL_NODES=12
python scripts/node_worker.py
```

#### Start Monitoring Dashboard:
```bash
python scripts/dashboard.py
```

## Monitoring
- **Dashboard**: `http://localhost:8000`
- Orchestrator logs: `orchestrator.log`  
- Node logs: `logs/node_<index>.out`  
- Health metrics: `http://<node_ip>:8080/metrics` (Prometheus format)

## Customization
| Variable          | Description                          | Default |
|-------------------|--------------------------------------|---------|
| `TOTAL_VIDEOS`    | Total videos to process              | 15000   |
| `LOG_LEVEL`       | Logging verbosity                    | INFO    |
| `HEALTH_PORT`     | Health check port                    | 8080    |
| `DASHBOARD_PORT`  | Monitoring dashboard port            | 8000    |

## Troubleshooting
```bash
# Check node status:
curl http://node-ip:8080/health

# View dashboard:
curl http://localhost:8000/api/cluster

# Restart failed nodes:
export NODE_INDEX=<failed_node> && python node_worker.py

# Debug dashboard:
python scripts/dashboard.py --debug