{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1523337d", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "id": "d414057b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Plot saved to: test_1/syncnet_plots/syncnet_one_face_full_speech_track1.png\n"]}, {"data": {"image/png": "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*********************************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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import os\n", "\n", "# Input variables\n", "root_dir = \"test_1\"\n", "video_name = \"one_face_full_speech\"\n", "track_id = \"track1\"  # e.g., track1, track2, etc.\n", "\n", "# Construct directory path\n", "base_dir = os.path.join(\n", "    root_dir, f\"{video_name}_{track_id}\", \"pycrop\", f\"{video_name}_{track_id}\"\n", ")\n", "\n", "# The video identifier is typically '00000' for the first face in the track\n", "video_identifier = \"00000\"\n", "\n", "# Construct file paths\n", "unsmooth_path = os.path.join(base_dir, f\"{video_identifier}.avi_unprocessed_fconfm.npy\")\n", "smooth_path = os.path.join(\n", "    base_dir, f\"{video_identifier}_window_size_24_smoothened_fconfm.npy\"\n", ")\n", "\n", "# Load scores\n", "try:\n", "    unsmooth_scores = np.load(unsmooth_path, allow_pickle=True)\n", "    smooth_scores = np.load(smooth_path, allow_pickle=True)\n", "except FileNotFoundError as e:\n", "    print(f\"Error loading files: {e}\")\n", "    print(f\"Looking for files at:\")\n", "    print(f\"Unsmooth path: {unsmooth_path}\")\n", "    print(f\"Smooth path: {smooth_path}\")\n", "    exit()\n", "\n", "# Get min and max values for unsmooth scores\n", "max_score = np.max(unsmooth_scores)\n", "min_score = np.min(unsmooth_scores)\n", "max_index = np.argmax(unsmooth_scores)\n", "min_index = np.argmin(unsmooth_scores)\n", "\n", "# Get min and max values for smooth scores\n", "max_score_smooth = np.max(smooth_scores)\n", "min_score_smooth = np.min(smooth_scores)\n", "max_index_smooth = np.argmax(smooth_scores)\n", "min_index_smooth = np.argmin(smooth_scores)\n", "\n", "# Plotting\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(\n", "    range(len(unsmooth_scores)),\n", "    unsmooth_scores,\n", "    linestyle=\"-\",\n", "    color=\"b\",\n", "    label=\"Raw SyncNet Score\",\n", ")\n", "plt.plot(\n", "    range(len(smooth_scores)),\n", "    smooth_scores,\n", "    linestyle=\"--\",\n", "    color=\"r\",\n", "    label=\"Smoothed SyncNet Score\",\n", ")\n", "\n", "# Add max/min dots and annotations\n", "plt.scatter(max_index, max_score, color=\"blue\", s=100, zorder=5, label=\"Max Raw\")\n", "plt.scatter(min_index, min_score, color=\"blue\", s=100, zorder=5, label=\"Min Raw\")\n", "plt.text(\n", "    max_index,\n", "    max_score,\n", "    f\"Max: {max_score:.2f}\",\n", "    color=\"black\",\n", "    fontsize=11,\n", "    ha=\"right\",\n", "    va=\"bottom\",\n", ")\n", "plt.text(\n", "    min_index,\n", "    min_score,\n", "    f\"Min: {min_score:.2f}\",\n", "    color=\"black\",\n", "    fontsize=11,\n", "    ha=\"right\",\n", "    va=\"top\",\n", ")\n", "\n", "plt.scatter(\n", "    max_index_smooth, max_score_smooth, color=\"red\", s=100, zorder=5, label=\"<PERSON> Smooth\"\n", ")\n", "plt.scatter(\n", "    min_index_smooth, min_score_smooth, color=\"red\", s=100, zorder=5, label=\"Min Smooth\"\n", ")\n", "plt.text(\n", "    max_index_smooth,\n", "    max_score_smooth,\n", "    f\"Max: {max_score_smooth:.2f}\",\n", "    color=\"black\",\n", "    fontsize=11,\n", "    ha=\"left\",\n", "    va=\"bottom\",\n", ")\n", "plt.text(\n", "    min_index_smooth,\n", "    min_score_smooth,\n", "    f\"Min: {min_score_smooth:.2f}\",\n", "    color=\"black\",\n", "    fontsize=11,\n", "    ha=\"left\",\n", "    va=\"top\",\n", ")\n", "\n", "plt.title(f\"SyncNet Scores\\n{video_name} ({track_id})\", fontsize=14)\n", "plt.xlabel(\"Frame Index\", fontsize=12)\n", "plt.ylabel(\"SyncNet Confidence\", fontsize=12)\n", "plt.grid(True, alpha=0.3)\n", "plt.legend(fontsize=10)\n", "plt.xticks(fontsize=10)\n", "plt.yticks(fontsize=10)\n", "plt.tight_layout()\n", "\n", "# Create output directory if it doesn't exist\n", "output_dir = os.path.join(root_dir, \"syncnet_plots\")\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "# Save plot\n", "output_path = os.path.join(output_dir, f\"syncnet_{video_name}_{track_id}.png\")\n", "plt.savefig(output_path, dpi=300, bbox_inches=\"tight\")\n", "print(f\"Plot saved to: {output_path}\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "3bd62f43", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}