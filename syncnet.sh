#!/bin/bash

video_dir="tests"  # Replace with your actual directory path
for video_path in "$video_dir"/*.mp4; do
    filename=$(basename "$video_path")
    reference="${filename%.*}"  # Remove the file extension
    data_dir="test_results/$reference"  # Dynamic data_dir based on video name

    mkdir -p "$data_dir"

    uv run -m src.models.syncnet.run_pipeline \
        --videofile "$video_path" \
        --reference "$reference" \
        --data_dir "$data_dir" 
    uv run -m src.models.syncnet.run_syncnet\
        --videofile "$video_path" \
        --reference "$reference" \
        --data_dir "$data_dir" 
    uv run -m src.models.syncnet.run_visualise\
    --videofile "$video_path" \
    --reference "$reference" \
    --data_dir "$data_dir" 
done
