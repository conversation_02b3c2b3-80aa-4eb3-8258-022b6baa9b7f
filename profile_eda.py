#!/usr/bin/env python3
"""
Profiling script for EDA_Pipeline to identify where it's getting stuck.
"""

import os
import sys
import time
import signal
import threading
import psutil
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from EDA_Pipeline import EDAPipeline

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProcessMonitor:
    """Monitor system resources during execution"""
    
    def __init__(self, interval=1.0):
        self.interval = interval
        self.monitoring = False
        self.stats = []
        self.thread = None
        
    def start(self):
        """Start monitoring"""
        self.monitoring = True
        self.thread = threading.Thread(target=self._monitor_loop)
        self.thread.daemon = True
        self.thread.start()
        logger.info("Process monitoring started")
        
    def stop(self):
        """Stop monitoring"""
        self.monitoring = False
        if self.thread:
            self.thread.join(timeout=2.0)
        logger.info("Process monitoring stopped")
        
    def _monitor_loop(self):
        """Main monitoring loop"""
        process = psutil.Process()
        
        while self.monitoring:
            try:
                # Get current stats
                cpu_percent = process.cpu_percent()
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                
                # GPU stats if available
                gpu_info = "N/A"
                try:
                    import GPUtil
                    gpus = GPUtil.getGPUs()
                    if gpus:
                        gpu = gpus[0]
                        gpu_info = f"{gpu.memoryUsed}MB/{gpu.memoryTotal}MB ({gpu.load*100:.1f}%)"
                except ImportError:
                    pass
                
                stat = {
                    'timestamp': time.time(),
                    'cpu_percent': cpu_percent,
                    'memory_mb': memory_mb,
                    'gpu_info': gpu_info
                }
                self.stats.append(stat)
                
                # Log every 10 seconds
                if len(self.stats) % 10 == 0:
                    logger.info(f"CPU: {cpu_percent:.1f}%, Memory: {memory_mb:.1f}MB, GPU: {gpu_info}")
                    
            except Exception as e:
                logger.warning(f"Error in monitoring: {e}")
                
            time.sleep(self.interval)
            
    def get_summary(self):
        """Get monitoring summary"""
        if not self.stats:
            return "No monitoring data collected"
            
        cpu_avg = sum(s['cpu_percent'] for s in self.stats) / len(self.stats)
        memory_avg = sum(s['memory_mb'] for s in self.stats) / len(self.stats)
        memory_max = max(s['memory_mb'] for s in self.stats)
        
        return f"""
Monitoring Summary:
- Duration: {len(self.stats)} seconds
- Average CPU: {cpu_avg:.1f}%
- Average Memory: {memory_avg:.1f}MB
- Peak Memory: {memory_max:.1f}MB
"""

def timeout_handler(signum, frame):
    """Handle timeout signal"""
    logger.error("TIMEOUT: Process has been running too long!")
    raise TimeoutError("Process timeout")

def main():
    """Main profiling function"""
    # Set up timeout (5 minutes)
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(300)  # 5 minutes
    
    # Start monitoring
    monitor = ProcessMonitor()
    monitor.start()
    
    try:
        # Test data directory
        data_dir = "/home/<USER>/LatentSync-Fix/data/imported/Videos"
        device = "cuda"
        
        logger.info(f"Starting EDA Pipeline profiling...")
        logger.info(f"Data directory: {data_dir}")
        logger.info(f"Device: {device}")
        
        # Check if data directory exists
        if not os.path.exists(data_dir):
            logger.error(f"Data directory does not exist: {data_dir}")
            return
            
        # List video files
        video_files = []
        for ext in ['*.mp4', '*.avi', '*.mov']:
            video_files.extend(Path(data_dir).glob(ext))
            
        logger.info(f"Found {len(video_files)} video files")
        if video_files:
            logger.info(f"First video: {video_files[0]}")
            
        # Initialize pipeline
        start_time = time.time()
        logger.info("Initializing EDAPipeline...")
        
        pipeline = EDAPipeline(data_dir, device)
        init_time = time.time() - start_time
        logger.info(f"Pipeline initialization took {init_time:.2f}s")
        
        # Run the pipeline
        logger.info("Starting pipeline execution...")
        execution_start = time.time()
        
        pipeline.run_eda()
        
        execution_time = time.time() - execution_start
        logger.info(f"Pipeline execution completed in {execution_time:.2f}s")
        
    except TimeoutError:
        logger.error("Pipeline execution timed out!")
        logger.info("This indicates the pipeline is hanging somewhere.")
        
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
        
    except Exception as e:
        logger.error(f"Error during execution: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Stop monitoring and show summary
        monitor.stop()
        print(monitor.get_summary())
        
        # Cancel alarm
        signal.alarm(0)

if __name__ == "__main__":
    main()
