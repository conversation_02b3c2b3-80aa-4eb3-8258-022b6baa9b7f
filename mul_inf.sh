#!/bin/bash

# ./infer_dir.sh ./data S2-20-Influencer checkpoints/Task2-Stage2_17237_20250620_111055/checkpoints/checkpoint-11800.pt
# curl -X POST -H 'Content-type: application/json' --data '{"text":"Inference completed for S2-20-Influencer"}' *********************************************************************************

# ./infer_dir.sh ./data MS-SSIM-16449 checkpoints/Influencer-Task2-Stage2_16449_20250620_145203/checkpoint-11800.pt
# curl -X POST -H 'Content-type: application/json' --data '{"text":"Inference completed for MS-SSIM-16449"}' *********************************************************************************

# ./infer_dir.sh ./data MS-SSIM-16866 checkpoints/Influencer-Task2-Stage2_16866_20250620_123414/checkpoints/checkpoint-11800.pt
# curl -X POST -H 'Content-type: application/json' --data '{"text":"Inference completed for MS-SSIM-16866"}' *********************************************************************************

# ./infer_dir.sh ./data MS-SSIM-18543 checkpoints/Influencer-Task2-Stage2_18543_20250620_134314/checkpoints/checkpoint-11800.pt
# curl -X POST -H 'Content-type: application/json' --data '{"text":"Inference completed for MS-SSIM-18543"}' *********************************************************************************

# ./infer_dir.sh ./data MS-SSIM-20132 checkpoints/Influencer-Task2-Stage2_20132_20250620_160032/checkpoints/checkpoint-11800.pt
# curl -X POST -H 'Content-type: application/json' --data '{"text":"Inference completed for MS-SSIM-20132"}' *********************************************************************************

./infer_dir.sh data/Mayur HD_OneShot_Mayur checkpoints/Task1-HD-Stage2/ckpt_11_8.pt
curl -X POST -H 'Content-type: application/json' --data '{"text":"Inference completed for HD_OneShot_Mayur"}' *********************************************************************************

