{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Audio Utils"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyannote.audio import Pipeline\n", "import torch\n", "\n", "\n", "def count_speakers_in_audio(audio_file_path, device=None):\n", "    \"\"\"\n", "    Count the number of unique speakers in an audio file using pyannote.audio.\n", "\n", "    Parameters:\n", "    audio_file_path (str): Path to the audio file\n", "    device (str, optional): <PERSON><PERSON> to run the pipeline on. Options: 'cpu', 'cuda', 'cuda:0', 'cuda:1', etc.\n", "                           If None, will use CUDA if available, otherwise CPU.\n", "\n", "    Returns:\n", "    int: Number of unique speakers detected\n", "    \"\"\"\n", "    # Determine the device to use\n", "    if device is None:\n", "        device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "\n", "    # Initialize the pretrained speaker diarization pipeline\n", "    pipeline = Pipeline.from_pretrained(\n", "        \"pyannote/speaker-diarization@2.1\", use_auth_token=\"YOUR_HUGGINGFACE_TOKEN\"\n", "    )\n", "\n", "    # Move pipeline to specified device\n", "    pipeline = pipeline.to(device)\n", "\n", "    # Apply the pipeline to the audio file\n", "    diarization = pipeline(audio_file_path)\n", "\n", "    # Extract unique speaker labels\n", "    speakers = set()\n", "    for turn, _, speaker in diarization.itertracks(yield_label=True):\n", "        speakers.add(speaker)\n", "\n", "    # Return the number of unique speakers\n", "    return len(speakers)\n", "\n", "\n", "# Example usage\n", "if __name__ == \"__main__\":\n", "    audio_path = \"path/to/your/audio/file.wav\"\n", "\n", "    # Use default device (CUDA if available)\n", "    num_speakers = count_speakers_in_audio(audio_path)\n", "    print(f\"Detected {num_speakers} speakers in the audio file.\")\n", "\n", "    # Or specify a particular device\n", "    # num_speakers = count_speakers_in_audio(audio_path, device=\"cuda:1\")  # Use second GPU\n", "    # num_speakers = count_speakers_in_audio(audio_path, device=\"cpu\")     # Force CPU usage"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Face Detector"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Add project root to python path from jupyter notebook\n", "import sys\n", "from pathlib import Path\n", "\n", "sys.path.append(str(Path.cwd().parent))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from src.models.faceDetector.s3fd import S3FD\n", "import cv2"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[ WARN:0@3.445] global loadsave.cpp:268 findDecoder imread_('/home/<USER>/HotDub-Preprocess/temp/inputs/bts.jpg'): can't open/read file: check file path/integrity\n"]}], "source": ["image = cv2.imread(\"/home/<USER>/HotDub-Preprocess/temp/inputs/bts.jpg\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DET = S3FD(device=\"cuda\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Detect faces\n", "frame_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)\n", "bboxes = DET.detect_faces(frame_rgb, conf_th=0.9, scales=[1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bboxes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Tracking"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from models.faceDetector.s3fd import S3FD\n", "import os\n", "import cv2\n", "import numpy as np\n", "import pickle\n", "import sys\n", "from pathlib import Path\n", "from types import SimpleNamespace"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class FaceTracker:\n", "    def __init__(self, iou_threshold=0.5):\n", "        self.tracks = []  # List of tracked faces\n", "        self.colors = {}  # Dictionary mapping track_id to color\n", "        self.next_id = 0  # Counter for generating unique track IDs\n", "        self.iou_threshold = iou_threshold\n", "\n", "    def _generate_color(self):\n", "        \"\"\"Generate a random BGR color\"\"\"\n", "        color = tuple(map(int, np.random.randint(0, 255, 3)))\n", "        return color\n", "\n", "    def _calculate_iou(self, box1, box2):\n", "        \"\"\"Calculate IOU between two boxes\"\"\"\n", "        x1, y1, x2, y2 = box1\n", "        x3, y3, x4, y4 = box2\n", "\n", "        # Calculate intersection\n", "        xx1 = max(x1, x3)\n", "        yy1 = max(y1, y3)\n", "        xx2 = min(x2, x4)\n", "        yy2 = min(y2, y4)\n", "\n", "        w = max(0, xx2 - xx1)\n", "        h = max(0, yy2 - yy1)\n", "        intersection = w * h\n", "\n", "        # Calculate union\n", "        box1_area = (x2 - x1) * (y2 - y1)\n", "        box2_area = (x4 - x3) * (y4 - y3)\n", "        union = box1_area + box2_area - intersection\n", "\n", "        iou = intersection / union if union > 0 else 0\n", "        return iou\n", "\n", "    def update(self, detections):\n", "        \"\"\"Update tracks with new detections\"\"\"\n", "        if not self.tracks:  # First frame\n", "            for det in detections:\n", "                track_id = self.next_id\n", "                self.next_id += 1\n", "                self.colors[track_id] = self._generate_color()\n", "                self.tracks.append(\n", "                    {\n", "                        \"id\": track_id,\n", "                        \"bbox\": det[:-1],  # Exclude confidence score\n", "                        \"conf\": det[-1],\n", "                    }\n", "                )\n", "            return self.tracks\n", "\n", "        # Match detections to existing tracks using IOU\n", "        matched_track_ids = set()\n", "        matched_det_ids = set()\n", "        matches = []  # (track_idx, det_idx) pairs\n", "\n", "        for track_idx, track in enumerate(self.tracks):\n", "            for det_idx, det in enumerate(detections):\n", "                if det_idx in matched_det_ids:\n", "                    continue\n", "\n", "                iou = self._calculate_iou(track[\"bbox\"], det[:-1])\n", "                if iou >= self.iou_threshold:\n", "                    matches.append((track_idx, det_idx))\n", "                    matched_track_ids.add(track_idx)\n", "                    matched_det_ids.add(det_idx)\n", "\n", "        # Update matched tracks\n", "        new_tracks = []\n", "        for track_idx, det_idx in matches:\n", "            track = self.tracks[track_idx]\n", "            det = detections[det_idx]\n", "            track[\"bbox\"] = det[:-1]\n", "            track[\"conf\"] = det[-1]\n", "            new_tracks.append(track)\n", "\n", "        # Add new tracks for unmatched detections\n", "        for det_idx, det in enumerate(detections):\n", "            if det_idx not in matched_det_ids:\n", "                track_id = self.next_id\n", "                self.next_id += 1\n", "                self.colors[track_id] = self._generate_color()\n", "                new_tracks.append({\"id\": track_id, \"bbox\": det[:-1], \"conf\": det[-1]})\n", "\n", "        self.tracks = new_tracks\n", "        return self.tracks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_output_path(video_path):\n", "    \"\"\"Generate unique output path with incremental index\"\"\"\n", "    video_path = Path(video_path)\n", "    base_path = Path(\"@/temp/results\")\n", "    base_path.mkdir(parents=True, exist_ok=True)\n", "\n", "    base_name = f\"{video_path.stem}_result\"\n", "    index = 0\n", "    while True:\n", "        suffix = f\"_{index:03d}\" if index > 0 else \"\"\n", "        output_path = base_path / f\"{base_name}{suffix}{video_path.suffix}\"\n", "        if not output_path.exists():\n", "            return str(output_path)\n", "        index += 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def inference_video(config):\n", "    \"\"\"Process video with face detection and tracking\"\"\"\n", "    # Initialize face detector\n", "    DET = S3FD(device=\"cuda\")\n", "    tracker = FaceTracker(iou_threshold=0.5)\n", "\n", "    # Open video capture\n", "    cap = cv2.VideoCapture(config.videoFilePath)\n", "    if not cap.isOpened():\n", "        raise ValueError(f\"Could not open video file: {config.videoFilePath}\")\n", "\n", "    # Get video properties\n", "    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))\n", "    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))\n", "    fps = int(cap.get(cv2.CAP_PROP_FPS))\n", "    # fourcc = int(cap.get(cv2.CAP_PROP_FOURCC))\n", "    fourcc = cv2.VideoWriter_fourcc(*\"mp4v\")\n", "\n", "    # Initialize video writer\n", "    output_path = get_output_path(config.videoFilePath)\n", "    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))\n", "\n", "    dets = []  # Store all detections for pickle output\n", "    frame_idx = 0\n", "\n", "    while True:\n", "        ret, frame = cap.read()\n", "        if not ret:\n", "            break\n", "\n", "        # Detect faces\n", "        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)\n", "        bboxes = DET.detect_faces(frame_rgb, conf_th=0.9, scales=[config.facedetScale])\n", "\n", "        # Update tracks\n", "        tracks = tracker.update(bboxes)\n", "\n", "        # Store detections\n", "        frame_dets = []\n", "        for track in tracks:\n", "            bbox = track[\"bbox\"]\n", "            conf = track[\"conf\"]\n", "            frame_dets.append(\n", "                {\n", "                    \"frame\": frame_idx,\n", "                    \"bbox\": bbox.tolist() if isinstance(bbox, np.ndarray) else bbox,\n", "                    \"conf\": conf,\n", "                }\n", "            )\n", "\n", "            # Draw bounding box\n", "            x1, y1, x2, y2 = map(int, bbox)\n", "            color = tracker.colors[track[\"id\"]]\n", "            cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)\n", "\n", "            # Add track ID\n", "            label = f\"Face {track['id']}\"\n", "            cv2.putText(\n", "                frame, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2\n", "            )\n", "\n", "        dets.append(frame_dets)\n", "        writer.write(frame)\n", "\n", "        sys.stderr.write(\n", "            f\"{config.videoFilePath}-{frame_idx:05d}; {len(frame_dets)} dets\\r\"\n", "        )\n", "        frame_idx += 1\n", "\n", "    # Clean up\n", "    cap.release()\n", "    writer.release()\n", "\n", "    # Save detections to pickle file\n", "    save_path = os.path.join(config.pyworkPath, \"faces.pckl\")\n", "    with open(save_path, \"wb\") as fil:\n", "        pickle.dump(dets, fil)\n", "\n", "    return dets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def main(video_path):\n", "    \"\"\"Process video end to end with face detection and tracking\"\"\"\n", "    # Create output directories\n", "    temp_dir = Path(\"temp\")\n", "    temp_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "    results_dir = temp_dir / \"results\"\n", "    results_dir.mkdir(exist_ok=True)\n", "\n", "    # Configure processing parameters\n", "    config = SimpleNamespace(\n", "        videoFilePath=video_path,\n", "        facedetScale=0.25,  # Scale factor for face detection\n", "        pyworkPath=str(temp_dir),  # Path for saving pickle file\n", "    )\n", "\n", "    # Process video\n", "    try:\n", "        print(f\"Processing video: {video_path}\")\n", "        detections = inference_video(config)\n", "        print(\n", "            f\"\\nProcessing complete! Found {sum(len(d) for d in detections)} faces in {len(detections)} frames\"\n", "        )\n", "        print(f\"Results saved in {results_dir}\")\n", "        return True\n", "    except Exception as e:\n", "        print(f\"Error processing video: {str(e)}\")\n", "        return False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example usage:\n", "video_path = (\n", "    \"/home/<USER>/HotDub-Preprocess/temp/inputs/_-MpWAXYSSk_P0_C1_shot_001_000.mp4\"\n", ")\n", "success = main(video_path)"]}], "metadata": {"kernelspec": {"display_name": "dpp", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}