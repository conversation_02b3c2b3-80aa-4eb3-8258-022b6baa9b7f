{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f2c98d43", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/HotDub-Preprocess/.venv/lib/python3.12/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import argparse\n", "import imageio\n", "import cv2\n", "from tqdm import tqdm\n", "import yaml\n", "import os\n", "import numpy as np\n", "\n", "from threeddfa.FaceBoxes import FaceBoxes\n", "from threeddfa.TDDFA import TDDFA\n", "\n", "# from threeddfa.utils.render import render\n", "from threeddfa.utils.render_ctypes import render\n", "from threeddfa.utils.functions import cv_draw_landmark, get_suffix, draw_landmarks\n", "from threeddfa.utils.pose import viz_pose"]}, {"cell_type": "code", "execution_count": 2, "id": "1210bb81", "metadata": {}, "outputs": [], "source": ["CONFIG = \"/home/<USER>/HotDub-Preprocess/src/models/threeddfa/threeddfa/configs/mb1_120x120.yml\"\n", "ONNX = False\n", "MODE = \"gpu\"\n", "VIDEO = \"/home/<USER>/HotDub-Preprocess/data/6_VALID/VnC1xdI5gfs_P1_C0_shot_001.mp4\"\n", "OPT = \"3d\""]}, {"cell_type": "code", "execution_count": 3, "id": "3825f052", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["0it [00:00, ?it/s]IMAGEIO FFMPEG_WRITER WARNING: input image is not divisible by macro_block_size=16, resizing from (1920, 1080) to (1920, 1088) to ensure video compatibility with most codecs and players. To prevent resizing, make your input image divisible by the macro_block_size or set the macro_block_size to 1 (risking incompatibility).\n", "1it [00:00,  2.08it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2it [00:00,  3.46it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["4it [00:00,  5.99it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.2, pitch: -0.0, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["8it [00:01,  8.73it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["10it [00:01,  9.41it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.2, pitch: -0.0, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: 0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["12it [00:01,  9.66it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["14it [00:01,  9.59it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["16it [00:01, 10.06it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.2, pitch: -0.0, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: 0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["20it [00:02, 10.34it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["22it [00:02, 10.51it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.2, pitch: -0.0, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["24it [00:02, 10.68it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["26it [00:02, 10.90it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.2, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["30it [00:03, 10.89it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["32it [00:03, 10.92it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.2, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: -0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: -0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["34it [00:03, 10.90it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["36it [00:03, 10.92it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.2, pitch: -0.0, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: -0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: -0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["40it [00:04, 10.96it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: -0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: -0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["42it [00:04, 10.91it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.2, pitch: -0.0, roll: -0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: -0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["44it [00:04, 10.61it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.0, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["46it [00:04, 10.69it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.1, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["48it [00:04, 10.09it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.1, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["50it [00:05,  9.99it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.1, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["53it [00:05,  9.73it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.1, pitch: -0.0, roll: -0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.0, roll: -0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["55it [00:05,  9.62it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.1, pitch: -0.0, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.0, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["57it [00:05,  8.79it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.1, pitch: -0.0, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: 0.0, roll: -0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["58it [00:06,  8.38it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: 0.0, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["60it [00:06,  7.88it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.2, pitch: 0.1, roll: -0.2\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: 0.1, roll: -0.2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["62it [00:06,  7.55it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: 0.0, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: 0.0, roll: -0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["63it [00:06,  7.44it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: 0.0, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: 0.0, roll: -0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["66it [00:07,  7.57it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: 0.0, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: 0.0, roll: -0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["68it [00:07,  7.57it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.0, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.0, roll: -0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["70it [00:07,  7.58it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["71it [00:07,  7.03it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.0, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["74it [00:08,  7.53it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["76it [00:08,  7.84it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["77it [00:08,  6.56it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["79it [00:08,  7.13it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["81it [00:09,  8.45it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["83it [00:09,  8.67it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["86it [00:09,  7.64it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["88it [00:09,  8.47it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["89it [00:10,  8.69it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["91it [00:10,  9.23it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["95it [00:10,  9.62it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["97it [00:10,  9.54it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["99it [00:11,  9.59it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: 0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["101it [00:11,  9.44it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["102it [00:11,  9.11it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: 0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["104it [00:11,  7.88it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["106it [00:12,  7.13it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.2, pitch: -0.1, roll: 0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["107it [00:12,  6.82it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["109it [00:12,  7.07it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.1, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["111it [00:12,  7.86it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.2, pitch: -0.1, roll: -0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: -0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["112it [00:12,  8.32it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.1, pitch: -0.1, roll: -0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: -0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: -0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["116it [00:13,  8.67it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: -0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: -0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["118it [00:13,  9.12it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["120it [00:13,  9.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.1, pitch: -0.1, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["122it [00:13,  8.94it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.1, pitch: -0.1, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: -0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["124it [00:14,  8.49it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.1, pitch: -0.1, roll: -0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["126it [00:14,  9.15it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.1, pitch: -0.1, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["128it [00:14,  9.22it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.1, pitch: -0.0, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["130it [00:14,  9.29it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.2, pitch: -0.0, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: -0.0, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["132it [00:14,  9.00it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["yaw: 0.2, pitch: -0.0, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: 0.0, roll: -0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["133it [00:15,  8.22it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.2, pitch: 0.0, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: 0.0, roll: -0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["135it [00:15,  7.52it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: 0.0, roll: -0.1\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.0, roll: -0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["137it [00:15,  7.69it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.0, roll: -0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.0, roll: -0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["139it [00:15,  8.37it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: 0.0\n", "param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["141it [00:16,  8.72it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["param_lst: (1, 62)\n", "ver: (3, 38365)\n", "yaw: 0.1, pitch: -0.1, roll: -0.0\n", "Dump to examples/results/videos/VnC1xdI5gfs_P1_C0_shot_001_3d.mp4\n"]}], "source": ["cfg = yaml.load(open(CONFIG), Loader=yaml.SafeLoader)\n", "\n", "# Init FaceBoxes and TDDFA, recommend using onnx flag\n", "if ONNX:\n", "    os.environ[\"KMP_DUPLICATE_LIB_OK\"] = \"True\"\n", "    os.environ[\"OMP_NUM_THREADS\"] = \"4\"\n", "\n", "    from threeddfa.FaceBoxes.FaceBoxes_ONNX import FaceBoxes_ONNX\n", "    from threeddfa.TDDFA_ONNX import TDDFA_ONNX\n", "\n", "    face_boxes = FaceBoxes_ONNX()\n", "    tddfa = TDDFA_ONNX(**cfg)\n", "else:\n", "    gpu_mode = MODE == \"gpu\"\n", "    tddfa = TDDFA(gpu_mode=gpu_mode, **cfg)\n", "    face_boxes = FaceBoxes()\n", "\n", "# Given a video path\n", "fn = VIDEO.split(\"/\")[-1]\n", "reader = imageio.get_reader(VIDEO)\n", "\n", "fps = reader.get_meta_data()[\"fps\"]\n", "\n", "suffix = get_suffix(VIDEO)\n", "video_wfp = f\"examples/results/videos/{fn.replace(suffix, '')}_{OPT}.mp4\"\n", "if not os.path.exists(\"examples/results/videos\"):\n", "    os.makedirs(\"examples/results/videos\")\n", "writer = imageio.get_writer(video_wfp, fps=fps)\n", "\n", "# run\n", "dense_flag = OPT in (\"3d\",)\n", "pre_ver = None\n", "for i, frame in tqdm(enumerate(reader)):\n", "    frame_bgr = frame[..., ::-1]  # RGB->BGR\n", "\n", "    if i == 0:\n", "        # the first frame, detect face, here we only use the first face, you can change depending on your need\n", "        boxes = face_boxes(frame_bgr)\n", "        boxes = [boxes[0]]\n", "        param_lst, roi_box_lst = tddfa(frame_bgr, boxes)\n", "        ver = tddfa.recon_vers(param_lst, roi_box_lst, dense_flag=dense_flag)[0]\n", "\n", "        # refine\n", "        param_lst, roi_box_lst = tddfa(frame_bgr, [ver], crop_policy=\"landmark\")\n", "        ver = tddfa.recon_vers(param_lst, roi_box_lst, dense_flag=dense_flag)[0]\n", "    else:\n", "        param_lst, roi_box_lst = tddfa(frame_bgr, [pre_ver], crop_policy=\"landmark\")\n", "\n", "        roi_box = roi_box_lst[0]\n", "        # todo: add confidence threshold to judge the tracking is failed\n", "        if abs(roi_box[2] - roi_box[0]) * abs(roi_box[3] - roi_box[1]) < 2020:\n", "            boxes = face_boxes(frame_bgr)\n", "            boxes = [boxes[0]]\n", "            param_lst, roi_box_lst = tddfa(frame_bgr, boxes)\n", "\n", "        ver = tddfa.recon_vers(param_lst, roi_box_lst, dense_flag=dense_flag)[0]\n", "\n", "    param_arr = np.array(param_lst)\n", "    ver_arr = np.array(ver)\n", "\n", "    print(f\"param_lst: {param_arr.shape}\")\n", "    print(f\"ver: {ver_arr.shape}\")\n", "\n", "    pre_ver = ver  # for tracking\n", "\n", "    if OPT == \"2d_sparse\":\n", "        res = cv_draw_landmark(frame_bgr, ver)\n", "    elif <PERSON> == \"2d_dense\":\n", "        res = draw_landmarks(frame_bgr, ver, dense_flag=True)\n", "        pose = viz_pose(res, param_lst, [ver], show_flag=False)\n", "    elif <PERSON> == \"3d\":\n", "        res = render(frame_bgr, [ver], tddfa.tri)\n", "        pose = viz_pose(res, param_lst, [ver], show_flag=False)\n", "        # Function to plot 30th landmark\n", "        cv2.circle(res, (int(ver[0][8310]), int(ver[1][8310])), 5, (0, 255, 0), -1)\n", "    else:\n", "        raise ValueError(f\"Unknown opt {OPT}\")\n", "\n", "    writer.append_data(pose[..., ::-1])  # BGR->RGB\n", "\n", "writer.close()\n", "print(f\"Dump to {video_wfp}\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}