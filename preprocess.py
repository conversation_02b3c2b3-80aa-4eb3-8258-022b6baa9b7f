#!/usr/bin/env python3
"""
Video preprocessing pipeline that handles:
1. Corruption check
2. Shot generation
3. FPS/Hz resampling
4. Denoising
5. ASD validation
6. AV sync correction
7. affine transformation
8. visual quality assessment
"""

import atexit
import gc
import glob
import hashlib
import multiprocessing
import multiprocessing as mp
import os
import shutil
import signal
import subprocess
import threading
import time
from concurrent.futures import ProcessPoolExecutor, as_completed
from dataclasses import dataclass
from functools import partial, wraps
from pathlib import Path
from typing import Dict, Optional, Set, Union

import yaml
from rich.console import Console
from rich.prompt import InvalidResponse, Prompt

from scripts.node_worker import send_slack_notification
from src.affine import affine_transform_multi_gpus
from src.asd import segment_video_by_speech
from src.auto_asd import run_asd_pipeline
from src.filter_single_face import process_sf_videos_parallel
from src.filter_visual_quality import filter_visual_quality_multi_gpus
from src.generate_shots import detect_shot_multiprocessing
from src.resample_fps_hz import resample_fps_hz_multiprocessing
from src.sync_correction import sync_av_multi_gpus
from src.utils.audio_utils import enhance, extract_audio
from src.utils.logger import configure_logger
from src.utils.model_utils import verify_and_download_model
from src.utils.video_utils import check_all_videos_corruption, get_all_videos_durations

# Set multiprocessing start method before any other imports
multiprocessing.set_start_method("spawn", force=True)

# Try importing torch for CUDA cleanup
try:
    import torch

    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False


# Configure logger
logger = configure_logger(__name__)


@dataclass
class FileReadyFlag:
    """Dataclass for tracking file processing status and integrity."""

    file_path: str
    checksum: str
    timestamp: float
    size: int


def timer_decorator(func):
    """Decorator to measure and log function execution time."""

    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        total_duration = time.time() - start_time
        duration_str = (
            f"{total_duration:.2f} seconds"
            if total_duration < 60
            else f"{total_duration / 60:.2f} minutes"
        )
        logger.info(f"{func.__name__} completed in {duration_str}")
        return result

    return wrapper


class PreprocessPipeline:
    # Default config values, will be overridden by YAML if available
    DEFAULT_CONFIG = {
        "pipeline": {
            "process_kill_timeout": 30,
            "ffmpeg_timeout": 300,
            "cuda_retry_count": 3,
        },
        "stages": {
            "filter_single_face": {"confidence_threshold": 0.95},
            "delete_short_videos": {"min_duration_seconds": 3},
            "sync_correction": {"sync_confidence_threshold": 3},
            "run_affine": {"output_resolution": 256},
        },
    }
    """Video preprocessing pipeline manager."""

    def __init__(
        self,
        base_dir: str = "data",
        config_path: str = "src/configs/pipeline_config.yaml",
    ):
        """
        Initialize preprocessing pipeline with enhanced stability features.

        Args:
            base_dir (str): Base directory for all processing stages
            config_path (str): Path to the YAML configuration file
        """
        self.base_dir = base_dir
        self._load_config(config_path)

        # Initialize settings from config
        self.process_kill_timeout = self.config["pipeline"].get(
            "process_kill_timeout",
            self.DEFAULT_CONFIG["pipeline"]["process_kill_timeout"],
        )
        self.ffmpeg_timeout = self.config["pipeline"].get(
            "ffmpeg_timeout", self.DEFAULT_CONFIG["pipeline"]["ffmpeg_timeout"]
        )
        self.cuda_retry_count = self.config["pipeline"].get(
            "cuda_retry_count", self.DEFAULT_CONFIG["pipeline"]["cuda_retry_count"]
        )

        self.stages = [
            "0_RAW_VIDEOS",
            "1_GOOD",
            "2_SHOTS",
            "3_RESAMPLED",
            "4_DENOISED",
            "5_VALID",
            "6_SYNC_CORRECTION",
            "7_AFFINE",
            "8_QUALITY",
        ]

        # Track active processes for cleanup
        self._active_processes: Set[subprocess.Popen] = set()
        self._process_lock = threading.Lock()

        # Register cleanup handlers
        atexit.register(self._emergency_cleanup)

        # Create directory structure
        self._setup_directories()

        # Initialize ready flags directory
        self._ready_flags_dir = os.path.join(self.base_dir, ".ready_flags")
        os.makedirs(self._ready_flags_dir, exist_ok=True)

    def _load_config(self, config_path: str):
        """Load configuration from YAML file."""
        try:
            with open(config_path, "r") as f:
                self.config = yaml.safe_load(f)
            if self.config is None:  # Handle empty config file
                logger.warning(
                    f"Config file {config_path} is empty. Using default configuration."
                )
                self.config = self.DEFAULT_CONFIG
            else:
                logger.info(f"Successfully loaded configuration from {config_path}")
        except FileNotFoundError:
            logger.warning(
                f"Config file not found at {config_path}. Using default configuration."
            )
            self.config = self.DEFAULT_CONFIG
        except yaml.YAMLError as e:
            logger.error(
                f"Error parsing YAML config file {config_path}: {e}. Using default configuration."
            )
            self.config = self.DEFAULT_CONFIG
        except Exception as e:
            logger.error(
                f"Unexpected error loading config {config_path}: {e}. Using default configuration."
            )
            self.config = self.DEFAULT_CONFIG

        # Ensure top-level keys 'pipeline' and 'stages' exist, using defaults if necessary
        if "pipeline" not in self.config or not isinstance(
            self.config["pipeline"], dict
        ):
            logger.warning(
                "Missing or invalid 'pipeline' section in config. Merging with defaults."
            )
            self.config["pipeline"] = {
                **self.DEFAULT_CONFIG["pipeline"],
                **(self.config.get("pipeline", {})),
            }

        if "stages" not in self.config or not isinstance(self.config["stages"], dict):
            logger.warning(
                "Missing or invalid 'stages' section in config. Merging with defaults."
            )
            self.config["stages"] = {
                **self.DEFAULT_CONFIG["stages"],
                **(self.config.get("stages", {})),
            }

    def _setup_directories(self):
        """Create required directory structure."""
        for stage in self.stages:
            path = os.path.join(self.base_dir, stage)
            os.makedirs(path, exist_ok=True)
            logger.info(f"Created directory: {path}")

    def _get_stage_path(self, stage: str) -> str:
        """Get full path for a processing stage."""
        if stage not in self.stages:
            raise ValueError(f"Invalid stage: {stage}")
        return os.path.join(self.base_dir, stage)

    def _emergency_cleanup(self):
        """Emergency cleanup handler for process termination."""
        logger.warning("Emergency cleanup initiated")
        with self._process_lock:
            for process in self._active_processes:
                try:
                    process.kill()
                    process.wait(timeout=self.process_kill_timeout)
                except Exception as e:
                    logger.exception(f"Failed to kill process: {e}")
        logger.info("Emergency cleanup completed")

    def _track_process(self, process: subprocess.Popen):
        """Track subprocess for cleanup."""
        with self._process_lock:
            self._active_processes.add(process)

    def _untrack_process(self, process: subprocess.Popen):
        """Remove subprocess from tracking."""
        with self._process_lock:
            self._active_processes.discard(process)

    def _create_ready_flag(self, file_path: str) -> FileReadyFlag:
        """Create readiness flag for processed file."""
        checksum = hashlib.md5(Path(file_path).read_bytes()).hexdigest()
        timestamp = time.time()
        size = os.path.getsize(file_path)

        flag = FileReadyFlag(file_path, checksum, timestamp, size)
        flag_path = os.path.join(
            self._ready_flags_dir, f"{os.path.basename(file_path)}.ready"
        )

        with open(flag_path, "w") as f:
            f.write(f"{checksum}\n{timestamp}\n{size}")

        return flag

    def _verify_ready_flag(self, file_path: str) -> bool:
        """Verify file readiness and integrity."""
        flag_path = os.path.join(
            self._ready_flags_dir, f"{os.path.basename(file_path)}.ready"
        )

        if not os.path.exists(flag_path):
            return False

        try:
            with open(flag_path) as f:
                stored_checksum = f.readline().strip()
                stored_timestamp = float(f.readline().strip())
                stored_size = int(f.readline().strip())

            current_checksum = hashlib.md5(Path(file_path).read_bytes()).hexdigest()
            current_size = os.path.getsize(file_path)

            return stored_checksum == current_checksum and stored_size == current_size
        except Exception as e:
            logger.exception(f"Failed to verify ready flag: {e}")
            return False

    def _cleanup_resources(self):
        """Enhanced resource cleanup between pipeline stages."""
        # Force garbage collection
        gc.collect()

        # Enhanced CUDA cleanup with retries
        if TORCH_AVAILABLE and torch.cuda.is_available():
            for attempt in range(self.cuda_retry_count):
                try:
                    # Synchronize CUDA operations
                    torch.cuda.synchronize()
                    # Clear cache
                    torch.cuda.empty_cache()
                    # Reset peak memory stats
                    torch.cuda.reset_peak_memory_stats()
                    # Force IPC cleanup
                    torch.cuda.ipc_collect()

                    logger.debug(f"CUDA cleanup successful on attempt {attempt + 1}")
                    break
                except Exception as e:
                    logger.warning(f"CUDA cleanup attempt {attempt + 1} failed: {e}")
                    if attempt == self.cuda_retry_count - 1:
                        logger.error("All CUDA cleanup attempts failed")
                    time.sleep(2**attempt)  # Exponential backoff

        # Kill any lingering processes
        with self._process_lock:
            for process in list(self._active_processes):
                try:
                    if process.poll() is None:  # Process still running
                        process.terminate()
                        try:
                            process.wait(timeout=self.process_kill_timeout)
                        except subprocess.TimeoutExpired:
                            process.kill()
                            process.wait()
                    self._active_processes.remove(process)
                except Exception as e:
                    logger.exception(f"Failed to cleanup process: {e}")

        time.sleep(5)  # Reduced wait time, since we have better cleanup now
        logger.debug("Resource cleanup completed")

    def check_corruption(
        self, num_workers: Optional[int] = None
    ) -> Dict[str, Union[bool, str]]:
        """
        Check for corrupted videos and move good ones.

        Args:
            num_workers: Number of worker processes

        Returns:
            Dict mapping video paths to corruption status
        """
        input_dir = self._get_stage_path("0_RAW_VIDEOS")
        output_dir = self._get_stage_path("1_GOOD")

        logger.info("Checking for video corruption...")
        results = check_all_videos_corruption(
            input_dir=input_dir, output_dir=output_dir, num_workers=num_workers
        )

        return results

    def generate_shots(self, num_workers: Optional[int] = None):
        """Generate shots from good videos."""
        input_dir = self._get_stage_path("1_GOOD")
        output_dir = self._get_stage_path("2_SHOTS")

        logger.info("Generating video shots...")
        detect_shot_multiprocessing(
            input_dir=input_dir, output_dir=output_dir, num_workers=num_workers or 4
        )

    def filter_single_face(self, num_workers: Optional[int] = None):
        """Filter videos containing single face."""

        conf_threshold = (
            self.config["stages"]
            .get("filter_single_face", {})
            .get(
                "confidence_threshold",
                self.DEFAULT_CONFIG["stages"]["filter_single_face"][
                    "confidence_threshold"
                ],
            )
        )
        input_dir = self._get_stage_path("2_SHOTS")
        output_dir = self._get_stage_path("3_SINGLE_FACE")

        logger.info("Filtering for single face videos...")
        process_sf_videos_parallel(
            input_dir=input_dir,
            output_dir=output_dir,
            delete_multi_face=False,
            num_workers=num_workers,
            confidence_threshold=conf_threshold,
        )

    def resample_videos(self, num_workers: Optional[int] = None):
        """Resample videos to target FPS and audio sample rate."""
        input_dir = self._get_stage_path("2_SHOTS")
        output_dir = self._get_stage_path("3_RESAMPLED")

        logger.info("Resampling videos to 25 FPS and 16kHz audio...")
        results = resample_fps_hz_multiprocessing(
            input_dir=input_dir, output_dir=output_dir, num_workers=num_workers
        )

        # Log any failures
        errors = {k: v for k, v in results.items() if v is not None}
        if errors:
            logger.error("Failed to resample videos:")
            for path, error in errors.items():
                logger.error(f"{path}: {error}")

        # Get video durations after resampling
        logger.info("Getting resampled video durations...")
        durations = get_all_videos_durations(
            input_dir=output_dir, num_workers=num_workers
        )

        # Log duration results
        success_count = sum(1 for v in durations.values() if isinstance(v[1], float))
        logger.info(
            f"Successfully got durations for {success_count}/{len(durations)} videos"
        )

    def _denoise_single_video(self, video_path: str, output_dir: str) -> bool:
        """
        Denoise a single video by enhancing its audio with improved stability.

        Args:
            video_path (str): Path to input video
            output_dir (str): Output directory for denoised video

        Returns:
            bool: True if successful, False otherwise
        """
        temp_files = []
        ffmpeg_process = None
        output_path = os.path.join(output_dir, os.path.basename(video_path))

        try:
            # Extract audio from video
            audio_path = os.path.join(
                output_dir,
                f"{os.path.splitext(os.path.basename(video_path))[0]}_temp.wav",
            )
            temp_files.append(audio_path)
            extract_audio(video_path, audio_path)

            # Enhance audio with CUDA cleanup between operations
            enhanced_files = enhance(audio_path)
            if not enhanced_files:
                logger.error(f"No enhanced audio files produced for {video_path}")
                return False

            temp_files.extend(enhanced_files)
            self._cleanup_resources()  # CUDA cleanup after enhancement

            try:
                # Use the "No Crowd" track as the enhanced audio
                enhanced_audio = next(f for f in enhanced_files if "needed" in f)
            except StopIteration:
                logger.error(
                    f"No 'No Crowd' track found in enhanced files for {video_path}"
                )
                return False

            # BEWARE: This can cause AV sync issues if denoiser changes the duration of audio.
            # Merge enhanced audio with video using FFmpeg
            command = [
                "ffmpeg",
                "-y",
                "-i",
                video_path,  # Video input
                "-i",
                enhanced_audio,  # Enhanced audio input
                "-c:v",
                "copy",  # Copy video stream
                "-c:a",
                "aac",  # Re-encode audio to AAC
                "-map",
                "0:v:0",  # Use video from first input
                "-map",
                "1:a:0",  # Use audio from second input
                "-max_muxing_queue_size",
                "1024",  # Prevent muxing issues
                "-shortest",  # Stop when the shortest stream ends
                output_path,
            ]

            # Start FFmpeg process with timeout
            ffmpeg_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                start_new_session=True,  # Allow independent termination
            )

            # Track the process for cleanup
            self._track_process(ffmpeg_process)

            try:
                stdout, stderr = ffmpeg_process.communicate(timeout=self.ffmpeg_timeout)
                if ffmpeg_process.returncode != 0:
                    error_msg = stderr.decode() if stderr else "Unknown FFmpeg error"
                    raise subprocess.CalledProcessError(
                        ffmpeg_process.returncode, command, output=stdout, stderr=stderr
                    )
            except subprocess.TimeoutExpired:
                # Kill the process group
                os.killpg(os.getpgid(ffmpeg_process.pid), signal.SIGTERM)
                time.sleep(2)
                if ffmpeg_process.poll() is None:
                    os.killpg(os.getpgid(ffmpeg_process.pid), signal.SIGKILL)
                raise RuntimeError(
                    f"FFmpeg process timed out after {self.ffmpeg_timeout}s"
                )

            # Verify output
            if not os.path.exists(output_path) or os.path.getsize(output_path) == 0:
                raise RuntimeError("Failed to create output video or output is empty")

            # Create ready flag only after successful processing
            self._create_ready_flag(output_path)

            logger.info(f"Successfully created denoised video: {output_path}")
            return True

        except Exception as e:
            logger.exception(f"Error denoising video {video_path}: {e}")
            # Attempt to clean up partial output
            if os.path.exists(output_path):
                try:
                    os.remove(output_path)
                except OSError:
                    pass
            return False

        finally:
            # Always untrack and cleanup the FFmpeg process
            if ffmpeg_process:
                self._untrack_process(ffmpeg_process)
                if ffmpeg_process.poll() is None:
                    try:
                        os.killpg(os.getpgid(ffmpeg_process.pid), signal.SIGTERM)
                    except Exception as e:
                        pass

            # Cleanup any remaining temp files
            for f in temp_files:
                try:
                    if os.path.exists(f):
                        os.remove(f)
                        logger.debug(f"Cleaned up temporary file: {f}")
                except OSError as e:
                    logger.warning(f"Failed to remove temporary file {f}: {e}")

    def denoise_videos(self, num_workers: Optional[int] = None):
        """
        Denoise videos by enhancing their audio tracks.
        Process one video at a time to avoid CUDA memory issues.

        Args:
            num_workers: Not used, kept for interface consistency
        """
        input_dir = self._get_stage_path("3_RESAMPLED")
        output_dir = self._get_stage_path("4_DENOISED")

        # Get list of videos to process
        videos = [f for f in os.listdir(input_dir) if f.endswith((".mp4", ".avi"))]
        if not videos:
            logger.warning("No videos found for denoising")
            return

        logger.info(f"Denoising {len(videos)} videos...")

        # Process videos sequentially to avoid GPU memory issues
        success_count = 0
        for video in videos:
            try:
                if self._denoise_single_video(
                    os.path.join(input_dir, video), output_dir
                ):
                    success_count += 1
                    logger.info(f"Successfully denoised {video}")
                else:
                    logger.error(f"Failed to denoise {video}")
            except Exception as e:
                logger.exception(f"Error processing {video}: {e}")

        logger.info(
            f"Denoising complete. {success_count}/{len(videos)} videos processed successfully"
        )

    def run_asd(self, num_workers: Optional[int] = None):
        """
        Run Audio-Visual Synchronization Detection with enhanced stability.
        Uses existing parallel processing functionality from src/asd.py with
        improved error handling and resource management.
        """
        try:
            # Ensure TalkNet ASD model is available
            talknet_model_path = verify_and_download_model("pretrain_TalkSet")
            logger.info(f"Using TalkNet ASD model at: {talknet_model_path}")
        except Exception as e:
            logger.error(
                f"Failed to verify/download TalkNet ASD model: {e}. Skipping ASD."
            )
            return

        input_dir = self._get_stage_path("4_DENOISED")
        output_dir = self._get_stage_path("5_VALID")

        # Only process files that have readiness flags
        input_files = [
            f
            for f in os.listdir(input_dir)
            if f.endswith((".mp4", ".avi"))
            and self._verify_ready_flag(os.path.join(input_dir, f))
        ]

        if not input_files:
            logger.warning("No ready input files found for ASD validation")
            return

        logger.info(f"Running ASD validation on {len(input_files)} videos...")

        try:
            # Ensure clean state before starting
            self._cleanup_resources()
            # import torch
            # from concurrent.futures import ProcessPoolExecutor, as_completed
            # from functools import partial
            # import glob

            # Get number of available GPUs
            num_gpus = torch.cuda.device_count()
            if num_gpus == 0:
                logger.error("No CUDA-capable GPUs found for parallel processing")
                raise RuntimeError("No CUDA-capable GPUs found")

            # Get all video files
            video_extensions = [".mp4", ".avi", ".mov", ".mkv", ".wmv"]
            videos = []
            for ext in video_extensions:
                videos.extend(glob.glob(os.path.join(input_dir, f"*{ext}")))
            videos = sorted(videos)

            if not videos:
                logger.error(f"No videos found in directory: {input_dir}")
                raise ValueError(f"No videos found in {input_dir}")

            logger.info(f"Found {len(videos)} videos in {input_dir}")
            logger.info(f"Using {num_gpus} GPUs for parallel processing")

            # Create ProcessPoolExecutor with number of processes equal to number of GPUs
            results = {}
            with ProcessPoolExecutor(max_workers=num_gpus) as executor:
                # Process videos in parallel, assigning each to a GPU round-robin
                process_fn = partial(
                    segment_video_by_speech,
                    output_dir=output_dir,
                    debug=False,
                    cleanup=False,
                )
                future_to_video = {
                    executor.submit(process_fn, video_path): video_path
                    for video_path in videos
                }

                # Get results as they complete
                for future in as_completed(future_to_video):
                    video_path = future_to_video[future]
                    try:
                        results[video_path] = future.result()
                    except Exception as e:
                        logger.exception(f"Failed to process {video_path}: {e}")
                        results[video_path] = []

            # Cleanup between batches
            self._cleanup_resources()

            # Create ready flags for successful outputs
            for video, segments in results.items():
                if segments:  # Only mark as ready if validation passed
                    output_path = os.path.join(output_dir, os.path.basename(video))
                    if os.path.exists(output_path):
                        self._create_ready_flag(output_path)

            # Log final results
            total_segments = sum(len(segments) for segments in results.values())
            passed_videos = sum(1 for segments in results.values() if len(segments) > 0)

            logger.info(
                f"ASD validation complete: {passed_videos}/{len(results)} videos passed"
            )
            if total_segments > len(results):
                logger.info(
                    f"Created {total_segments} segments from multi-speaker videos"
                )

        except Exception as e:
            logger.exception(f"ASD validation failed: {e}")
            raise

        finally:
            # Ensure thorough cleanup
            self._cleanup_resources()

    def run_auto_asd(self, num_workers: Optional[int] = None):
        """
        Run Audio-Visual Synchronization Detection with enhanced stability.
        Uses existing parallel processing functionality from src/auto_asd.py with
        improved error handling and resource management.
        """
        input_dir = self._get_stage_path("4_DENOISED")
        output_dir = self._get_stage_path("5_VALID")

        # Only process files that have readiness flags
        input_files = [
            f
            for f in os.listdir(input_dir)
            if f.endswith((".mp4", ".avi"))
            and self._verify_ready_flag(os.path.join(input_dir, f))
        ]

        if not input_files:
            logger.warning("No ready input files found for ASD validation")
            return

        logger.info(f"Running ASD validation on {len(input_files)} videos...")

        try:
            num_gpus = torch.cuda.device_count()  # Assuming this method exists
            videos = [os.path.join(input_dir, f) for f in input_files]
            results = {}

            with ProcessPoolExecutor(max_workers=num_gpus) as executor:
                # Process videos in parallel, assigning each to a GPU round-robin
                process_fn = partial(
                    run_asd_pipeline, output_dir=output_dir, debug=False
                )

                future_to_video = {
                    executor.submit(process_fn, video_path): video_path
                    for video_path in videos
                }

                # Get results as they complete
                for future in as_completed(future_to_video):
                    video_path = future_to_video[future]
                    try:
                        results[video_path] = future.result()
                    except Exception as e:
                        logger.error(f"Failed to process {video_path}: {str(e)}")
                        results[video_path] = []

            # Cleanup between batches
            self._cleanup_resources()

            # Create ready flags for successful outputs
            for video, segments in results.items():
                if segments:  # Only mark as ready if validation passed
                    output_path = os.path.join(output_dir, os.path.basename(video))
                    if os.path.exists(output_path):
                        self._create_ready_flag(output_path)

            # Log final results
            total_segments = sum(len(segments) for segments in results.values())
            passed_videos = sum(1 for segments in results.values() if len(segments) > 0)

            logger.info(
                f"ASD validation complete: {passed_videos}/{len(results)} videos passed"
            )
            if total_segments > len(results):
                logger.info(
                    f"Created {total_segments} segments from multi-speaker videos"
                )

        except Exception as e:
            logger.error(f"ASD validation failed: {str(e)}")
            raise

        finally:
            # Ensure thorough cleanup
            self._cleanup_resources()

    def delete_short_videos(self, num_workers: Optional[int] = None):
        """
        Delete videos shorter than a specified duration.

        Args:
            num_workers: Number of worker processes
        """
        input_dir = self._get_stage_path("5_VALID")
        min_dur = (
            self.config["stages"]
            .get("delete_short_videos", {})
            .get(
                "min_duration_seconds",
                self.DEFAULT_CONFIG["stages"]["delete_short_videos"][
                    "min_duration_seconds"
                ],
            )
        )
        logger.info(f"Deleting videos shorter than {min_dur} seconds...")
        get_all_videos_durations(
            input_dir, delete=True, threshold=min_dur, num_workers=num_workers
        )

    def perform_sync_correction(self, num_workers: Optional[int] = None):
        input_dir = self._get_stage_path("5_VALID")
        output_dir = self._get_stage_path("6_SYNC_CORRECTION")

        temp_dir = os.path.join(self.base_dir, "sync_correction_temp_dir")

        logger.info("Sync-correcting videos...")

        _ = verify_and_download_model("syncnet_v2")
        sync_av_multi_gpus(
            input_dir,
            output_dir,
            temp_dir,
            num_workers=num_workers,
            sync_conf_threshold=self.config["stages"]["sync_correction"][
                "sync_confidence_threshold"
            ],
        )

        shutil.rmtree(temp_dir)

    def run_affine(self, num_workers: Optional[int] = None):
        """
        Run affine transformation on videos.

        Args:
            num_workers: Number of worker processes
        """
        num_workers = 1
        try:
            # Ensure Face Landmarker model is available (for planned MediaPipe integration)
            landmarker_model_path = verify_and_download_model("face_landmarker")
            logger.info(f"Using Face Landmarker model at: {landmarker_model_path}")
            # This path would then be passed to the affine transform logic when it's updated
        except Exception as e:
            logger.error(
                f"Failed to verify/download Face Landmarker model: {e}. Skipping affine transformation."
            )
            return

        input_dir = self._get_stage_path("6_SYNC_CORRECTION")
        output_dir = self._get_stage_path("7_AFFINE")
        temp_dir = "temp"  # This could also be moved to config if needed
        res = (
            self.config["stages"]
            .get("run_affine", {})
            .get(
                "output_resolution",
                self.DEFAULT_CONFIG["stages"]["run_affine"]["output_resolution"],
            )
        )

        logger.info("Running affine transformation on videos...")

        affine_transform_multi_gpus(input_dir, output_dir, temp_dir, res, num_workers)

    def visual_quality_check(self, num_workers: Optional[int] = None):
        """
        Filter videos based on visual quality scores.

        Args:
            num_workers: Number of worker processes
        """
        try:
            # Ensure quality model is available
            quality_model_path = verify_and_download_model("koniq_pretrained")
            logger.info(f"Using quality model at: {quality_model_path}")
        except Exception as e:
            logger.error(f"Failed to verify/download quality model: {e}. Skipping ASD.")
            return

        input_dir = self._get_stage_path("7_AFFINE")
        output_dir = self._get_stage_path("8_QUALITY")

        logger.info("Filtering videos based on visual quality...")
        filter_visual_quality_multi_gpus(input_dir, output_dir, num_workers)

    @timer_decorator
    def run_pipeline(
        self,
        start_stage: str = "corruption",
        end_stage: str = "quality",
        num_workers: Optional[int] = None,
    ):
        """
        Run preprocessing pipeline from start_stage to end_stage.

        Args:
            start_stage (str): Stage to start from
            end_stage (str): Stage to end at
            num_workers: Number of worker processes
        """
        if num_workers is None:
            num_workers = max(1, (mp.cpu_count() // 2))
        logger.info(
            f"Starting pipeline from {start_stage} to {end_stage} with {num_workers} workers"
        )

        # Map stage names to methods
        stage_map = {
            "corruption": self.check_corruption,
            "shots": self.generate_shots,
            "resample": self.resample_videos,
            "denoise": self.denoise_videos,
            "asd": self.run_auto_asd,
            "duration": self.delete_short_videos,
            "sync_correction": self.perform_sync_correction,
            "affine": self.run_affine,
            "quality": self.visual_quality_check,
        }

        # Get stage indices
        stages = list(stage_map.keys())
        try:
            start_idx = stages.index(start_stage)
            end_idx = stages.index(end_stage)
            if start_idx > end_idx:
                raise ValueError("Start stage cannot come after end stage")
        except ValueError as e:
            logger.exception(f"Invalid stage name: {e}")
            raise

        try:
            # Run selected pipeline stages
            for stage in stages[start_idx : end_idx + 1]:
                logger.info(f"Running stage: {stage}")
                stage_map[stage](num_workers=num_workers)
                # Clean up resources after each stage
                self._cleanup_resources()

            logger.info(
                f"Pipeline completed successfully ({start_stage} -> {end_stage})"
            )

        except Exception as e:
            logger.exception(f"Pipeline failed at stage {stage}: {e}")

            raise


def main():
    """CLI entry point"""
    import argparse

    from dotenv import load_dotenv

    load_dotenv(dotenv_path="scripts/.env", override=True)

    node_idx = os.getenv("NODE_INDEX", "0")

    parser = argparse.ArgumentParser(
        description="""Video preprocessing pipeline that:
        1. Checks for video corruption
        2. Generates video shots/scenes
        3. Filters for single face videos
        4. Resamples to 25 FPS and 16kHz audio
        5. Applies audio denoising and enhancement
        6. Runs audio-visual synchronization validation
        """
    )
    parser.add_argument(
        "-i", "--input-dir", required=True, help="Directory containing input videos"
    )
    parser.add_argument(
        "-b",
        "--base-dir",
        default="data",
        help="Base directory for processing stages (default: data)",
    )
    parser.add_argument(
        "-w",
        "--workers",
        type=int,
        help="Number of worker processes (default: CPU_COUNT//2)",
    )
    parser.add_argument(
        "-s",
        "--start-stage",
        choices=[
            "corruption",
            "shots",
            "resample",
            "denoise",
            "asd",
            "duration",
            "sync_correction",
            "affine",
            "quality",
        ],
        default=None,  # Was "corruption"
        help="Start pipeline from this stage (interactive prompt if not set)",
    )
    parser.add_argument(
        "-e",
        "--end-stage",
        choices=[
            "corruption",
            "shots",
            "resample",
            "denoise",
            "asd",
            "duration",
            "sync_correction",
            "affine",
            "quality",
        ],
        default=None,  # Was "quality"
        help="End pipeline at this stage (interactive prompt if not set)",
    )
    parser.add_argument(
        "--slack-webhook-url",
        type=str,
        default=None,
        help="Slack Webhook URL for notifications. If not provided, tries to use SLACK_WEBHOOK_URL from .env.",
    )

    args = parser.parse_args()

    # --- Define available stages (consistent with PreprocessPipeline.stage_map) ---
    PIPELINE_STAGES = [
        "corruption",
        "shots",
        "resample",
        "denoise",
        "asd",
        "duration",
        "sync_correction",
        "affine",
        "quality",
    ]

    console = Console()
    start_stage_cli = args.start_stage
    end_stage_cli = args.end_stage

    # --- Interactive CUI for stage selection if not provided via CLI ---
    if start_stage_cli is None:
        console.print("\n[bold cyan]Select Pipeline Start Stage:[/bold cyan]")
        # Display choices manually after prompt for clarity
        for i, stage_name in enumerate(PIPELINE_STAGES):
            console.print(f"  [green]{i + 1}[/green]. {stage_name}")
        start_stage_cli = Prompt.ask(
            "Enter the number of the start stage",
            choices=[str(i + 1) for i in range(len(PIPELINE_STAGES))],
            show_choices=False,  # We'll show them manually for better formatting
        )

        try:
            start_stage_cli = PIPELINE_STAGES[int(start_stage_cli) - 1]
        except (ValueError, IndexError):
            console.print("[bold red]Invalid selection. Exiting.[/bold red]")
            return 1
        logger.info(f"User selected start stage: {start_stage_cli}")

    if end_stage_cli is None:
        console.print("\n[bold cyan]Select Pipeline End Stage:[/bold cyan]")
        # Display choices before prompt for end stage
        for i, stage_name in enumerate(PIPELINE_STAGES):
            console.print(f"  [green]{i + 1}[/green]. {stage_name}")

        end_stage_cli = Prompt.ask(
            "Enter the number of the end stage",
            choices=[str(i + 1) for i in range(len(PIPELINE_STAGES))],
            show_choices=False,
        )
        try:
            end_stage_cli = PIPELINE_STAGES[int(end_stage_cli) - 1]
        except (ValueError, IndexError):
            console.print("[bold red]Invalid selection. Exiting.[/bold red]")
            return 1
        logger.info(f"User selected end stage: {end_stage_cli}")

    # --- Validate stage sequence ---
    try:
        start_idx = PIPELINE_STAGES.index(start_stage_cli)
        end_idx = PIPELINE_STAGES.index(end_stage_cli)
        if start_idx > end_idx:
            error_msg = f"Start stage '{start_stage_cli}' cannot come after end stage '{end_stage_cli}'."
            logger.error(error_msg)
            console.print(f"[bold red]Error: {error_msg} Exiting.[/bold red]")
            return 1
    except ValueError:
        # This should ideally not happen if choices are enforced by argparse/prompt
        logger.error(
            f"Invalid stage name provided: start='{start_stage_cli}', end='{end_stage_cli}'"
        )
        console.print("[bold red]Invalid stage name encountered. Exiting.[/bold red]")
        return 1

    # Update args with potentially CUI-selected values
    args.start_stage = start_stage_cli
    args.end_stage = end_stage_cli
    # --- End of CUI and validation logic ---

    # --- Copy videos from input_dir to 0_RAW_VIDEOS ---
    raw_videos_dir = Path(args.base_dir) / "0_RAW_VIDEOS"
    input_video_dir = Path(args.input_dir)

    if not input_video_dir.is_dir():
        logger.error(f"Input directory not found: {input_video_dir}")
        return 1  # Exit if input directory doesn't exist

    try:
        os.makedirs(raw_videos_dir, exist_ok=True)
        logger.info(f"Ensured raw videos directory exists: {raw_videos_dir}")

        video_extensions = (
            ".mp4",
            ".mov",
            ".avi",
            ".mkv",
            ".wmv",
        )  # Common video extensions
        copied_count = 0
        for video_file in input_video_dir.iterdir():
            if video_file.is_file() and video_file.name.lower().endswith(
                video_extensions
            ):
                try:
                    shutil.copy2(video_file, raw_videos_dir / video_file.name)
                    logger.info(f"Copied '{video_file.name}' to '{raw_videos_dir}'")
                    copied_count += 1
                except shutil.Error as e:
                    logger.error(f"Error copying file {video_file.name}: {e}")
                except Exception as e:
                    logger.error(
                        f"An unexpected error occurred while copying {video_file.name}: {e}"
                    )

        if copied_count == 0:
            logger.warning(f"No video files found or copied from {input_video_dir}")
        else:
            logger.info(
                f"Successfully copied {copied_count} video(s) to {raw_videos_dir}"
            )

    except OSError as e:
        logger.error(
            f"Error creating directory {raw_videos_dir} or accessing {input_video_dir}: {e}"
        )
        return 1  # Exit on directory creation or access error
    except Exception as e:
        logger.error(f"An unexpected error occurred during video copying: {e}")
        return 1
    # --- End of video copying logic ---

    try:
        pipeline = PreprocessPipeline(base_dir=args.base_dir)
        pipeline.run_pipeline(
            start_stage=args.start_stage,
            end_stage=args.end_stage,
            num_workers=args.workers,
        )
    except Exception as e:
        logger.exception(f"Error: {e}")
        # Determine Slack webhook URL
        slack_url = (
            args.slack_webhook_url
            if args.slack_webhook_url
            else os.getenv("SLACK_WEBHOOK_URL")
        )
        if slack_url:
            send_slack_notification(
                webhook_url=slack_url,
                success=False,
                message=f"Node: {node_idx}: Pipeline failed: {e}",
            )
        else:
            logger.warning(
                "Slack Webhook URL not configured. Skipping failure notification."
            )
        return 1

    # Send Slack notification on success
    slack_url = (
        args.slack_webhook_url
        if args.slack_webhook_url
        else os.getenv("SLACK_WEBHOOK_URL")
    )
    if slack_url:
        send_slack_notification(
            webhook_url=slack_url,
            success=True,
            message=f"Node: {node_idx}: Pipeline completed successfully ({args.start_stage} -> {args.end_stage})",
        )
    else:
        logger.info("Slack Webhook URL not configured. Skipping success notification.")

    return 0


if __name__ == "__main__":
    exit(main())
