# Architectural Decision Log

## Key Decisions
### 2025-03-15: Batch Processing Strategy
```mermaid
flowchart LR
    A[Single Item] --> B[Memory Spike Risk]
    C[Fixed Batch] --> D[Inflexible Scaling]
    E[Dynamic Batch] --> F[Selected Approach]
    F --> G[GPU Utilization Monitoring]
    F --> H[Auto-scaling Algorithm]
```

**Rationale**: Dynamic batch sizing based on GPU metrics reduces out-of-memory errors while maintaining throughput

### 2025-03-18: Face Detection Model Choice
| Option        | Accuracy | Speed  | Memory | Selected |
|---------------|----------|--------|--------|----------|
| S3FD          | 92%      | 18ms   | 1.2GB  | Yes      |
| MTCNN         | 89%      | 32ms   | 0.8GB  | No       |
| BlazeFace     | 85%      | 12ms   | 0.6GB  | No       |

**Justification**: Balanced accuracy and resource requirements for video processing context

### 2025-03-25: CUDA Multiprocessing in Audio Processing
```mermaid
flowchart LR
    A[Fork Issue] --> B[CUDA Conflict]
    B --> C[Spawn Solution]
    C --> D[Process Pool]
    D --> E[Isolated Pipeline]
```

**Context**: CUDA initialization in forked subprocesses was causing runtime errors during speaker diarization.

**Solution**:
- Implemented spawn-based multiprocessing context
- Encapsulated pipeline initialization and execution in isolated process
- Used process pool for managed resource cleanup
- Preserved CUDA device handling while ensuring thread safety

**Benefits**:
- Resolved CUDA re-initialization conflicts
- Improved stability in multi-process environments
- Maintained GPU acceleration capabilities
- Proper resource management

### 2025-05-09: CLI, Configuration, and Model Management Enhancements

**Decisions Made:**
-   **CLI Arguments:**
    -   Added `--input-dir` / `-i` (required) for specifying source video directory.
    -   Added shorthands for existing arguments (`-b` for `--base-dir`, `-w` for `--workers`, `-s` for `--start-stage`, `-e` for `--end-stage`).
    -   Modified `--start-stage` and `--end-stage` to default to `None`, triggering interactive CUI if not provided.
    -   Added optional `--slack-webhook-url` argument.
-   **Interactive CUI:**
    -   Implemented CUI using `rich` library for selecting start/end stages when not provided via CLI.
-   **Configuration Management:**
    -   Externalized hardcoded pipeline parameters (timeouts, thresholds, etc.) to `src/configs/pipeline_config.yaml`.
    -   `PreprocessPipeline` now loads this YAML configuration, with fallbacks to default values.
-   **Model Checkpoint Management:**
    -   Created `src/configs/models.yaml` to define model sources (Hugging Face Hub `repo_id`/`filename` or direct `url`), local paths, and optional checksums.
    -   Implemented `src/utils/model_utils.py` with `verify_and_download_model` function to:
        -   Parse `models.yaml`.
        -   Download models from Hugging Face Hub (using `huggingface_hub`) or direct URLs.
        -   Verify checksums if provided.
    -   Integrated calls to `verify_and_download_model` into relevant pipeline stages (`filter_single_face`, `run_asd`, `run_affine`).

**Rationale:**
-   Improve usability and flexibility of the script through enhanced CLI and interactive prompts.
-   Centralize configurations, making the pipeline easier to manage and adjust.
-   Automate model checkpoint acquisition, simplifying setup and ensuring correct model versions.
-   Maintain robustness with checksum verification and clear fallback mechanisms.

## Pending Decisions
1. Audio resampling synchronization method
2. Long-term temporary file retention policy
3. Cross-stage dependency management approach
4. Finalizing all model sources and checksums in `src/configs/models.yaml`.
5. Implementation strategy for deferred tasks: Rich progress bars and structured JSON logging.