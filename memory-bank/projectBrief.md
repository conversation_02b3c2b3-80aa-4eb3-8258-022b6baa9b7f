# HotDub-Preprocess Project Brief

## Project Overview
**Objective**: Robust video preprocessing pipeline for audio-visual synchronization detection  
**Key Features**:
- Corruption detection/repair
- Automatic shot generation
- Single face filtering
- FPS/Hz resampling
- Audio denoising with enhanced stability
- Audio-Visual Synchronization Detection (ASD)

## Core Objectives
1. Implement fault-tolerant media processing pipeline
2. Ensure GPU resource efficiency
3. Maintain process integrity across stages
4. Enable batch processing scalability

## Technical Specifications
### Hardware Requirements
- NVIDIA GPU with CUDA support
- 8GB+ GPU VRAM
- 16GB+ System RAM

### Software Stack
- Python 3.8+
- CUDA Toolkit 11.8+
- cuDNN 8.7+
- FFMPEG (latest stable)

### Key Dependencies
```python
audio-separator[gpu]
pyannote.audio  
onnxruntime-gpu
torch ecosystem
```

## Architecture Blueprint
![Pipeline Flow](docs/flow.md)

### Critical Components
1. **Resource Manager**: Handles CUDA memory cleanup and batch control
2. **Process Monitor**: Tracks FFmpeg processes and system resources
3. **Recovery System**: Manages partial outputs and temporary files
4. **ASD Engine**: Batch-based audio-visual synchronization detection

## Milestones
```mermaid
gantt
    title Project Timeline
    dateFormat  YYYY-MM-DD
    section Core Pipeline
    Corruption Handling       :done,    des1, 2025-03-01, 7d
    Shot Generation           :done,    des2, 2025-03-08, 5d  
    Face Filtering            :active,  des3, 2025-03-13, 3d
    Resampling                :         des4, 2025-03-16, 4d
    ASD Implementation        :         des5, 2025-03-20, 7d
    
    section Stability
    Memory Management         :         des6, 2025-03-15, 10d
    Error Recovery            :         des7, 2025-03-20, 14d