# Active Context - HotDub-Preprocess

## Current Focus Areas
```mermaid
flowchart TD
    A[Pipeline Stages] --> B[Face Filtering]
    A --> C[Resampling Implementation]
    A --> D[ASD Optimization]
    B --> E[Single Face Detection]
    C --> F[Frame Rate Conversion]
    D --> G[Batch Processing]
```

### Priority Tasks
1. Complete face filtering stability improvements
2. Implement adaptive resampling thresholds
3. Optimize ASD batch memory usage
4. Enhance process cleanup reliability
5. Implement Rich progress bars for long-running stages (Task I.4 from enhancement plan) - *Deferred*
6. Implement Structured JSON logging (Task II.4 from enhancement plan) - *Deferred*
7. Populate actual URLs/checksums/repo_ids in `src/configs/models.yaml`

## Open Questions
- Batch size optimization for varying GPU configurations
- Cross-stage dependency management
- Long-term temporary file retention policy
- Finalizing model sources (Hugging Face Hub vs. direct) for all checkpoints
- Strategy for updating `face_landmarker.task` checksum if it changes upstream

## Risk Assessment
```mermaid
pie title Current Risks
    "GPU Memory Fragmentation" : 35
    "FFmpeg Process Hangs" : 25
    "File Lock Contention" : 20
    "Batch Size Tuning" : 20
```

## Resource Allocation
| Resource        | Current Usage | Threshold |
|-----------------|---------------|-----------|
| GPU Memory      | 6.8/8 GB      | 90%       |
| System Memory   | 12/16 GB      | 75%       |
| Disk IO         | 45 MB/s       | 100 MB/s  |
| Batch Throughput| 3.2 items/sec | 5 items/sec |