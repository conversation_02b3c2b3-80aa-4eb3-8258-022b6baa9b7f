# Product Context - HotDub-Preprocess

## System Architecture
```mermaid
flowchart LR
    A[Input Media] --> B[Corruption Check]
    B --> C[Shot Generation]
    C --> D[Face Filtering]
    D --> E[Resampling]
    E --> F[ASD Processing]
    F --> G[Output Validation]
    
    subgraph Resource Management
        RM[Resource Manager]
        PM[Process Monitor]
        GC[Garbage Collector]
    end
    
    subgraph Error Handling
        EH[Error Recovery]
        FL[Failure Logger]
        TM[Temp File Manager]
    end
    
    B <--> RM
    D <--> PM
    F <--> EH
```

## Component Catalog

### Core Pipeline Components
1. **CorruptionHandler** (`src/remove_broken_videos.py`)
   - MD5 checksum verification
   - FFmpeg validation
   - Auto-repair capabilities

2. **ShotGenerator** (`src/generate_shots.py`)
   - Scene boundary detection
   - Adaptive thresholding
   - Batch processing

3. **FaceFilter** (`src/filter_single_face.py`)
   - S3FD face detection
   - Landmark analysis
   - Multi-frame validation

4. **Resampler** (`src/resample_fps_hz.py`)
   - Audio-video synchronization
   - Sample rate conversion
   - Quality preservation

### Infrastructure Components
1. **ResourceManager** (`src/utils/logger.py`)
   - CUDA memory monitoring
   - Process lifecycle management
   - Batch size adaptation

2. **RecoverySystem** (`src/utils/audio_utils.py`)
   - Partial output cleanup
   - State tracking
   - Retry mechanisms

## Dependency Map
```mermaid
erDiagram
    PREPROCESS_PY ||--|{ GENERATE_SHOTS : "Invokes"
    PREPROCESS_PY ||--|{ FILTER_SINGLE_FACE : "Manages"
    FILTER_SINGLE_FACE ||--|{ S3FD : "Uses model from"
    AUDIO_UTILS ||--|{ FFMPEG : "Wrapper for"
    TALKNET_WRAPPER ||--|{ ONNXRUNTIME : "Depends on"
    
    PREPROCESS_PY {
        string pipeline_stages
        int batch_size
    }
    GENERATE_SHOTS {
        float threshold
        int min_shot_duration
    }
    S3FD {
        string model_path
        float confidence
    }