# Architectural Patterns

## Pipeline Processing Pattern
```mermaid
flowchart LR
    Input --> Preprocessor
    Preprocessor --> Processor
    Processor --> Validator
    Validator --> Output
    
    subgraph Pattern Implementation
        pre[src/remove_broken_videos.py]
        proc[src/generate_shots.py]
        val[src/filter_single_face.py]
    end
```
**Implementation**: Sequential stage execution with strict phase separation  
**Example**: Media processing pipeline in `preprocess.py`

## Dynamic Batch Sizing
```mermaid
sequenceDiagram
    Participant ResourceMonitor
    Participant BatchManager
    Participant GPU
    
    ResourceMonitor->>GPU: Poll memory usage
    GPU-->>ResourceMonitor: Current utilization
    ResourceMonitor->>BatchManager: Adjust batch size
    BatchManager->>Processor: New batch config
```
**Implementation**: `src/utils/logger.py` memory monitoring logic  
**Strategy**: Adaptive scaling based on CUDA memory pressure

## Circuit Breaker Error Handling
```mermaid
stateDiagram-v2
    [*] --> Closed
    Closed --> Open: Error threshold exceeded
    Open --> HalfOpen: Timeout elapsed
    HalfOpen --> Open: Error occurs
    HalfOpen --> Closed: Success
```
**Implementation**: Retry mechanisms in `src/utils/audio_utils.py`  
**Thresholds**: 3 consecutive failures trip circuit

## Monitoring Triad
```mermaid
classDiagram
    class ResourceMonitor {
        +track_memory()
        +track_processes()
    }
    
    class PerformanceTracker {
        +throughput_metrics()
        +latency_stats()
    }
    
    class HealthChecker {
        +system_checks()
        +dependency_validation()
    }
    
    ResourceMonitor <-- PerformanceTracker
    ResourceMonitor <-- HealthChecker
```
**Implementation**: Distributed across `logger.py` and `preprocess.py`

## Temporary File Strategy
**Pattern**: Write-Ahead Temporary Files  
**Rules**:
1. All intermediate files use `.tmp` extension
2. Staged in `assets/resampled/tmp/`
3. Atomic moves on completion
4. Automatic cleanup after 24h

**Implementation**: `src/utils/video_utils.py` cleanup functions

## Externalized Configuration Pattern
**Pattern**: Centralized YAML Configuration
**Rules**:
1.  Pipeline operational parameters (timeouts, thresholds, default values) are stored in `src/configs/pipeline_config.yaml`.
2.  Model checkpoint details (source, local path, checksums) are stored in `src/configs/models.yaml`.
3.  Scripts load configurations at startup, with fallbacks to hardcoded defaults if files are missing or malformed.
**Benefits**:
-   Easy modification of parameters without code changes.
-   Clear separation of configuration from logic.
-   Versionable configuration files.
**Implementation**: `PreprocessPipeline.__init__` in `preprocess.py` loads `pipeline_config.yaml`. `src/utils/model_utils.py` loads `models.yaml`.

## Automated Model Checkpoint Management
**Pattern**: On-Demand Download and Verification
**Rules**:
1.  Model requirements are defined in `src/configs/models.yaml` (source: Hugging Face Hub or direct URL).
2.  Before a model-dependent stage runs, `src/utils/model_utils.py:verify_and_download_model` is called.
3.  The utility checks for local model existence.
4.  If a checksum is provided in `models.yaml`, it's verified.
5.  If the model is missing or checksum fails, it's downloaded from the specified source.
6.  Hugging Face Hub (`hf_hub_download`) is preferred; direct download via `requests` is a fallback.
**Benefits**:
-   Simplifies initial setup for users.
-   Ensures necessary model files are present and (optionally) verified.
-   Reduces the need to bundle large model files with the codebase.
**Implementation**: `src/utils/model_utils.py` and integration calls in `preprocess.py` stage methods.