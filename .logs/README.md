# HotDub-Preprocess Logs Directory

This directory contains log files generated by the HotDub-Preprocess application.

## Log Files

- `application.log`: Main log file containing all application logs
- `application.log.1`, `application.log.2`, etc.: Rotated log files when the main log exceeds size limits

## Log Format

Log entries follow this format:
```
TIMESTAMP | PROCESS_ID | LOG_LEVEL | MODULE:LINE_NUMBER - MESSAGE
```

Example:
```
2025-03-19 14:08:29,651 | 155978 | INFO | test_logging:47 - Checking for log directory
```

## Log Levels

The following log levels are used:
- `DEBUG`: Detailed information, typically useful only for diagnosing problems
- `INFO`: Confirmation that things are working as expected
- `WARNING`: Indication that something unexpected happened, but the application is still working
- `ERROR`: Due to a more serious problem, the application has not been able to perform a function
- `CRITICAL`: A serious error indicating that the program itself may be unable to continue running

## Configuration

Logs are configured with:
- File rotation at 10MB
- Maximum of 5 backup files
- Process-safe logging for multiprocessing support
- Console output for INFO level and above
- File logging for DEBUG level and above

## Notes

- This directory is created automatically by the application if it doesn't exist
- Log files may contain sensitive information and should be handled accordingly
- For debugging purposes, check these logs first when encountering issues